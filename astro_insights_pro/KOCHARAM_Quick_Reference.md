# KOCHARAM Filter Quick Reference Guide

## Quick Formula Summary

### Basic Transit Calculation
```
Days to Transit = Angular Distance ÷ Daily Motion
Where:
- Angular Distance = (Target° - Current°) % 360
- Daily Motion = Planet-specific motion rate
```

### Real Ephemeris Verification
```
1. Get current position: get_real_planet_position_for_date(planet, start_date)
2. Search target house: day-by-day ephemeris check
3. Verify prediction: cross-check with D1 chart
4. Return result: VERIFIED or ESTIMATED accuracy
```

## Planet Daily Motion Rates
| Planet  | Daily Motion | Transit Period |
|---------|-------------|----------------|
| Sun     | ~1.0°/day   | 30 days/house  |
| Moon    | ~13.2°/day  | 2.3 days/house |
| Mars    | ~0.5°/day   | 60 days/house  |
| Mercury | ~1.4°/day   | 21 days/house  |
| Jupiter | ~0.08°/day  | 1 year/house   |
| Venus   | ~1.6°/day   | 19 days/house  |
| Saturn  | ~0.03°/day  | 2.5 years/house|

## House System Mapping Example
```json
{
  "1": "Kanni",    "7": "Meenam",
  "2": "Thulam",   "8": "<PERSON><PERSON>", 
  "3": "Virichigam", "9": "Rishabam",
  "4": "Dhanusu",  "10": "Midunam",
  "5": "MAGARAM",  "11": "Kadagam",
  "6": "Kumbam",   "12": "Simmam"
}
```

## API Usage Examples

### Basic KOCHARAM Query
```json
{
  "query": "JUPITER Bhukti_Dates AND KOCHARAM_FILTER(JUPITER in 7th_House)"
}
```

### Multiple Conditions
```json
{
  "query": "VENUS Bhukti_Dates AND KOCHARAM_FILTER(VENUS in 5th_House OR VENUS in 7th_House)"
}
```

## Accuracy Indicators
- **VERIFIED**: Real ephemeris confirmed ✅
- **ESTIMATED**: Mathematical calculation ⚠️
- **ERROR**: Calculation failed ❌

## Common Use Cases
1. **Marriage Timing**: Venus in 7th house
2. **Career Growth**: Jupiter in 10th house  
3. **Financial Gains**: Jupiter in 2nd/11th house
4. **Health Issues**: Mars in 6th/8th house
5. **Education**: Mercury in 4th/5th house

## Real Examples

### Example 1: Marriage Timing
```json
{
  "query": "VENUS Bhukti_Dates AND KOCHARAM_FILTER(JUPITER in 7th_House)",
  "result": {
    "predicted_date": "1987-02-12",
    "accuracy_level": "VERIFIED",
    "planet_predicted_longitude": 332.07,
    "calculation_method": "Real ephemeris verification"
  }
}
```

### Example 2: Career Growth
```json
{
  "query": "JUPITER Bhukti_Dates AND KOCHARAM_FILTER(MARS in 10th_House)",
  "result": {
    "predicted_date": "1990-07-20",
    "status": "Planet already in target house",
    "accuracy_level": "VERIFIED"
  }
}
```

### Example 3: Multiple Conditions
```json
{
  "query": "MERCURY Bhukti_Dates AND KOCHARAM_FILTER(VENUS in 5th_House OR JUPITER in 7th_House)",
  "result": {
    "conditions_met": "OR logic - either condition satisfies",
    "periods_passed": 8,
    "success_rate": 66.7
  }
}
```

## Step-by-Step Calculation Example

### Jupiter Transit to 7th House
1. **Current Position**: Jupiter at 36.37° (Rishabam/9th house)
2. **Target House**: 7th house = Meenam (330°-360°)
3. **Distance**: (330° - 36.37°) = 293.63°
4. **Daily Motion**: 0.08°/day (Jupiter)
5. **Estimated Days**: 293.63° ÷ 0.08° = 3670 days
6. **Real Search**: Day-by-day ephemeris verification
7. **Found Date**: 1987-02-12 (3826 days actual)
8. **Verification**: ✅ Jupiter at 332.07° in Meenam

## Troubleshooting
- **No results**: Check planet name spelling
- **Wrong dates**: Verify user house system
- **Slow response**: Large date ranges take time
- **Accuracy issues**: Use VERIFIED results only

## Quick Test Commands

### Test Basic KOCHARAM
```bash
curl -X POST http://localhost:5003/api/rule-engine/ \
  -H "Content-Type: application/json" \
  -d '{"user_profile_id": 1, "member_profile_id": 1, "query": "JUPITER Bhukti_Dates AND KOCHARAM_FILTER(JUPITER in 7th_House)", "chart_type": "D1"}'
```

### Test Multiple Planets
```bash
curl -X POST http://localhost:5003/api/rule-engine/ \
  -H "Content-Type: application/json" \
  -d '{"user_profile_id": 1, "member_profile_id": 1, "query": "VENUS Bhukti_Dates AND KOCHARAM_FILTER(VENUS in 5th_House OR JUPITER in 7th_House)", "chart_type": "D1"}'
```

---
*For detailed formulas and implementation, see KOCHARAM_Filter_Formula.md*
