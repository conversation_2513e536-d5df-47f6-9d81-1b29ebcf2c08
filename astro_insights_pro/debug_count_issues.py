"""
Debug Count Issues

This script debugs why the counts don't match the expected values.

Version: 2.0
Author: Fortune Lens Team
Last Updated: 2025-07-31
"""

import sys
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens')

from astro_insights_pro.app.extensions import mongo
from astro_insights_pro.app import create_app
from astro_insights_pro.app.services.rule_engine.api.main_rule_engine import _fetch_chart_data_from_mongodb

def debug_venus_periods():
    """Debug all VENUS periods in the database"""
    
    print("🔍 Debugging All VENUS Periods in Database")
    print("=" * 50)
    
    try:
        # Get chart data
        chart_data = _fetch_chart_data_from_mongodb(100001, 1, "D1")
        
        if chart_data and 'chart_data' in chart_data:
            d1_data = chart_data['chart_data']['D1']
            dashas = d1_data.get('dashas', [])
            
            print(f"✅ Found {len(dashas)} total dasha periods")
            
            # Analyze all VENUS periods
            venus_periods = []
            venus_bhukti_only = []
            venus_dasa_only = []
            
            for dasha in dashas:
                maha = dasha.get('maha_dasha', '').upper()
                bhukti = dasha.get('bhukti_dasha', '').upper()
                start_date = dasha.get('start_date', '')
                
                # Check if it's a VENUS period
                if maha == 'VENUS' or bhukti == 'VENUS':
                    venus_periods.append(dasha)
                    
                    # Categorize
                    if bhukti == 'VENUS' and maha != 'VENUS':
                        venus_bhukti_only.append(dasha)
                    elif maha == 'VENUS' and bhukti != 'VENUS':
                        venus_dasa_only.append(dasha)
                    elif maha == 'VENUS' and bhukti == 'VENUS':
                        print(f"   🎯 VENUS-VENUS period: {start_date[:10]}")
            
            print(f"\n📊 VENUS Period Analysis:")
            print(f"   Total VENUS periods: {len(venus_periods)}")
            print(f"   VENUS as Bhukti only: {len(venus_bhukti_only)}")
            print(f"   VENUS as Dasa only: {len(venus_dasa_only)}")
            
            print(f"\n📋 All VENUS Periods:")
            for i, period in enumerate(venus_periods, 1):
                maha = period.get('maha_dasha', '?')
                bhukti = period.get('bhukti_dasha', '?')
                start_date = period.get('start_date', '?')[:10]
                
                # Determine type
                if maha == bhukti:
                    period_type = "MAIN_DASA"
                elif bhukti == 'VENUS':
                    period_type = "BHUKTI"
                elif maha == 'VENUS':
                    period_type = "DASA_SUB"
                else:
                    period_type = "OTHER"
                
                print(f"   {i:2d}. {maha}-{bhukti} ({period_type}) - {start_date}")
            
            print(f"\n🎯 Expected Counts:")
            print(f"   Dasa_Dates (main dasa only): Should be 1")
            print(f"   Bhukti_Dates (bhukti only): Should be 9") 
            print(f"   Dasa_Bhukti_Dates (combined): Should be 17")
            
            print(f"\n🔍 Actual Counts from Raw Data:")
            main_dasa_count = len([p for p in venus_periods if p.get('maha_dasha', '').upper() == 'VENUS' and p.get('maha_dasha', '').upper() == p.get('bhukti_dasha', '').upper()])
            bhukti_only_count = len([p for p in venus_periods if p.get('bhukti_dasha', '').upper() == 'VENUS' and p.get('maha_dasha', '').upper() != 'VENUS'])
            dasa_sub_count = len([p for p in venus_periods if p.get('maha_dasha', '').upper() == 'VENUS' and p.get('bhukti_dasha', '').upper() != 'VENUS'])
            
            print(f"   Main VENUS-VENUS dasa: {main_dasa_count}")
            print(f"   VENUS bhukti periods: {bhukti_only_count}")
            print(f"   VENUS dasa sub-periods: {dasa_sub_count}")
            print(f"   Total: {main_dasa_count + bhukti_only_count + dasa_sub_count}")
            
            # Check for the missing periods
            print(f"\n🔍 Missing Period Analysis:")
            if bhukti_only_count < 9:
                print(f"   Missing {9 - bhukti_only_count} bhukti periods")
            if main_dasa_count + bhukti_only_count + dasa_sub_count < 17:
                missing = 17 - (main_dasa_count + bhukti_only_count + dasa_sub_count)
                print(f"   Missing {missing} total periods for Dasa_Bhukti_Dates")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


def debug_processor_filtering():
    """Debug the processor filtering logic"""
    
    print(f"\n🔍 Debugging Processor Filtering Logic")
    print("=" * 45)
    
    try:
        from astro_insights_pro.app.services.rule_engine.processors.dasha_processor import DashaProcessor
        
        processor = DashaProcessor()
        chart_data = _fetch_chart_data_from_mongodb(100001, 1, "D1")
        
        if chart_data:
            print(f"✅ Testing processor methods:")
            
            # Test bhukti periods
            bhukti_periods = processor._get_planet_bhukti_periods("VENUS", chart_data)
            print(f"   Bhukti periods: {len(bhukti_periods)}")
            
            # Test dasa periods
            dasa_periods = processor._get_planet_dasa_periods("VENUS", chart_data)
            print(f"   Dasa periods: {len(dasa_periods)}")
            
            # Show details
            print(f"\n📋 Bhukti Periods Found:")
            for i, period in enumerate(bhukti_periods, 1):
                maha = period.get('maha_dasha', '?')
                bhukti = period.get('bhukti_dasha', '?')
                start_date = period.get('start_date', '?')[:10]
                print(f"   {i}. {maha}-{bhukti} - {start_date}")
            
            print(f"\n📋 Dasa Periods Found:")
            for i, period in enumerate(dasa_periods, 1):
                maha = period.get('maha_dasha', '?')
                bhukti = period.get('bhukti_dasha', '?')
                start_date = period.get('start_date', '?')[:10]
                print(f"   {i}. {maha}-{bhukti} - {start_date}")
            
            # Test deduplication
            combined = bhukti_periods + dasa_periods
            deduplicated = processor._remove_duplicate_dasha_periods(combined)
            
            print(f"\n🔄 Deduplication Test:")
            print(f"   Before: {len(combined)} periods")
            print(f"   After: {len(deduplicated)} periods")
            print(f"   Removed: {len(combined) - len(deduplicated)} duplicates")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🔍 Count Issues Debug Suite")
    print("=" * 50)
    
    # Create app context
    app = create_app()
    
    with app.app_context():
        debug_venus_periods()
        debug_processor_filtering()
    
    print(f"\n🏁 Count Issues Debug Complete")
    print(f"   This should help identify why counts don't match expectations.")
