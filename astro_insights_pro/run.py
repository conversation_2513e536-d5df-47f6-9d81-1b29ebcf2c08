#!/usr/bin/env python3
"""
Astro Insights Pro - Application Runner

Professional astrological analysis platform entry point.
This module serves as the main entry point for the Astro Insights Pro application.
It creates and configures the Flask application instance and starts the server.

Author: Astro Insights Pro Team
Version: 1.0.0
License: MIT
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import create_app

def initialize_application():
    """
    Initialize and configure the Astro Insights Pro application.

    Creates the Flask application instance using the application factory pattern
    with proper configuration management and error handling.

    Returns:
        Flask: Configured Flask application instance
    """
    try:
        # Load environment variables from .env file
        load_dotenv()

        # Get configuration from environment variable
        config_name = os.getenv('FLASK_ENV', 'development')

        # Create the Flask application instance
        application = create_app(config_name)

        return application

    except Exception as error:
        print(f"❌ Failed to initialize application: {error}")
        sys.exit(1)

def start_server():
    """
    Start the Astro Insights Pro development server.

    Configures and starts the Flask development server with appropriate
    settings for development and production environments.
    """
    try:
        # Initialize the application
        app = initialize_application()

        # Get server configuration
        host = os.getenv('HOST', '0.0.0.0')
        port = int(os.getenv('PORT', 5003))
        debug = os.getenv('FLASK_ENV', 'development') == 'development'

        print(f"🚀 Starting Astro Insights Pro server...")
        print(f"📍 Server URL: http://{host}:{port}")
        print(f"🔧 Debug mode: {debug}")
        print(f"📚 API Documentation: http://{host}:{port}/api/docs")

        # Start the server
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )

    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as error:
        print(f"❌ Server error: {error}")
        sys.exit(1)

def main():
    """
    Main entry point for the Astro Insights Pro application.
    """
    print("=" * 60)
    print("🌟 Astro Insights Pro - Professional Astrological Platform")
    print("=" * 60)

    start_server()

if __name__ == '__main__':
    main()
