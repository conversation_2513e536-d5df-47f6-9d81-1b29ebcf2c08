"""
Debug Dasha Filtering

This script debugs why VENUS periods are not being found
even though 90 dasha periods exist.

Version: 2.0
Author: Fortune Lens Team
Last Updated: 2025-07-31
"""

import sys
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens')

from astro_insights_pro.app.extensions import mongo
from astro_insights_pro.app import create_app
from astro_insights_pro.app.services.rule_engine.api.main_rule_engine import _fetch_chart_data_from_mongodb

def debug_dasha_filtering():
    """Debug why VENUS periods are not being found"""
    
    print("🔍 Debugging VENUS Dasha Filtering")
    print("=" * 40)
    
    try:
        # Get chart data
        chart_data = _fetch_chart_data_from_mongodb(100001, 1, "D1")
        
        if chart_data and 'chart_data' in chart_data:
            d1_data = chart_data['chart_data']['D1']
            dashas = d1_data.get('dashas', [])
            
            print(f"✅ Found {len(dashas)} total dasha periods")
            
            # Analyze all dashas
            venus_bhukti = []
            venus_dasa = []
            all_planets = set()
            
            for i, dasha in enumerate(dashas):
                maha = dasha.get('maha_dasha', '').upper()
                bhukti = dasha.get('bhukti_dasha', '').upper()
                dasha_type = dasha.get('dasha_type', '')
                
                all_planets.add(maha)
                all_planets.add(bhukti)
                
                # Check for VENUS
                if bhukti == 'VENUS':
                    venus_bhukti.append(dasha)
                    print(f"   VENUS Bhukti {len(venus_bhukti)}: {maha}-{bhukti} ({dasha_type}) - {dasha.get('start_date', '?')[:10]}")
                
                if maha == 'VENUS':
                    venus_dasa.append(dasha)
                    print(f"   VENUS Dasa {len(venus_dasa)}: {maha}-{bhukti} ({dasha_type}) - {dasha.get('start_date', '?')[:10]}")
            
            print(f"\n📊 Analysis Results:")
            print(f"   Total VENUS Bhukti periods: {len(venus_bhukti)}")
            print(f"   Total VENUS Dasa periods: {len(venus_dasa)}")
            print(f"   Total VENUS periods: {len(venus_bhukti) + len(venus_dasa)}")
            
            print(f"\n🌟 All Planets Found:")
            sorted_planets = sorted(list(all_planets))
            print(f"   {sorted_planets}")
            
            # Show sample periods for debugging
            print(f"\n📋 Sample Dasha Periods:")
            for i, dasha in enumerate(dashas[:5], 1):
                print(f"   {i}. {dasha.get('maha_dasha', '?')}-{dasha.get('bhukti_dasha', '?')} ({dasha.get('dasha_type', '?')})")
                print(f"      {dasha.get('start_date', '?')} to {dasha.get('end_date', '?')}")
            
            # Test the processor logic manually
            print(f"\n🧪 Testing Processor Logic:")
            
            # Test bhukti filtering
            bhukti_matches = [d for d in dashas if d.get('bhukti_dasha', '').upper() == 'VENUS']
            print(f"   Manual bhukti filter: {len(bhukti_matches)} matches")
            
            # Test dasa filtering  
            dasa_matches = [d for d in dashas if d.get('maha_dasha', '').upper() == 'VENUS']
            print(f"   Manual dasa filter: {len(dasa_matches)} matches")
            
            # Test combined
            combined_matches = bhukti_matches + dasa_matches
            print(f"   Combined matches: {len(combined_matches)}")
            
            if combined_matches:
                print(f"   ✅ VENUS periods exist in data!")
                print(f"   Sample match: {combined_matches[0]}")
            else:
                print(f"   ❌ No VENUS periods found")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


def debug_processor_methods():
    """Debug the processor methods directly"""
    
    print(f"\n🔍 Debugging Processor Methods")
    print("=" * 35)
    
    try:
        from astro_insights_pro.app.services.rule_engine.processors.dasha_processor import DashaProcessor
        
        processor = DashaProcessor()
        chart_data = _fetch_chart_data_from_mongodb(100001, 1, "D1")
        
        if chart_data:
            print(f"✅ Chart data available")
            
            # Debug _get_planet_bhukti_periods
            print(f"\n🔍 Testing _get_planet_bhukti_periods:")
            
            # Check the method logic
            if 'chart_data' in chart_data and 'D1' in chart_data['chart_data']:
                d1_data = chart_data['chart_data']['D1']
                if 'dashas' in d1_data:
                    dashas = d1_data['dashas']
                    print(f"   Dashas available: {len(dashas)}")
                    
                    # Manual filtering
                    venus_bhukti = []
                    for dasha_entry in dashas:
                        if isinstance(dasha_entry, dict) and dasha_entry.get('bhukti_dasha') == 'VENUS':
                            venus_bhukti.append(dasha_entry)
                    
                    print(f"   Manual VENUS bhukti filter: {len(venus_bhukti)}")
                    
                    # Test with uppercase
                    venus_bhukti_upper = []
                    for dasha_entry in dashas:
                        if isinstance(dasha_entry, dict) and dasha_entry.get('bhukti_dasha', '').upper() == 'VENUS':
                            venus_bhukti_upper.append(dasha_entry)
                    
                    print(f"   Manual VENUS bhukti filter (uppercase): {len(venus_bhukti_upper)}")
                    
                    if venus_bhukti_upper:
                        print(f"   Sample VENUS bhukti: {venus_bhukti_upper[0]}")
            
            # Test the actual processor method
            venus_periods = processor._get_planet_bhukti_periods("VENUS", chart_data)
            print(f"   Processor method result: {len(venus_periods)}")
            
            # Test dasa periods
            venus_dasa = processor._get_planet_dasa_periods("VENUS", chart_data)
            print(f"   Processor dasa result: {len(venus_dasa)}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🔍 Dasha Filtering Debug Suite")
    print("=" * 50)
    
    # Create app context
    app = create_app()
    
    with app.app_context():
        debug_dasha_filtering()
        debug_processor_methods()
    
    print(f"\n🏁 Dasha Filtering Debug Complete")
    print(f"   This should identify why VENUS periods aren't being found.")
