"""
Debug Processor Data Path

This script debugs the exact data path that the processor is using
to access dasha data.

Version: 2.0
Author: Fortune Lens Team
Last Updated: 2025-07-31
"""

import sys
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens')

from astro_insights_pro.app.extensions import mongo
from astro_insights_pro.app import create_app
from astro_insights_pro.app.services.rule_engine.api.main_rule_engine import _fetch_chart_data_from_mongodb

def debug_processor_data_path():
    """Debug the exact data path the processor uses"""
    
    print("🔍 Debugging Processor Data Path")
    print("=" * 40)
    
    try:
        # Get chart data exactly as processor receives it
        chart_data = _fetch_chart_data_from_mongodb(100001, 1, "D1")
        
        print(f"✅ Chart data structure:")
        print(f"   Top level keys: {list(chart_data.keys())}")
        
        # Follow the exact path the processor uses
        print(f"\n🔍 Following processor path:")
        
        # Step 1: Check 'chart_data' in chart_data
        if 'chart_data' in chart_data:
            chart_info = chart_data['chart_data']
            print(f"   ✅ chart_data exists: {list(chart_info.keys())}")
            
            # Step 2: Check 'd1' in chart_data['chart_data']
            if 'd1' in chart_info:
                d1_data = chart_info['d1']
                print(f"   ✅ d1 exists: {list(d1_data.keys())}")
                
                # Step 3: Check 'dashas' in d1_data
                if 'dashas' in d1_data:
                    dashas = d1_data['dashas']
                    print(f"   ✅ dashas exists: {type(dashas)} with {len(dashas)} items")
                    
                    # Test the exact filtering logic
                    print(f"\n🧪 Testing exact processor logic:")
                    
                    # Simulate _get_planet_bhukti_periods logic
                    periods = []
                    planet_name = "VENUS"
                    
                    for dasha_entry in dashas:
                        if isinstance(dasha_entry, dict):
                            bhukti_dasha = dasha_entry.get('bhukti_dasha', '')
                            print(f"      Checking: bhukti_dasha='{bhukti_dasha}' vs planet='{planet_name}'")
                            
                            if bhukti_dasha.upper() == planet_name.upper():
                                print(f"      ✅ MATCH FOUND: {dasha_entry.get('maha_dasha', '?')}-{bhukti_dasha}")
                                periods.append(dasha_entry)
                            
                            # Only show first 5 to avoid spam
                            if len(periods) >= 5:
                                break
                    
                    print(f"   Manual filtering result: {len(periods)} periods")
                    
                    # Test all VENUS periods
                    all_venus_bhukti = []
                    all_venus_dasa = []
                    
                    for dasha_entry in dashas:
                        if isinstance(dasha_entry, dict):
                            bhukti = dasha_entry.get('bhukti_dasha', '').upper()
                            maha = dasha_entry.get('maha_dasha', '').upper()
                            
                            if bhukti == 'VENUS':
                                all_venus_bhukti.append(dasha_entry)
                            if maha == 'VENUS':
                                all_venus_dasa.append(dasha_entry)
                    
                    print(f"   All VENUS bhukti: {len(all_venus_bhukti)}")
                    print(f"   All VENUS dasa: {len(all_venus_dasa)}")
                    
                else:
                    print(f"   ❌ No 'dashas' in d1_data")
                    print(f"   d1_data keys: {list(d1_data.keys())}")
            else:
                print(f"   ❌ No 'd1' in chart_data")
                print(f"   chart_data keys: {list(chart_info.keys())}")
                
                # Check if it's uppercase 'D1'
                if 'D1' in chart_info:
                    print(f"   ⚠️ Found 'D1' (uppercase) instead of 'd1'")
                    d1_data = chart_info['D1']
                    print(f"   D1 keys: {list(d1_data.keys())}")
                    
                    if 'dashas' in d1_data:
                        dashas = d1_data['dashas']
                        print(f"   ✅ dashas in D1: {len(dashas)} items")
        else:
            print(f"   ❌ No 'chart_data' in chart_data")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


def debug_processor_method_directly():
    """Debug the processor method by calling it directly with debug prints"""
    
    print(f"\n🔍 Debugging Processor Method Directly")
    print("=" * 45)
    
    try:
        from astro_insights_pro.app.services.rule_engine.processors.dasha_processor import DashaProcessor
        
        # Create processor
        processor = DashaProcessor()
        
        # Get chart data
        chart_data = _fetch_chart_data_from_mongodb(100001, 1, "D1")
        
        # Add debug prints to understand what's happening
        print(f"✅ Calling _get_planet_bhukti_periods with:")
        print(f"   planet_name: 'VENUS'")
        print(f"   chart_data type: {type(chart_data)}")
        print(f"   chart_data keys: {list(chart_data.keys()) if isinstance(chart_data, dict) else 'Not a dict'}")
        
        # Call the method
        result = processor._get_planet_bhukti_periods("VENUS", chart_data)
        print(f"   Result: {len(result)} periods")
        
        if result:
            print(f"   Sample result: {result[0]}")
        
        # Test dasa method too
        dasa_result = processor._get_planet_dasa_periods("VENUS", chart_data)
        print(f"   Dasa result: {len(dasa_result)} periods")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🔍 Processor Data Path Debug Suite")
    print("=" * 50)
    
    # Create app context
    app = create_app()
    
    with app.app_context():
        debug_processor_data_path()
        debug_processor_method_directly()
    
    print(f"\n🏁 Processor Data Path Debug Complete")
    print(f"   This should identify the exact issue in the data path.")
