"""
Custom Exceptions
"""


class APIError(Exception):
    """Base API Error"""
    def __init__(self, message, code=400):
        self.message = message
        self.code = code
        super().__init__(self.message)


class ValidationError(APIError):
    """Validation Error"""
    def __init__(self, errors):
        self.errors = errors
        message = "Validation Error"
        super().__init__(message, 400)


class ResourceNotFoundError(APIError):
    """Resource Not Found Error"""
    def __init__(self, resource="Resource"):
        message = f"{resource} not found"
        super().__init__(message, 404)


class AuthenticationError(APIError):
    """Authentication Error"""
    def __init__(self, message="Authentication failed"):
        super().__init__(message, 401)


class AuthorizationError(APIError):
    """Authorization Error"""
    def __init__(self, message="You don't have permission to access this resource"):
        super().__init__(message, 403)
