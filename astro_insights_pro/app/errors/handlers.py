"""
Error Handlers
"""

from flask import jsonify
from werkzeug.exceptions import HTTPException
from pymongo.errors import PyMongoError

from .exceptions import APIError, ValidationError, ResourceNotFoundError, AuthenticationError


def register_error_handlers(app):
    """Register error handlers for the application"""
    
    @app.errorhandler(APIError)
    def handle_api_error(error):
        response = {
            'error': {
                'code': error.code,
                'message': error.message
            }
        }
        return jsonify(response), error.code
    
    @app.errorhandler(ValidationError)
    def handle_validation_error(error):
        response = {
            'error': {
                'code': 400,
                'message': 'Validation Error',
                'errors': error.errors
            }
        }
        return jsonify(response), 400
    
    @app.errorhandler(ResourceNotFoundError)
    def handle_resource_not_found_error(error):
        response = {
            'error': {
                'code': 404,
                'message': error.message
            }
        }
        return jsonify(response), 404
    
    @app.errorhandler(AuthenticationError)
    def handle_authentication_error(error):
        response = {
            'error': {
                'code': 401,
                'message': error.message
            }
        }
        return jsonify(response), 401
    
    @app.errorhandler(PyMongoError)
    def handle_pymongo_error(error):
        response = {
            'error': {
                'code': 500,
                'message': 'Database Error',
                'details': str(error)
            }
        }
        return jsonify(response), 500
    
    @app.errorhandler(HTTPException)
    def handle_http_exception(error):
        response = {
            'error': {
                'code': error.code,
                'message': error.description
            }
        }
        return jsonify(response), error.code
    
    @app.errorhandler(Exception)
    def handle_generic_exception(error):
        response = {
            'error': {
                'code': 500,
                'message': 'Internal Server Error',
                'details': str(error)
            }
        }
        return jsonify(response), 500
