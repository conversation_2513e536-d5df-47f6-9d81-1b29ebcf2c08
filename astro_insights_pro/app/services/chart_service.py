"""
Chart Service

This module provides services for generating and working with astrological charts.
It serves as the main interface for chart functionality in the Fortune Lens application.
"""

from datetime import datetime
from collections import namedtuple as struct
from traceback import print_tb

# Import chart functions
from .horoscope.chart.charts import *
from .horoscope.dhasa.graha import vimsottari
from .panchanga import drik
from . import const, utils
from .utils.astronomy import norm360


# Define norm360 function locally in case import fails
def norm360(angle):
    """
    Normalize angle to range [0, 360).

    Args:
        angle (float): Angle in degrees

    Returns:
        float: Normalized angle
    """
    return angle % 360


def format_planet_positions_360(planet_positions, planet_list, raasi_list):
    formatted_positions = []
    for item in planet_positions:
        planet = item[0]
        raasi, longitude = item[1]
        if planet == 'L':
            planet_name = 'lagnam'
        else:
            if planet < len(planet_list):
                planet_name = planet_list[planet]
            else:
                print(f"IndexError: {planet} is out of range in planet_list")
                continue

        raasi_name = raasi_list[raasi]
        raasi_longitude = raasi * 30 + longitude  # Calculate the exact position in 360 degrees
        formatted_positions.append([planet_name, (raasi_name, round(raasi_longitude, 2))])
    return formatted_positions

# Struct definitions
Date = struct('Date', ['year', 'month', 'day'])
Place = struct('Place', ['Place', 'latitude', 'longitude', 'timezone'])

# Planet and Raasi lists
planet_list = ['sun', 'moon', 'mars', 'mercury', 'jupiter', 'venus', 'saturn', 'rahu', 'ketu']
raasi_list = ['Mesham', 'Rishabam', 'Midunam', 'Kadagam', 'Simmam', 'Kanni', 'Thulam', 'Virichigam', 'Dhanusu',
              'MAGARAM', 'Kumbam', 'Meenam']

def julian_day_number(date_of_birth_as_tuple, time_of_birth_as_tuple):
    """
    Return julian day number for given Date of birth and time of birth as tuples

    Args:
        date_of_birth_as_tuple: Date of birth as tuple. e.g. (2000,1,1)
            Note: For BC Dates use negative year e.g. (-3114,1,1) means 1-Jan of 3114 BC
            Note: There is no 0 BC or 0 AD so avoid Zero year
        time_of_birth_as_tuple: time of birth as tuple e.g. (18,0,30)

    Returns:
        julian day number
    """
    tob_in_hours = time_of_birth_as_tuple[0] + time_of_birth_as_tuple[1] / 60.0 + time_of_birth_as_tuple[2] / 3600.0
    return utils.julian_day_number(date_of_birth_as_tuple, time_of_birth_as_tuple)

def get_location_info(location):
    """
    Get location information from location string

    Args:
        location: Location string in the format "City,State,Country"

    Returns:
        Dictionary with location information
    """
    # This is a placeholder - in a real implementation, this would use a geocoding service
    # For now, we'll just parse the location string and return dummy values
    parts = location.split(',')
    city = parts[0].strip() if len(parts) > 0 else ""
    state = parts[1].strip() if len(parts) > 1 else ""
    country = parts[2].strip() if len(parts) > 2 else ""

    # In a real implementation, these would be looked up
    latitude = 13.0  # Default to Chennai
    longitude = 80.0
    timezone = 5.5   # IST

    return {
        'Location_name': f"{city},{country}",
        'latitude': latitude,
        'longitude': longitude,
        'Time_zone_offset': timezone
    }

def get_astrology_data(astro_data):
    """
    Process astrological data to organize by houses

    Args:
        astro_data: List of planet positions

    Returns:
        List of dictionaries with house data
    """
    # Determine the Lagnam (1st house)
    lagnam = next((item[1][0] for item in astro_data if item[0] == 'lagnam'), None)

    # Create the sequence of Rasis starting from the Lagnam
    if lagnam:
        start_index = raasi_list.index(lagnam)
        adjusted_raasi_list = raasi_list[start_index:] + raasi_list[:start_index]

        # Initialize a dictionary to store planets and degrees for each Rasi
        raasi_planet_dict = {raasi: {'planets': [], 'degrees': []} for raasi in adjusted_raasi_list}

        # Populate the dictionary with planets and degrees for each Rasi
        for item in astro_data:
            planet = item[0]
            raasi, degree = item[1]
            if raasi in raasi_planet_dict:
                raasi_planet_dict[raasi]['planets'].append(planet)
                raasi_planet_dict[raasi]['degrees'].append(degree)

        # Create a list of dictionaries to represent houses, planets, and degrees
        house_data = []
        for i, raasi in enumerate(adjusted_raasi_list, start=1):
            house_dict = {
                f'house_name_{i}': raasi,
                f'house_{i}_planets': raasi_planet_dict[raasi]['planets'],
                f'house_{i}_planets_degree': raasi_planet_dict[raasi]['degrees']
            }
            house_data.append(house_dict)

        return house_data
    else:
        # Return an empty list instead of a string to avoid errors
        print("Warning: Lagnam not found in the data.")
        return []

def find_nakshatra_and_pada(degree):
    """
    Calculate nakshatra and pada for a given longitude

    Args:
        degree: Longitude in degrees (0-360)

    Returns:
        Tuple of (nakshatra, pada)
    """
    nakshatras = [
        "ASHWINI", "BARANI", "KARTHIKAI", "ROHINI", "MIRIGASIRISHAM", "THIRUVADIRAI",
        "PUNARPOOSAM", "POOSAM", "AYILYAM", "MAGAM", "POORAM", "UTHIRAM", "HASTHAM",
        "CHITHIRAI", "SWATHI", "VISAGAM", "ANUSHAM", "KETTAI", "MOOLAM", "POORADAM",
        "UTHIRADAM", "THIRUVONAM", "AVITTAM", "SADAYAM", "POORATADHI", "UTHIRATTADHI", "REVATHI"
    ]
    span_per_nakshatra = 360 / 27
    span_per_pada = span_per_nakshatra / 4

    nakshatra_index = int(degree // span_per_nakshatra)
    pada_index = int((degree % span_per_nakshatra) // span_per_pada) + 1

    return nakshatras[nakshatra_index], pada_index

def process_data(data):
    """
    Add nakshatra and pada information to chart data

    Args:
        data: Chart data dictionary

    Returns:
        Updated chart data with nakshatra and pada information
    """
    for key in list(data.keys()):
        if key.endswith("_planets_degree"):
            house = key.replace("_planets_degree", "")
            planet_key = house + "_planets"
            planets = data[planet_key]
            degrees = data[key]
            for i, degree in enumerate(degrees):
                nakshatra, pada = find_nakshatra_and_pada(degree)
                planet = planets[i]
                star_key = f"{planet}_star"
                pada_key = f"{planet}_pada"
                data[star_key] = nakshatra
                data[pada_key] = pada
    return data

def generate_chart(birth_data, chart_types=None, include_all_dashas=True):
    """
    Generate all 23 astrological divisional charts based on birth data.

    This function processes birth information to calculate planetary positions
    for all 23 divisional charts used in Vedic astrology. It returns a structured
    dictionary containing detailed information for each chart, including house
    positions, planetary placements, nakshatras, and dashas.

    Args:
        birth_data (dict): Dictionary with birth information
            Required keys:
            - user_birthdate (str): Birth date in format "DD-MM-YYYY" or "YYYY-MM-DD"
            - user_birthtime (str): Birth time in format "HH:MM:SS", "HH:MM", or "HH:MM:SS AM/PM"
            - user_birthplace (str): Birth place
            - user_state (str): State
            - user_country (str): Country
        chart_types (list, optional): List of divisional chart factors to generate (e.g., [1, 9, 10])
            If None, all 23 divisional charts will be generated
        include_all_dashas (bool, optional): Whether to include all dasha levels in the output
            If True, includes maha, bhukti, antara, sukshma, and prana dashas
            If False, includes only maha dasha

    Returns:
        dict: A structured dictionary containing all 23 divisional charts with the following format:
            {
                'D1': {  # Rasi Chart
                    'chart_info': {
                        'name': 'Rasi Chart',
                        'description': 'Basic birth chart showing planetary positions at birth',
                        'divisional_factor': 1
                    },
                    'houses': [
                        {
                            'house_number': 1,
                            'house_name': 'Mesham',  # Example
                            'planets': ['sun', 'moon'],  # Example
                            'planet_degrees': [15.5, 20.3],  # Example
                            'planet_nakshatras': {'sun': 'ASHWINI', 'moon': 'BARANI'},  # Example
                            'planet_padas': {'sun': 2, 'moon': 3},  # Example
                            'planet_positions_precise': {'sun': '15 Ar 30\'00.00"', 'moon': '20 Ar 18\'00.00"'},  # Example
                            'planet_minutes_seconds': {'sun': '30\'00.00"', 'moon': '18\'00.00"'}  # Example
                        },
                        # ... other houses
                    ],
                    'lagna': {
                        'sign': 'Mesham',  # Example
                        'degree': 15.5,  # Example
                        'position_precise': '15 Ar 30\'00.00"',  # Example
                        'minutes_seconds': '30\'00.00"'  # Example
                    },
                    'planets_precise': {
                        'sun': {  # Example
                            'sign': 'Mesham',
                            'degree': 15.5,
                            'position_precise': '15 Ar 30\'00.00"',
                            'minutes_seconds': '30\'00.00"'
                        },
                        'moon': {  # Example
                            'sign': 'Mesham',
                            'degree': 20.3,
                            'position_precise': '20 Ar 18\'00.00"',
                            'minutes_seconds': '18\'00.00"'
                        }
                        # ... other planets
                    },
                    'dashas': {  # Only for D1 chart
                        'maha_dasha': {...},
                        'bhukti_dasha': {...},
                        'antara_dasha': {...},
                        'sukshma_dasha': {...},
                        'prana_dasha': {...}
                    }
                },
                'D2': {  # Hora Chart
                    # Similar structure without dashas
                },
                # ... other divisional charts
            }

    Raises:
        ValueError: If required birth data fields are missing
        Exception: For any errors in chart calculation or processing
    """
    try:
        # Validate required fields
        required_fields = ['user_birthdate', 'user_birthtime', 'user_birthplace', 'user_state', 'user_country']
        for field in required_fields:
            if field not in birth_data:
                raise ValueError(f"Missing required field: {field}")

        # Parse birth data
        dob_timestamp = birth_data['user_birthdate']
        try:
            # Try to parse date in YYYY-MM-DD format (from member_profile_service.py)
            dob_datetime = datetime.strptime(dob_timestamp, "%Y-%m-%d").date()
        except ValueError:
            try:
                # Try to parse date in DD-MM-YYYY format (from profile_workflow_service.py)
                dob_datetime = datetime.strptime(dob_timestamp, "%d-%m-%Y").date()
            except ValueError:
                raise ValueError(f"Invalid date format: {dob_timestamp}. Expected YYYY-MM-DD or DD-MM-YYYY.")
        dob = (dob_datetime.year, dob_datetime.month, dob_datetime.day)

        tob_time = birth_data['user_birthtime']
        try:
            # Try to parse time in HH:MM:SS format (from member_profile_service.py)
            tob_datetime = datetime.strptime(tob_time, "%H:%M:%S")
        except ValueError:
            try:
                # Try to parse time in HH:MM:SS AM/PM format (from profile_workflow_service.py)
                tob_datetime = datetime.strptime(tob_time, "%I:%M:%S %p")
            except ValueError:
                try:
                    # Try to parse time in HH:MM format (from member_profile_service.py)
                    tob_datetime = datetime.strptime(tob_time, "%H:%M")
                except ValueError:
                    raise ValueError(f"Invalid time format: {tob_time}. Expected HH:MM:SS, HH:MM, or HH:MM:SS AM/PM.")
        tob = (tob_datetime.hour, tob_datetime.minute, tob_datetime.second)

        location = f"{birth_data['user_birthplace']},{birth_data['user_state']},{birth_data['user_country']}"
        location_info = get_location_info(location)

        place = Place(location_info['Location_name'], location_info['latitude'], location_info['longitude'],
                     location_info['Time_zone_offset'])

        # Calculate Julian day number
        jd = julian_day_number(dob, tob)

        # Get dasha details (only need to calculate once)
        vimsottari_balance = vimsottari.get_vimsottari_dhasa_bhukthi(jd, place, star_position_from_moon=1,
                                                                    divisional_chart_factor=1)
        maha_dasha, bhukti_dasha, antara_dasha, sukshma_dasha, prana_dasha = vimsottari_balance
        # print(f"Mahadasha records: {len(maha_dasha)}")
        # print(f"Bhukti records: {len(bhukti_dasha)}")
        # print(f"Antardasha records: {len(antara_dasha)}")
        # print(f"Sukshma records: {len(sukshma_dasha)}")
        # print(f"Prana records: {len(prana_dasha)}")

        # Define all divisional chart factors and their descriptions
        divisional_chart_info = {
            1: {'name': 'Rasi Chart', 'description': 'Basic birth chart showing planetary positions at birth'},
            2: {'name': 'Hora Chart', 'description': 'Chart of wealth and prosperity'},
            3: {'name': 'Drekkana Chart', 'description': 'Chart of siblings and courage'},
            4: {'name': 'Chaturthamsa Chart', 'description': 'Chart of fortune and property'},
            5: {'name': 'Panchamsa Chart', 'description': 'Chart of spiritual merits from past lives'},
            6: {'name': 'Shashthamsa Chart', 'description': 'Chart of health, disease, and enemies'},
            7: {'name': 'Saptamsa Chart', 'description': 'Chart of children and progeny'},
            8: {'name': 'Ashtamsa Chart', 'description': 'Chart of unexpected events and obstacles'},
            9: {'name': 'Navamsa Chart', 'description': 'Chart of spouse and general fortune'},
            10: {'name': 'Dasamsa Chart', 'description': 'Chart of career and professional life'},
            11: {'name': 'Rudramsa Chart', 'description': 'Chart of gains and fulfillment of desires'},
            12: {'name': 'Dwadasamsa Chart', 'description': 'Chart of parents and ancestry'},
            16: {'name': 'Shodasamsa Chart', 'description': 'Chart of vehicles and comforts'},
            20: {'name': 'Vimsamsa Chart', 'description': 'Chart of spiritual pursuits and worship'},
            24: {'name': 'Chaturvimsamsa Chart', 'description': 'Chart of education and learning'},
            27: {'name': 'Nakshatramsa Chart', 'description': 'Chart of strengths and weaknesses'},
            30: {'name': 'Trimsamsa Chart', 'description': 'Chart of misfortunes and challenges'},
            40: {'name': 'Khavedamsa Chart', 'description': 'Chart of auspicious and inauspicious effects'},
            45: {'name': 'Akshavedamsa Chart', 'description': 'Chart of general indications'},
            60: {'name': 'Shashtyamsa Chart', 'description': 'Chart of overall life indications'},
            81: {'name': 'Nava Navamsa Chart', 'description': 'Chart of hidden aspects'},
            108: {'name': 'Ashtotharamsa Chart', 'description': 'Chart of specific spiritual indications'},
            144: {'name': 'Dwadas Dwadasamsa Chart', 'description': 'Chart of final conclusions'}
        }

        # Dictionary to store all chart results
        all_charts = {}

        # Generate D1 (Rasi) chart first
        rasi_positions = rasi_chart(jd, place)
        rasi_formatted_positions = format_planet_positions_360(rasi_positions, planet_list, raasi_list)
        rasi_house_data_list = get_astrology_data(rasi_formatted_positions)
        # Add D1 chart to all_charts
        d1_info = divisional_chart_info[1]
        d1_chart_result = {
            'chart_info': {
                'name': d1_info['name'],
                'description': d1_info['description'],
                'divisional_factor': 1
            },
            'houses': [],
            'planets_precise': {},
            'lagna': {}
        }

        # Add lagna and planet information
        for item in rasi_positions:
            planet = item[0]
            raasi, degree = item[1]

            if planet == 'L':
                d1_chart_result['lagna'] = {
                    'sign': raasi_list[raasi],
                    'degree': degree,
                    'position_precise': format_precise_degrees(degree, raasi_list[raasi]),
                    'minutes_seconds': format_precise_degrees(degree, raasi_list[raasi], minutes_seconds_only=True)
                }
            else:
                if planet < len(planet_list):
                    planet_name = planet_list[planet]
                    d1_chart_result['planets_precise'][planet_name] = {
                        'sign': raasi_list[raasi],
                        'degree': degree,
                        'position_precise': format_precise_degrees(degree, raasi_list[raasi]),
                        'minutes_seconds': format_precise_degrees(degree, raasi_list[raasi], minutes_seconds_only=True)
                    }

        # Process D1 chart data
        if rasi_house_data_list and not isinstance(rasi_house_data_list, str):
            # We don't need to process planets here since we're only interested in house data
            # The docstring format only requires house data with planets, degrees, nakshatras, and padas

            # Process house data for D1
            for house_number, house_data in enumerate(rasi_house_data_list, 1):
                house_name = house_data[f'house_name_{house_number}']
                planets = house_data[f'house_{house_number}_planets']
                planet_degrees = house_data[f'house_{house_number}_planets_degree']

                # Create house object matching the docstring format
                house_obj = {
                    'house_number': house_number,
                    'house_name': house_name,
                    'planets': planets,
                    'planet_degrees': planet_degrees,
                    'planet_nakshatras': {},
                    'planet_padas': {},
                    'planet_positions_precise': {},
                    'planet_minutes_seconds': {}
                }

                # Calculate nakshatra and pada for each planet
                for i, planet in enumerate(planets):
                    degree = planet_degrees[i]
                    nakshatra, pada = find_nakshatra_and_pada(degree)
                    house_obj['planet_nakshatras'][planet] = nakshatra
                    house_obj['planet_padas'][planet] = pada

                    # Add precise degree notation (e.g., "27 Li 51' 50.28\"")
                    house_obj['planet_positions_precise'][planet] = format_precise_degrees(degree, house_name)
                    # Add minutes and seconds only (e.g., "51' 50.28\"")
                    house_obj['planet_minutes_seconds'][planet] = format_precise_degrees(degree, house_name, minutes_seconds_only=True)

                d1_chart_result['houses'].append(house_obj)

        # Add D1 chart to all_charts
        all_charts['D1'] = d1_chart_result
        # print(all_charts)
        # Add dasha information to D1 chart
        if include_all_dashas:
            all_charts['D1']['dashas'] = {
                'maha_dasha': maha_dasha,
                'bhukti_dasha': bhukti_dasha,
                # 'antara_dasha': antara_dasha,
                # 'sukshma_dasha': sukshma_dasha,
                # 'prana_dasha': prana_dasha
            }
        else:
            all_charts['D1']['dashas'] = {
                'maha_dasha': maha_dasha
            }
        # print(f"All charts with dashas: {all_charts['D1']['dashas']}")
        # Determine which charts to generate
        if chart_types is None:
            # Generate all charts
            factors_to_generate = list(divisional_chart_info.keys())
        else:
            # Generate only specified charts
            factors_to_generate = [f for f in chart_types if f in divisional_chart_info]
            # Make sure D1 is included if it was requested
            if 1 not in factors_to_generate:
                factors_to_generate.append(1)

        # Generate all other divisional charts
        for factor in factors_to_generate:
            # Skip D1 as we've already processed it
            if factor == 1:
                continue

            info = divisional_chart_info[factor]

            try:
                # Use the D1 chart positions as the basis for all other charts
                # For each divisional chart, apply the appropriate divisional calculation
                if factor == 2:  # D2 (Hora)
                    chart_positions = hora_chart(rasi_positions)
                elif factor == 3:  # D3 (Drekkana)
                    chart_positions = drekkana_chart(rasi_positions)
                elif factor == 4:  # D4 (Chaturthamsa)
                    chart_positions = chaturthamsa_chart(rasi_positions)
                elif factor == 5:  # D5 (Panchamsa)
                    chart_positions = panchamsa_chart(rasi_positions)
                elif factor == 6:  # D6 (Shashthamsa)
                    chart_positions = shashthamsa_chart(rasi_positions)
                elif factor == 7:  # D7 (Saptamsa)
                    chart_positions = saptamsa_chart(rasi_positions)
                elif factor == 8:  # D8 (Ashtamsa)
                    chart_positions = ashtamsa_chart(rasi_positions)
                elif factor == 9:  # D9 (Navamsa)
                    chart_positions = navamsa_chart(rasi_positions)
                elif factor == 10:  # D10 (Dasamsa)
                    chart_positions = dasamsa_chart(rasi_positions)
                elif factor == 11:  # D11 (Rudramsa)
                    chart_positions = rudramsa_chart(rasi_positions)
                elif factor == 12:  # D12 (Dwadasamsa)
                    chart_positions = dwadasamsa_chart(rasi_positions)
                elif factor == 16:  # D16 (Shodasamsa)
                    chart_positions = shodasamsa_chart(rasi_positions)
                elif factor == 20:  # D20 (Vimsamsa)
                    chart_positions = vimsamsa_chart(rasi_positions)
                elif factor == 24:  # D24 (Chaturvimsamsa)
                    chart_positions = chaturvimsamsa_chart(rasi_positions)
                elif factor == 27:  # D27 (Nakshatramsa)
                    chart_positions = nakshatramsa_chart(rasi_positions)
                elif factor == 30:  # D30 (Trimsamsa)
                    chart_positions = trimsamsa_chart(rasi_positions)
                elif factor == 40:  # D40 (Khavedamsa)
                    chart_positions = khavedamsa_chart(rasi_positions)
                elif factor == 45:  # D45 (Akshavedamsa)
                    chart_positions = akshavedamsa_chart(rasi_positions)
                elif factor == 60:  # D60 (Shashtyamsa)
                    chart_positions = shashtyamsa_chart(rasi_positions)
                elif factor == 81:  # D81 (Nava Navamsa)
                    chart_positions = nava_navamsa_chart(rasi_positions)
                elif factor == 108:  # D108 (Ashtotharamsa)
                    chart_positions = ashtotharamsa_chart(rasi_positions)
                elif factor == 144:  # D144 (Dwadas Dwadasamsa)
                    chart_positions = dwadas_dwadasamsa_chart(rasi_positions)
                else:
                    # Fallback for any other divisional chart
                    # This should not happen as we're explicitly handling all 23 charts
                    print(f"Warning: Unknown divisional chart factor {factor}")
                    chart_positions = []


                # Format planet positions
                formatted_position = format_planet_positions_360(chart_positions, planet_list, raasi_list)
                house_data_list = get_astrology_data(formatted_position)

                # Create structured chart result exactly matching the docstring format
                chart_result = {
                    'chart_info': {
                        'name': info['name'],
                        'description': info['description'],
                        'divisional_factor': factor
                    },
                    'houses': [],
                    'planets_precise': {},
                    'lagna': {}
                }

                # Add lagna and planet information
                for item in chart_positions:
                    planet = item[0]
                    raasi, degree = item[1]

                    if planet == 'L':
                        chart_result['lagna'] = {
                            'sign': raasi_list[raasi],
                            'degree': degree,
                            'position_precise': format_precise_degrees(degree, raasi_list[raasi]),
                            'minutes_seconds': format_precise_degrees(degree, raasi_list[raasi], minutes_seconds_only=True)
                        }
                    else:
                        if planet < len(planet_list):
                            planet_name = planet_list[planet]
                            chart_result['planets_precise'][planet_name] = {
                                'sign': raasi_list[raasi],
                                'degree': degree,
                                'position_precise': format_precise_degrees(degree, raasi_list[raasi]),
                                'minutes_seconds': format_precise_degrees(degree, raasi_list[raasi], minutes_seconds_only=True)
                            }
                # Process house data
                for house_number, house_data in enumerate(house_data_list, 1):
                    house_name = house_data[f'house_name_{house_number}']
                    planets = house_data[f'house_{house_number}_planets']
                    planet_degrees = house_data[f'house_{house_number}_planets_degree']

                    # Create house object matching the docstring format
                    house_obj = {
                        'house_number': house_number,
                        'house_name': house_name,
                        'planets': planets,
                        'planet_degrees': planet_degrees,
                        'planet_nakshatras': {},
                        'planet_padas': {},
                        'planet_positions_precise': {},
                        'planet_minutes_seconds': {}
                    }

                    # Calculate nakshatra and pada for each planet
                    for i, planet in enumerate(planets):
                        degree = planet_degrees[i]
                        nakshatra, pada = find_nakshatra_and_pada(degree)
                        house_obj['planet_nakshatras'][planet] = nakshatra
                        house_obj['planet_padas'][planet] = pada

                        # Add precise degree notation (e.g., "27 Li 51' 50.28\"")
                        house_obj['planet_positions_precise'][planet] = format_precise_degrees(degree, house_name)
                        # Add minutes and seconds only (e.g., "51' 50.28\"")
                        house_obj['planet_minutes_seconds'][planet] = format_precise_degrees(degree, house_name, minutes_seconds_only=True)

                    chart_result['houses'].append(house_obj)

                # Add to all charts dictionary
                chart_name = f'D{factor}'
                all_charts[chart_name] = chart_result
            except Exception as e:
                print(f"Error generating chart D{factor}: {str(e)}")
                # Create an empty structure for this chart
                chart_name = f'D{factor}'
                all_charts[chart_name] = {
                    'chart_info': {
                        'name': info['name'],
                        'description': info['description'],
                        'divisional_factor': factor
                    },
                    'error': str(e)
                }

            # print(all_charts)
        return all_charts

    except ValueError as ve:
        return {'error': str(ve)}
    except Exception as e:
        return {'error': f"Chart generation error: {str(e)}"}

def generate_divisional_chart(birth_data, divisional_chart_factor=1, include_all_dashas=True):
    """
    Generate a specific divisional chart with detailed structure.

    This function processes birth information to calculate planetary positions
    for a specific divisional chart. It returns a structured dictionary containing
    detailed information for the chart, including house positions, planetary placements,
    nakshatras, and dashas (for D1 chart only).

    Args:
        birth_data (dict): Dictionary with birth information
            Required keys:
            - user_birthdate (str): Birth date in format "DD-MM-YYYY"
            - user_birthtime (str): Birth time in format "HH:MM:SS AM/PM"
            - user_birthplace (str): Birth place
            - user_state (str): State
            - user_country (str): Country
        divisional_chart_factor (int): Divisional chart factor (1=D1, 9=D9, etc.)
        include_all_dashas (bool, optional): Whether to include all dasha levels in the output
            If True, includes maha, bhukti, antara, sukshma, and prana dashas
            If False, includes only maha dasha

    Returns:
        dict: A structured dictionary containing the divisional chart data with the following format:
            {
                'chart_info': {
                    'name': 'Chart Name',
                    'description': 'Chart description',
                    'divisional_factor': divisional_chart_factor
                },
                'houses': [
                    {
                        'house_number': 1,
                        'house_name': 'Mesham',  # Example
                        'planets': ['sun', 'moon'],  # Example
                        'planet_degrees': [15.5, 20.3],  # Example
                        'planet_nakshatras': {'sun': 'ASHWINI', 'moon': 'BARANI'},  # Example
                        'planet_padas': {'sun': 2, 'moon': 3}  # Example
                    },
                    # ... other houses
                ],
                'dashas': {  # Only for D1 chart
                    'maha_dasha': {...},
                    'bhukti_dasha': {...},
                    'antara_dasha': {...},
                    'sukshma_dasha': {...},
                    'prana_dasha': {...}
                },
                'birth_details': {
                    'date': '01-01-1990',  # Example
                    'time': '10:30:00 AM',  # Example
                    'place': 'Chennai',  # Example
                    'state': 'Tamil Nadu',  # Example
                    'country': 'India',  # Example
                    'latitude': 13.0,  # Example
                    'longitude': 80.0,  # Example
                    'timezone': 5.5  # Example
                }
            }

    Raises:
        ValueError: If required birth data fields are missing or divisional chart factor is invalid
        Exception: For any errors in chart calculation or processing
    """
    # try:
    # Validate required fields
    required_fields = ['user_birthdate', 'user_birthtime', 'user_birthplace', 'user_state', 'user_country']
    for field in required_fields:
        if field not in birth_data:
            raise ValueError(f"Missing required field: {field}")

    # Validate divisional chart factor
    valid_factors = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 16, 20, 24, 27, 30, 40, 45, 60, 81, 108, 144]
    if divisional_chart_factor not in valid_factors:
        raise ValueError(f"Invalid divisional chart factor: {divisional_chart_factor}. Must be one of {valid_factors}")

    # Chart info dictionary
    chart_info_dict = {
        1: {'name': 'Rasi Chart', 'description': 'Basic birth chart showing planetary positions at birth'},
        2: {'name': 'Hora Chart', 'description': 'Chart of wealth and prosperity'},
        3: {'name': 'Drekkana Chart', 'description': 'Chart of siblings and courage'},
        4: {'name': 'Chaturthamsa Chart', 'description': 'Chart of fortune and property'},
        5: {'name': 'Panchamsa Chart', 'description': 'Chart of spiritual merits from past lives'},
        6: {'name': 'Shashthamsa Chart', 'description': 'Chart of health, disease, and enemies'},
        7: {'name': 'Saptamsa Chart', 'description': 'Chart of children and progeny'},
        8: {'name': 'Ashtamsa Chart', 'description': 'Chart of unexpected events and obstacles'},
        9: {'name': 'Navamsa Chart', 'description': 'Chart of spouse and general fortune'},
        10: {'name': 'Dasamsa Chart', 'description': 'Chart of career and professional life'},
        11: {'name': 'Rudramsa Chart', 'description': 'Chart of gains and fulfillment of desires'},
        12: {'name': 'Dwadasamsa Chart', 'description': 'Chart of parents and ancestry'},
        16: {'name': 'Shodasamsa Chart', 'description': 'Chart of vehicles and comforts'},
        20: {'name': 'Vimsamsa Chart', 'description': 'Chart of spiritual pursuits and worship'},
        24: {'name': 'Chaturvimsamsa Chart', 'description': 'Chart of education and learning'},
        27: {'name': 'Nakshatramsa Chart', 'description': 'Chart of strengths and weaknesses'},
        30: {'name': 'Trimsamsa Chart', 'description': 'Chart of misfortunes and challenges'},
        40: {'name': 'Khavedamsa Chart', 'description': 'Chart of auspicious and inauspicious effects'},
        45: {'name': 'Akshavedamsa Chart', 'description': 'Chart of general indications'},
        60: {'name': 'Shashtyamsa Chart', 'description': 'Chart of overall life indications'},
        81: {'name': 'Nava Navamsa Chart', 'description': 'Chart of hidden aspects'},
        108: {'name': 'Ashtotharamsa Chart', 'description': 'Chart of specific spiritual indications'},
        144: {'name': 'Dwadas Dwadasamsa Chart', 'description': 'Chart of final conclusions'}
    }

    # Parse birth data
    dob_timestamp = birth_data['user_birthdate']
    dob_datetime = datetime.strptime(dob_timestamp, "%d-%m-%Y").date()
    dob = (dob_datetime.year, dob_datetime.month, dob_datetime.day)

    tob_time = birth_data['user_birthtime']
    tob_datetime = datetime.strptime(tob_time, "%I:%M:%S %p")
    tob = (tob_datetime.hour, tob_datetime.minute, tob_datetime.second)

    location = f"{birth_data['user_birthplace']},{birth_data['user_state']},{birth_data['user_country']}"
    location_info = get_location_info(location)

    place = Place(location_info['Location_name'], location_info['latitude'], location_info['longitude'],
                 location_info['Time_zone_offset'])

    # Calculate Julian day number
    jd = julian_day_number(dob, tob)

    # Generate divisional chart
    chart_positions = divisional_chart(jd, place, divisional_chart_factor=divisional_chart_factor)

    # Format planet positions
    formatted_position = format_planet_positions_360(chart_positions, planet_list, raasi_list)
    house_data_list = get_astrology_data(formatted_position)
    print(f"House data: {house_data_list}, Formatted positions: {formatted_position}")
    # Create structured chart result
    chart_result = {
        'chart_info': {
            'name': chart_info_dict[divisional_chart_factor]['name'],
            'description': chart_info_dict[divisional_chart_factor]['description'],
            'divisional_factor': divisional_chart_factor
        },
        'houses': [],
        'lagna': {},
        'planets_precise': {}
    }

    # Process all planetary positions including Lagna
    for item in chart_positions:
        planet = item[0]
        raasi, degree = item[1]

        # Add precise position for all planets
        if planet == 'L':
            chart_result['lagna'] = {
                'sign': raasi_list[raasi],
                'degree': degree,
                'position_precise': format_precise_degrees(degree, raasi_list[raasi]),
                'minutes_seconds': format_precise_degrees(degree, raasi_list[raasi], minutes_seconds_only=True)
            }
        else:
            if planet < len(planet_list):
                planet_name = planet_list[planet]
                chart_result['planets_precise'][planet_name] = {
                    'sign': raasi_list[raasi],
                    'degree': degree,
                    'position_precise': format_precise_degrees(degree, raasi_list[raasi]),
                    'minutes_seconds': format_precise_degrees(degree, raasi_list[raasi], minutes_seconds_only=True)
                }

    # Process house data
    for house_number, house_data in enumerate(house_data_list, 1):
        house_name = house_data[f'house_name_{house_number}']
        planets = house_data[f'house_{house_number}_planets']
        planet_degrees = house_data[f'house_{house_number}_planets_degree']

        # Create house object
        house_obj = {
            'house_number': house_number,
            'house_name': house_name,
            'planets': planets,
            'planet_degrees': planet_degrees,
            'planet_nakshatras': {},
            'planet_padas': {},
            'planet_positions_precise': {}
        }

        # Calculate nakshatra and pada for each planet
        for i, planet in enumerate(planets):
            degree = planet_degrees[i]
            nakshatra, pada = find_nakshatra_and_pada(degree)
            house_obj['planet_nakshatras'][planet] = nakshatra
            house_obj['planet_padas'][planet] = pada

            # Add precise degree notation (e.g., "27 Li 51' 50.28\"")
            house_obj['planet_positions_precise'][planet] = format_precise_degrees(degree, house_name)
            # Add minutes and seconds only (e.g., "51' 50.28\"")
            house_obj['planet_minutes_seconds'] = house_obj.get('planet_minutes_seconds', {})
            house_obj['planet_minutes_seconds'][planet] = format_precise_degrees(degree, house_name, minutes_seconds_only=True)

        chart_result['houses'].append(house_obj)
    # print(chart_result)
    # Add dasha information for D1 chart only
    if divisional_chart_factor == 1:
        vimsottari_balance = vimsottari.get_vimsottari_dhasa_bhukthi(jd, place, star_position_from_moon=1,
                                                                    divisional_chart_factor=1)
        maha_dasha, bhukti_dasha, antara_dasha, sukshma_dasha, prana_dasha = vimsottari_balance

        if include_all_dashas:
            chart_result['dashas'] = {
                'maha_dasha': maha_dasha,
                'bhukti_dasha': bhukti_dasha,
                'antara_dasha': antara_dasha,
                'sukshma_dasha': sukshma_dasha,
                'prana_dasha': prana_dasha
            }
        else:
            chart_result['dashas'] = {
                'maha_dasha': maha_dasha
            }

    # Add birth details
    chart_result['birth_details'] = {
        'date': dob_timestamp,
        'time': tob_time,
        'place': birth_data['user_birthplace'],
        'state': birth_data['user_state'],
        'country': birth_data['user_country'],
        'latitude': location_info['latitude'],
        'longitude': location_info['longitude'],
        'timezone': location_info['Time_zone_offset']
    }

    return chart_result

    # except ValueError as ve:
    #     return {'error': str(ve)}
    # except Exception as e:
    #     return {'error': f"Chart generation error: {str(e)}"}

def format_precise_degrees(degree, sign, minutes_seconds_only=False):
    """
    Format a degree value into precise astrological notation with degrees, minutes, and seconds

    Args:
        degree (float): Degree value (0-30)
        sign (str): Zodiac sign name
        minutes_seconds_only (bool): If True, return only minutes and seconds (e.g., "51' 50.28\"")

    Returns:
        str: Formatted position string (e.g., "27 Li 51' 50.28\"") or just minutes and seconds (e.g., "51' 50.28\"")
    """
    # Get the sign abbreviation (first two letters)
    sign_abbr = {
        'Mesham': 'Ar', 'Rishabam': 'Ta', 'Midunam': 'Ge', 'Kadagam': 'Ca',
        'Simmam': 'Le', 'Kanni': 'Vi', 'Thulam': 'Li', 'Virichigam': 'Sc',
        'Dhanusu': 'Sg', 'MAGARAM': 'Cp', 'Kumbam': 'Aq', 'Meenam': 'Pi'
    }

    # Calculate degrees, minutes, and seconds
    degrees = int(degree)
    minutes_float = (degree - degrees) * 60
    minutes = int(minutes_float)
    seconds = (minutes_float - minutes) * 60

    # Format the string
    if minutes_seconds_only:
        return f"{minutes}' {seconds:.2f}\""
    else:
        return f"{degrees} {sign_abbr.get(sign, sign)} {minutes}' {seconds:.2f}\""


def format_planet_positions_precise(chart_positions, planet_list, raasi_list):
    """
    Format planet positions in precise astrological notation with degrees, minutes, and seconds

    This function converts raw degree values into the standard astrological notation
    format that shows degrees, minutes (1 degree = 60 minutes), and seconds
    (1 minute = 60 seconds) along with the zodiac sign abbreviation.

    Example: "27 Li 51' 50.28\"" means 27 degrees, 51 minutes, and 50.28 seconds
    in the sign of Libra.

    Args:
        chart_positions: List of planet positions
        planet_list: List of planets
        raasi_list: List of zodiac signs

    Returns:
        dict: Dictionary of formatted planet positions
    """
    formatted_positions = {}

    for item in chart_positions:
        planet = item[0]
        raasi, degree = item[1]

        if planet in planet_list or planet == 'lagnam':
            formatted_positions[planet] = format_precise_degrees(degree, raasi_list[raasi])

    return formatted_positions


def get_all_planet_positions_precise(chart_positions, planet_list, raasi_list):
    """
    Get a flat list of all planetary positions with precise degree notation

    This function creates a simple dictionary with planet names as keys and
    their precise positions (with degrees, minutes, and seconds) as values.
    It's useful for displaying a simple list of all planetary positions.

    Args:
        chart_positions: List of planet positions
        planet_list: List of planets
        raasi_list: List of zodiac signs

    Returns:
        dict: Dictionary with planet names as keys and precise positions as values
              Example: {'sun': '15 Li 44\'35.52"', 'moon': '27 Sc 51\'50.28"', ...}
    """
    positions = {}
    minutes_seconds = {}
    planet_names = {
        'lagnam': 'Lagna',
        'sun': 'Sun',
        'moon': 'Moon',
        'mars': 'Mars',
        'mercury': 'Mercury',
        'jupiter': 'Jupiter',
        'venus': 'Venus',
        'saturn': 'Saturn',
        'rahu': 'Rahu',
        'ketu': 'Ketu'
    }

    for item in chart_positions:
        planet = item[0]
        raasi, degree = item[1]

        if planet in planet_list or planet == 'lagnam':
            planet_name = planet_names.get(planet, planet)
            positions[planet_name] = format_precise_degrees(degree, raasi_list[raasi])
            minutes_seconds[planet_name] = format_precise_degrees(degree, raasi_list[raasi], minutes_seconds_only=True)

    return {
        'full_positions': positions,
        'minutes_seconds': minutes_seconds
    }


def get_available_chart_types():
    """
    Get a list of available chart types

    Returns:
        Dictionary with available chart types
    """
    chart_types = {
        'D1': 'Rasi Chart',
        'D2': 'Hora Chart',
        'D3': 'Drekkana Chart',
        'D4': 'Chaturthamsa Chart',
        'D5': 'Panchamsa Chart',
        'D6': 'Shashthamsa Chart',
        'D7': 'Saptamsa Chart',
        'D8': 'Ashtamsa Chart',
        'D9': 'Navamsa Chart',
        'D10': 'Dasamsa Chart',
        'D11': 'Rudramsa Chart',
        'D12': 'Dwadasamsa Chart',
        'D16': 'Shodasamsa Chart',
        'D20': 'Vimsamsa Chart',
        'D24': 'Chaturvimsamsa Chart',
        'D27': 'Nakshatramsa Chart',
        'D30': 'Trimsamsa Chart',
        'D40': 'Khavedamsa Chart',
        'D45': 'Akshavedamsa Chart',
        'D60': 'Shashtyamsa Chart',
        'D81': 'Nava Navamsa Chart',
        'D108': 'Ashtotharamsa Chart',
        'D144': 'Dwadas Dwadasamsa Chart'
    }

    return chart_types
