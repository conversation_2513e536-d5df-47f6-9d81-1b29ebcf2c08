"""
Utility Functions Module

This module provides utility functions used throughout the application.
It imports and re-exports functions from specialized utility modules.

Note: This module is maintained for backward compatibility.
New code should import directly from the specialized modules in the utils package.
"""

# Import from specialized utility modules
from .utils.location import (
    get_location,
    get_place_from_user_ip_address,
    get_elevation,
    get_place_timezone_offset
)

from .utils.date_time import (
    julian_day_number,
    jd_to_gregorian,
    gregorian_to_jd,
    jd_to_gregorian_string,
    jd_to_local,
    julian_day_utc,
    local_time_to_jdut1,
    from_dms,
    to_dms,
    to_dms_prec,
    from_dms_str_to_dms,
    from_dms_str_to_degrees,
    date_diff_in_years_months_days,
    get_dob_years_months_60hrs_from_today,
    panchanga_date_diff,
    next_panchanga_day,
    previous_panchanga_day
)

from .utils.astronomy import (
    set_ephemeris_data_path,
    get_house_to_planet_dict_from_planet_to_house_dict,
    get_planet_to_house_dict_from_chart,
    get_planet_house_dictionary_from_planet_positions,
    get_house_planet_list_from_planet_positions,
    deeptaamsa_range_of_planet,
    norm180,
    norm360,
    unwrap_angles,
    count_stars,
    count_rasis,
    closest_elements,
    closest_element_from_list,
    get_fraction
)

# Import other required modules
import os
import sys
import numpy as np
import swisseph as swe
from app.services.const import *
import app.services.const as const
from .panchanga import drik as drig_panchanga

# Global variables for backward compatibility
[PLANET_NAMES, NAKSHATRA_LIST, TITHI_LIST, RAASI_LIST, KARANA_LIST, DAYS_LIST,
 PAKSHA_LIST, YOGAM_LIST, MONTH_LIST, YEAR_LIST, DHASA_LIST,
 BHUKTHI_LIST, PLANET_SHORT_NAMES, RAASI_SHORT_LIST,
 SHADVARGAMSA_NAMES, SAPTAVARGAMSA_NAMES, DHASAVARGAMSA_NAMES, SHODASAVARGAMSA_NAMES,
 SEASON_NAMES] = ([''],) * 19

# Helper functions
sort_tuple = lambda tup, tup_index, reverse=False: sorted(tup, key=lambda x: x[tup_index], reverse=reverse)
flatten_list = lambda list_of_lists: [item for sublist in list_of_lists for item in sublist]


# Legacy functions - these are kept for backward compatibility
# New code should use the functions from the utils package

def _get_time_zone_hours():
    """Legacy function for getting timezone hours."""
    from .utils.location import _get_time_zone_hours as _get_tz_hours
    return _get_tz_hours()


def save_location_to_database(location_data):
    """Legacy function for saving location to database."""
    from .utils.location import save_location_to_database as save_loc
    return save_loc(location_data)


def _get_place_from_ipinfo():
    """Legacy function for getting place from ipinfo."""
    from .utils.location import _get_place_from_ipinfo as get_place_ipinfo
    return get_place_ipinfo()


def _validate_data(place, latitude, longitude, time_zone_offset, dob, tob, division_chart_factor):
    """
    Validate and fill in missing data for astrological calculations.

    Args:
        place (str): Place name
        latitude (float): Latitude
        longitude (float): Longitude
        time_zone_offset (float): Timezone offset in hours
        dob (tuple): Date of birth as tuple (year, month, day)
        tob (tuple): Time of birth as tuple (hour, minute, second)
        division_chart_factor (int): Division chart factor

    Returns:
        tuple: (place, latitude, longitude, time_zone_offset, dob, tob, division_chart_factor)
    """
    import warnings
    import datetime

    # If place is provided but coordinates are not, get coordinates from place
    if place is not None and (latitude is None or longitude is None):
        city, latitude, longitude, time_zone_offset = get_location(place)

    # If coordinates are still missing, try to get them from IP address
    if latitude is None or longitude is None:
        place, latitude, longitude, time_zone_offset = get_place_from_user_ip_address()

    # If date of birth is missing, use today's date
    if dob is None:
        today = datetime.datetime.today()
        dob = (today.year, today.month, today.day)
        print("Today's Date:", dob, 'assumed')

    # If time of birth is missing, use current time
    if tob is None:
        tob = tuple(str(datetime.datetime.now()).split()[1].split(':'))
        print('Current time:', tob, 'assumed')

    # Validate division chart factor
    if division_chart_factor not in const.division_chart_factors:
        str_dvf = ','.join([str(x) for x in const.division_chart_factors])
        w_msg = f'\nInvalid value for dhasa varga factor: {division_chart_factor}'
        w_msg += f'\nAllowed values: {str_dvf}'
        w_msg += '\ndivision_chart_factor=1 (for Raasi) is assumed now'
        warnings.warn(w_msg)
        division_chart_factor = 1

    return place, latitude, longitude, time_zone_offset, dob, tob, division_chart_factor


def scrap_google_map_for_latlongtz_from_city_with_country(city_with_country):
    """Legacy function for scraping Google Maps."""
    from .utils.location import _scrap_google_map_for_latlongtz_from_city_with_country as scrap_google
    lat, long, tz = scrap_google(city_with_country)
    return city_with_country, lat, long, tz


def get_location_using_nominatim(place_with_country_code):
    """Legacy function for getting location using Nominatim."""
    from .utils.location import get_location_using_nominatim as get_loc_nominatim
    return get_loc_nominatim(place_with_country_code)


def _scrap_google_map_for_latlongtz_from_city_with_country(city_with_country):
    """Legacy function for scraping Google Maps."""
    from .utils.location import _scrap_google_map_for_latlongtz_from_city_with_country as scrap_google
    return scrap_google(city_with_country)


def _get_timezone_from_pytz(timezone_str_from_geocoder):
    """Legacy function for getting timezone from pytz."""
    print("Trying pytz to get timezone value")
    from pytz import timezone
    import datetime

    tz = datetime.datetime.now(timezone(timezone_str_from_geocoder)).utcoffset().total_seconds() / 60 / 60
    return tz


# The following functions are imported from the utils package
# They are kept here for backward compatibility


def set_language(language=const._DEFAULT_LANGUAGE):
    """
    Set the language for astrological terms and messages.

    Args:
        language (str): Language code (e.g., 'en', 'ta')
    """
    global PLANET_NAMES, NAKSHATRA_LIST, TITHI_LIST, RAASI_LIST, KARANA_LIST, DAYS_LIST, PAKSHA_LIST, YOGAM_LIST, MONTH_LIST, YEAR_LIST, DHASA_LIST, BHUKTHI_LIST, PLANET_SHORT_NAMES, RAASI_SHORT_LIST
    global SHADVARGAMSA_NAMES, SAPTAVARGAMSA_NAMES, DHASAVARGAMSA_NAMES, SHODASAVARGAMSA_NAMES
    global SEASON_NAMES
    global resource_strings

    if language in const.available_languages.values():
        const._DEFAULT_LANGUAGE = language
        language_list_file = const._LANGUAGE_PATH + const._DEFAULT_LANGUAGE_LIST_STR + language + '.txt'
        language_message_file = const._LANGUAGE_PATH + const._DEFAULT_LANGUAGE_MSG_STR + language + '.txt'

        [PLANET_NAMES, NAKSHATRA_LIST, TITHI_LIST, RAASI_LIST, KARANA_LIST, DAYS_LIST, PAKSHA_LIST, YOGAM_LIST, MONTH_LIST, \
         YEAR_LIST, DHASA_LIST, BHUKTHI_LIST, PLANET_SHORT_NAMES, RAASI_SHORT_LIST, SHADVARGAMSA_NAMES, \
         SAPTAVARGAMSA_NAMES, DHASAVARGAMSA_NAMES, SHODASAVARGAMSA_NAMES, SEASON_NAMES] = \
            get_resource_lists(language_list_file)
        resource_strings = get_resource_messages(language_message_file=language_message_file)


def _read_resource_messages_from_file(message_file):
    """
    Read resource messages from a file.

    Args:
        message_file (str): Path to the message file

    Returns:
        dict: Dictionary of message keys and values
    """
    import codecs
    import os

    if not os.path.exists(message_file):
        print(f'Error: List Types File: {message_file} does not exist. Script aborted.')
        exit()

    cal_key_list = {}
    with codecs.open(message_file, encoding='utf-8', mode='r') as fp:
        line_list = fp.read().splitlines()

    for line in line_list:
        # Skip comments
        if line.replace("\r\n", "").replace("\r", "").rstrip().lstrip() and line.replace("\r\n", "").replace("\r", "").rstrip().lstrip()[0] == '#':
            continue

        # Skip empty lines
        if not line.strip():
            continue

        # Parse key-value pairs
        try:
            splitLine = line.split('=')
            cal_key_list[splitLine[0].strip()] = splitLine[1].strip()
        except IndexError:
            print(f"Warning: Invalid line format in {message_file}: {line}")

    return cal_key_list


def get_resource_messages(language_message_file=None):
    """
    Retrieve message strings from language-specific message resource file.

    Args:
        language_message_file (str): Path to the language message file
            Default: const._LANGUAGE_PATH + 'msg_strings_' + const._DEFAULT_LANGUAGE + '.txt'

    Returns:
        dict: Dictionary of message keys with language-specific values
    """
    if language_message_file is None:
        language_message_file = const._LANGUAGE_PATH + const._DEFAULT_LANGUAGE_MSG_STR + const._DEFAULT_LANGUAGE + '.txt'

    res = _read_resource_messages_from_file(language_message_file)
    return res


# Initialize resource strings
resource_strings = get_resource_messages()


def _read_resource_lists_from_file(language_list_file):
    """
    Read resource lists from a file.

    Args:
        language_list_file (str): Path to the language list file

    Returns:
        list: List of resource lists
    """
    import os
    import codecs

    global PLANET_NAMES, NAKSHATRA_LIST, TITHI_LIST, RAASI_LIST, KARANA_LIST, DAYS_LIST, PAKSHA_LIST, YOGAM_LIST, MONTH_LIST, YEAR_LIST, DHASA_LIST, BHUKTHI_LIST, PLANET_SHORT_NAMES, RAASI_SHORT_LIST
    global SHADVARGAMSA_NAMES, SAPTAVARGAMSA_NAMES, DHASAVARGAMSA_NAMES, SHODASAVARGAMSA_NAMES
    global SEASON_NAMES

    if not os.path.exists(language_list_file):
        print(f'Error: input file: {language_list_file} does not exist. Script aborted.')
        exit()

    with codecs.open(language_list_file, encoding='utf-8', mode='r') as fp:
        # Read and process each line
        def read_next_line():
            line = fp.readline().strip().replace('\n', '')
            line = line.replace("\r", "").rstrip()
            # Skip comment lines
            if line and line.lstrip()[0] == '#':
                line = fp.readline().strip().replace('\n', '')
                line = line.replace("\r", "").rstrip()
            return line

        # Read planet names
        line = read_next_line()
        PLANET_NAMES = line.rstrip('\n').split(',')
        # For tropical mode Rahu and Ketu are excluded, Uranus, Neptune and Pluto are included
        if const._TROPICAL_MODE:
            PLANET_NAMES = PLANET_NAMES[:7] + PLANET_NAMES[9:]

        # Read other lists
        line = read_next_line()
        RAASI_LIST = line.rstrip('\n').split(',')

        line = read_next_line()
        NAKSHATRA_LIST = line.rstrip('\n').split(',')

        line = read_next_line()
        TITHI_LIST = line.rstrip('\n').split(',')

        line = read_next_line()
        KARANA_LIST = line.rstrip('\n').split(',')

        line = read_next_line()
        DAYS_LIST = line.rstrip('\n').split(',')

        line = read_next_line()
        PAKSHA_LIST = line.rstrip('\n').split(',')

        line = read_next_line()
        YOGAM_LIST = line.rstrip('\n').split(',')

        line = read_next_line()
        MONTH_LIST = line.rstrip('\n').split(',')

        line = read_next_line()
        YEAR_LIST = line.rstrip('\n').split(',')

        line = read_next_line()
        DHASA_LIST = line.rstrip('\n').split(',')

        line = read_next_line()
        BHUKTHI_LIST = line.rstrip('\n').split(',')

        line = read_next_line()
        PLANET_SHORT_NAMES = line.rstrip('\n').split(',')

        line = read_next_line()
        RAASI_SHORT_LIST = line.rstrip('\n').split(',')

        line = read_next_line()
        SHADVARGAMSA_NAMES = line.rstrip('\n').split(',')

        line = read_next_line()
        SAPTAVARGAMSA_NAMES = line.rstrip('\n').split(',')

        line = read_next_line()
        DHASAVARGAMSA_NAMES = line.rstrip('\n').split(',')

        line = read_next_line()
        SHODASAVARGAMSA_NAMES = line.rstrip('\n').split(',')

        line = read_next_line()
        SEASON_NAMES = line.rstrip('\n').split(',')

    return [PLANET_NAMES, NAKSHATRA_LIST, TITHI_LIST, RAASI_LIST, KARANA_LIST, DAYS_LIST, PAKSHA_LIST, YOGAM_LIST, \
            MONTH_LIST, YEAR_LIST, DHASA_LIST, BHUKTHI_LIST, PLANET_SHORT_NAMES, RAASI_SHORT_LIST, SHADVARGAMSA_NAMES, \
            SAPTAVARGAMSA_NAMES, DHASAVARGAMSA_NAMES, SHODASAVARGAMSA_NAMES, SEASON_NAMES]


def get_resource_lists(language_list_file=None):
    """
    Retrieve resource lists from language-specific resource list file.

    Args:
        language_list_file (str): Path to the language list file
            Default: const._LANGUAGE_PATH + 'list_values_' + const._DEFAULT_LANGUAGE + '.txt'

    Returns:
        list: List of resource lists containing:
            [PLANET_NAMES, NAKSHATRA_LIST, TITHI_LIST, RAASI_LIST, KARANA_LIST, DAYS_LIST, PAKSHA_LIST,
             YOGAM_LIST, MONTH_LIST, YEAR_LIST, DHASA_LIST, BHUKTHI_LIST, PLANET_SHORT_NAMES, RAASI_SHORT_LIST,
             SHADVARGAMSA_NAMES, SAPTAVARGAMSA_NAMES, DHASAVARGAMSA_NAMES, SHODASAVARGAMSA_NAMES, SEASON_NAMES]
    """
    if language_list_file is None:
        language_list_file = const._LANGUAGE_PATH + const._DEFAULT_LANGUAGE_LIST_STR + const._DEFAULT_LANGUAGE + '.txt'

    return _read_resource_lists_from_file(language_list_file)


# Helper functions for formatting degrees, minutes, seconds
from_dms_to_str = lambda dms_list: str(dms_list[0]) + const._degree_symbol + str(
    dms_list[1]) + const._minute_symbol + str(dms_list[2]) + const._second_symbol


def _to_dms_old(deg):
    """Legacy function for converting degrees to DMS."""
    d, m, s = to_dms_prec(deg)
    return [d, m, int(s)]


def _function(point):
    """Internal function for astrological calculations."""
    swe.set_sid_mode(swe.SIDM_USER, point, 0.0)
    # Place Citra at 180°
    fval = swe.fixstar_ut("Citra", point, flag=swe.FLG_SWIEPH | swe.FLG_SIDEREAL)[0] - (180)
    return fval


def _bisection_search(func, start, stop):
    """Bisection search algorithm for finding roots of functions."""
    left = start
    right = stop
    epsilon = 5E-10  # Precision limit

    while True:
        middle = (left + right) / 2
        midval = func(middle)
        rtval = func(right)
        if midval * rtval >= 0:
            right = middle
        else:
            left = middle

        if (right - left) <= epsilon:
            break

    return (right + left) / 2


def inverse_lagrange(x, y, ya):
    """Given two lists x and y, find the value of x = xa when y = ya, i.e., f(xa) = ya"""
    assert (len(x) == len(y))
    total = 0
    for i in range(len(x)):
        numer = 1
        denom = 1
        for j in range(len(x)):
            if j != i:
                numer *= (ya - y[j])
                denom *= (y[i] - y[j])

        total += numer * x[i] / denom

    return total


def newton_polynomial(x_data, y_data, x):
    """
    Evaluate Newton's polynomial at point x.

    Args:
        x_data: Data points at x
        y_data: Data points at y
        x: Evaluation point(s)

    Returns:
        float: Interpolated value at x
    """
    def _poly_newton_coefficient(x, y):
        """Calculate Newton polynomial coefficients."""
        m = len(x)
        x = np.copy(x)
        a = np.copy(y)
        for k in range(1, m):
            a[k:m] = (a[k:m] - a[k - 1]) / (x[k:m] - x[k - 1])
        return a

    a = _poly_newton_coefficient(x_data, y_data)
    n = len(x_data) - 1  # Degree of polynomial
    p = a[n]

    for k in range(1, n + 1):
        p = a[n - k] + (x - x_data[n - k]) * p

    return p


def julian_day_number_new(date_of_birth_as_tuple, time_of_birth_as_tuple):
    """Alternative method to calculate Julian day number."""
    y, m, d = date_of_birth_as_tuple
    h, mm, s = time_of_birth_as_tuple
    jdn = d + int((153 * m + 2) / 5) + 365 * y + int(y / 4) - int(y / 100) + int(y / 400) - 32045
    jdt = h + mm / 60 + s / 3600
    jd = jdn + (jdt - 12) / 24, 3
    return jd


def _convert_to_tamil_date_and_time(panchanga_date, time_of_day_in_hours, place=None):
    """
    Convert to Tamil date and time.

    Args:
        panchanga_date: Panchanga date
        time_of_day_in_hours (float): Time of day in hours
        place: Place object with timezone attribute

    Returns:
        tuple: (panchanga_date, time_of_day_in_hours)
    """
    # Handle time overflow/underflow
    extra_days = 0
    sign = 1

    if time_of_day_in_hours < 0:
        extra_days = int(abs(time_of_day_in_hours / 24)) + 1
        sign = -1
    elif time_of_day_in_hours > 24:
        extra_days = int(abs(time_of_day_in_hours / 24))
        sign = 1

    time_of_day_in_hours += - sign * extra_days * 24

    # Adjust date if needed
    if extra_days != 0:
        panchanga_date = next_panchanga_day(panchanga_date, add_days=sign * extra_days)

    # If solar time > sunset time, move to next day
    if place is not None:
        jd = gregorian_to_jd(panchanga_date)
        sunset_jd = drig_panchanga.sunset(jd, place)[0] - (place.timezone / 24.)
        sunset_time = from_dms_str_to_degrees(drig_panchanga.sunset(sunset_jd, place)[1])
        if sunset_time < time_of_day_in_hours:
            panchanga_date = next_panchanga_day(panchanga_date, add_days=1)

    return panchanga_date, time_of_day_in_hours


def panchanga_time_delta(panchanga_date1, panchanga_date2):
    """
    Calculate time delta between two panchanga dates in days.

    Args:
        panchanga_date1: First panchanga date
        panchanga_date2: Second panchanga date

    Returns:
        float: Difference in days
    """
    np_date1 = np.datetime64(panchanga_date1)
    np_date2 = np.datetime64(panchanga_date2)
    diff_days = (np_date1 - np_date2) / np.timedelta64(1, "D")
    return diff_days


def panchanga_date_to_tuple(panchanga_date):
    """
    Convert panchanga date to tuple.

    Args:
        panchanga_date: Panchanga date

    Returns:
        tuple: (year, month, day)
    """
    return panchanga_date[0], panchanga_date[1], panchanga_date[2]


def _solar_mean_motion_since_1900(days_since_1900):
    """Not working - placeholder for future implementation."""
    i_d = int(days_since_1900)
    f_d = days_since_1900 - i_d
    i_d_s = str(i_d)
    l = len(i_d_s)
    for i, c in enumerate(i_d_s):
        c1 = int(c)
        if c1 != 0:
            lng = const.mean_solar_daily_motions_table_from_1900[c1 - 1][i]


def udhayadhi_nazhikai(jd, place):
    """
    Calculate Udhayadhi Nazhikai (time since sunrise).

    Args:
        jd (float): Julian day number
        place: Place object with timezone attribute

    Returns:
        list: [nazhikai_string, nazhikai_decimal]
    """
    import math

    _, _, _, birth_time_hrs = jd_to_gregorian(jd)
    sunrise_time_in_float_hours = drig_panchanga.sunrise(jd, place)[0]

    # If birth time is before sunrise, use previous day's sunrise
    time_diff = birth_time_hrs - sunrise_time_in_float_hours
    if birth_time_hrs < sunrise_time_in_float_hours:
        sunrise_time_in_float_hours = drig_panchanga.sunrise(jd - 1, place)[0]
        time_diff = 24.0 + birth_time_hrs - sunrise_time_in_float_hours

    hours, minutes, seconds = to_dms(time_diff, as_string=False)
    tharparai1 = (int(hours)) * 9000 + int(minutes) * 150 + int(seconds)
    naazhigai = math.floor(tharparai1 / 3600)
    vinadigal = math.floor((tharparai1 - (naazhigai * 3600)) / 60)
    tharparai = math.floor(tharparai1 - naazhigai * 3600 - vinadigal * 60)

    return [f"{naazhigai}:{vinadigal}:{tharparai}", tharparai1 / 3600.0]

if __name__ == "__main__":
    # This section is for testing and demonstration purposes only
    # It will not be executed when the module is imported

    print("Fortune Lens Utilities Module")
    print("This module provides utility functions for the Fortune Lens application.")
    print("Import this module to use its functions in your code.")
