"""
Chart Constants

This module contains constants related to astrological charts.
"""

# Chart types
class ChartType:
    D1 = 1
    D2 = 2
    D3 = 3
    D4 = 4
    D5 = 5
    D6 = 6
    D7 = 7
    D8 = 8
    D9 = 9
    D10 = 10
    D11 = 11
    D12 = 12
    D16 = 16
    D20 = 20
    D24 = 24
    D27 = 27
    D30 = 30
    D40 = 40
    D45 = 45
    D60 = 60
    D81 = 81
    D108 = 108
    D144 = 144

# Chart names
class ChartName:
    D1 = 'Rasi Chart'
    D2 = 'Hora Chart'
    D3 = 'Drekkana Chart'
    D4 = 'Chaturthamsa Chart'
    D5 = 'Panchamsa Chart'
    D6 = 'Shashthamsa Chart'
    D7 = 'Saptamsa Chart'
    D8 = 'Ashtamsa Chart'
    D9 = 'Navamsa Chart'
    D10 = 'Dasamsa Chart'
    D11 = 'Rudramsa Chart'
    D12 = 'Dwadasamsa Chart'
    D16 = 'Shodasamsa Chart'
    D20 = 'Vimsamsa Chart'
    D24 = 'Chaturvimsamsa Chart'
    D27 = 'Nakshatramsa Chart'
    D30 = 'Trimsamsa Chart'
    D40 = 'Khavedamsa Chart'
    D45 = 'Akshavedamsa Chart'
    D60 = 'Shashtyamsa Chart'
    D81 = 'Nava Navamsa Chart'
    D108 = 'Ashtotharamsa Chart'
    D144 = 'Dwadas Dwadasamsa Chart'

# Chart display formats
class ChartFormat:
    SOUTH_INDIAN = 'south indian'
    NORTH_INDIAN = 'north indian'
    EAST_INDIAN = 'east indian'
    WESTERN = 'western'
    SUDARSANA_CHAKRA = 'sudarsana chakra'

# Planet names
class Planet:
    SUN = 'sun'
    MOON = 'moon'
    MARS = 'mars'
    MERCURY = 'mercury'
    JUPITER = 'jupiter'
    VENUS = 'venus'
    SATURN = 'saturn'
    RAHU = 'rahu'
    KETU = 'ketu'
    LAGNAM = 'lagnam'

# Zodiac signs (Rasis)
class Rasi:
    ARIES = 'Mesham'
    TAURUS = 'Rishabam'
    GEMINI = 'Midunam'
    CANCER = 'Kadagam'
    LEO = 'Simmam'
    VIRGO = 'Kanni'
    LIBRA = 'Thulam'
    SCORPIO = 'Virichigam'
    SAGITTARIUS = 'Dhanusu'
    CAPRICORN = 'MAGARAM'
    AQUARIUS = 'Kumbam'
    PISCES = 'Meenam'

# Nakshatras (Lunar mansions)
class Nakshatra:
    ASHWINI = 'ASHWINI'
    BARANI = 'BARANI'
    KARTHIKAI = 'KARTHIKAI'
    ROHINI = 'ROHINI'
    MIRIGASIRISHAM = 'MIRIGASIRISHAM'
    THIRUVADIRAI = 'THIRUVADIRAI'
    PUNARPOOSAM = 'PUNARPOOSAM'
    POOSAM = 'POOSAM'
    AYILYAM = 'AYILYAM'
    MAGAM = 'MAGAM'
    POORAM = 'POORAM'
    UTHIRAM = 'UTHIRAM'
    HASTHAM = 'HASTHAM'
    CHITHIRAI = 'CHITHIRAI'
    SWATHI = 'SWATHI'
    VISAGAM = 'VISAGAM'
    ANUSHAM = 'ANUSHAM'
    KETTAI = 'KETTAI'
    MOOLAM = 'MOOLAM'
    POORADAM = 'POORADAM'
    UTHIRADAM = 'UTHIRADAM'
    THIRUVONAM = 'THIRUVONAM'
    AVITTAM = 'AVITTAM'
    SADAYAM = 'SADAYAM'
    POORATADHI = 'POORATADHI'
    UTHIRATTADHI = 'UTHIRATTADHI'
    REVATHI = 'REVATHI'

# Lists for easy access
PLANET_LIST = [Planet.SUN, Planet.MOON, Planet.MARS, Planet.MERCURY, 
               Planet.JUPITER, Planet.VENUS, Planet.SATURN, Planet.RAHU, Planet.KETU]

RASI_LIST = [Rasi.ARIES, Rasi.TAURUS, Rasi.GEMINI, Rasi.CANCER, 
             Rasi.LEO, Rasi.VIRGO, Rasi.LIBRA, Rasi.SCORPIO, 
             Rasi.SAGITTARIUS, Rasi.CAPRICORN, Rasi.AQUARIUS, Rasi.PISCES]

NAKSHATRA_LIST = [Nakshatra.ASHWINI, Nakshatra.BARANI, Nakshatra.KARTHIKAI, 
                  Nakshatra.ROHINI, Nakshatra.MIRIGASIRISHAM, Nakshatra.THIRUVADIRAI,
                  Nakshatra.PUNARPOOSAM, Nakshatra.POOSAM, Nakshatra.AYILYAM, 
                  Nakshatra.MAGAM, Nakshatra.POORAM, Nakshatra.UTHIRAM, 
                  Nakshatra.HASTHAM, Nakshatra.CHITHIRAI, Nakshatra.SWATHI, 
                  Nakshatra.VISAGAM, Nakshatra.ANUSHAM, Nakshatra.KETTAI, 
                  Nakshatra.MOOLAM, Nakshatra.POORADAM, Nakshatra.UTHIRADAM, 
                  Nakshatra.THIRUVONAM, Nakshatra.AVITTAM, Nakshatra.SADAYAM, 
                  Nakshatra.POORATADHI, Nakshatra.UTHIRATTADHI, Nakshatra.REVATHI]

# Chart factors
DIVISION_CHART_FACTORS = [
    ChartType.D1, ChartType.D2, ChartType.D3, ChartType.D4, ChartType.D5, 
    ChartType.D6, ChartType.D7, ChartType.D8, ChartType.D9, ChartType.D10, 
    ChartType.D11, ChartType.D12, ChartType.D16, ChartType.D20, ChartType.D24, 
    ChartType.D27, ChartType.D30, ChartType.D40, ChartType.D45, ChartType.D60, 
    ChartType.D81, ChartType.D108, ChartType.D144
]

# Chart display formats
AVAILABLE_CHART_FORMATS = [
    ChartFormat.SOUTH_INDIAN, 
    ChartFormat.NORTH_INDIAN, 
    ChartFormat.EAST_INDIAN, 
    ChartFormat.WESTERN, 
    ChartFormat.SUDARSANA_CHAKRA
]

# Mapping of chart types to names
CHART_TYPE_NAMES = {
    ChartType.D1: ChartName.D1,
    ChartType.D2: ChartName.D2,
    ChartType.D3: ChartName.D3,
    ChartType.D4: ChartName.D4,
    ChartType.D5: ChartName.D5,
    ChartType.D6: ChartName.D6,
    ChartType.D7: ChartName.D7,
    ChartType.D8: ChartName.D8,
    ChartType.D9: ChartName.D9,
    ChartType.D10: ChartName.D10,
    ChartType.D11: ChartName.D11,
    ChartType.D12: ChartName.D12,
    ChartType.D16: ChartName.D16,
    ChartType.D20: ChartName.D20,
    ChartType.D24: ChartName.D24,
    ChartType.D27: ChartName.D27,
    ChartType.D30: ChartName.D30,
    ChartType.D40: ChartName.D40,
    ChartType.D45: ChartName.D45,
    ChartType.D60: ChartName.D60,
    ChartType.D81: ChartName.D81,
    ChartType.D108: ChartName.D108,
    ChartType.D144: ChartName.D144
}
