"""
Member Profile Service
"""

import random
import string
from datetime import datetime
from bson import ObjectId

from ..extensions import mongo
from ..config import BaseConfiguration
from .astro_generator import generate_astro_data
from .new_sequence_service import NewSequenceService
from .chart_service import generate_chart
from ..constants import Collection, Field


class MemberProfileService:
    """Member Profile Service"""

    @staticmethod
    def generate_unique_key(length=10):
        """
        Generate a random unique key

        Args:
            length (int): Length of the key

        Returns:
            str: Random unique key
        """
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(length))

    @staticmethod
    def create_member_profile(user_id, data):
        """
        Create a new member profile

        Args:
            user_id (str): User ID of the creator
            data (dict): Member profile data

        Returns:
            dict: Created member profile document
        """
        # Generate a unique key for joining collections
        unique_key = MemberProfileService.generate_unique_key()

        # Get user profile to get the user_profile_id
        user = mongo.db[BaseConfiguration.MONGO_USER_COLLECTION].find_one({'_id': ObjectId(user_id)})
        if not user:
            raise ValueError(f'User with ID {user_id} not found')

        # Check if this is the first member for this user
        existing_members = list(mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find({'user_profile_id': user.get('user_profile_id')}))

        # If this is the first member, use member_profile_id = 1
        # Otherwise, find the maximum member_profile_id for this user and increment it
        if not existing_members:
            member_profile_id = 1
        else:
            # Find the maximum member_profile_id for this user
            max_member = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one(
                {'user_profile_id': user.get('user_profile_id')},
                sort=[('member_profile_id', -1)]
            )
            if max_member and 'member_profile_id' in max_member:
                member_profile_id = max_member['member_profile_id'] + 1
            else:
                member_profile_id = 1

        # Create member profile document
        profile = {
            '_id': NewSequenceService.get_new_object_id(),  # Using ObjectId for _id field
            Field.MEMBER_PROFILE_ID: member_profile_id,  # Sequential ID for member_profile_id
            'user_profile_id': user.get('user_profile_id'),  # Use the user's user_profile_id
            'unique_key': unique_key,  # Add unique key for joining
            'name': data.get('name'),
            'relation': data.get('relation'),
            'birth_date': data.get('birth_date'),
            'birth_time': data.get('birth_time'),
            'birth_place': data.get('birth_place'),
            'state': data.get('state', ''),
            'country': data.get('country', ''),
            'latitude': data.get('latitude'),
            'longitude': data.get('longitude'),
            'gender': data.get('gender'),
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }

        # Insert profile into database
        result = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].insert_one(profile)

        # Also add this profile to the astro data collection with generated astro data
        try:
            astro_profile = dict(profile)
            astro_profile['_id'] = NewSequenceService.get_new_object_id()  # Use ObjectId for _id field
            astro_profile['astro_data'] = generate_astro_data(profile)
        except Exception as e:
            # Log error but continue
            pass

        # Generate chart data
        birth_data = {
            'user_birthdate': profile.get('birth_date'),
            'user_birthtime': profile.get('birth_time'),
            'user_birthplace': profile.get('birth_place'),
            'user_state': profile.get('state', ''),
            'user_country': profile.get('country', '')
        }

        try:
            # Generate chart data with all 23 divisional charts
            chart_data = generate_chart(birth_data)

            # Add chart data to astro profile
            astro_profile['chart_data'] = chart_data
        except Exception as e:
            print(f"Error generating chart data: {str(e)}")
            # Create dummy chart data with all 23 divisional charts
            chart_data = {}

            # Define all divisional chart factors and their descriptions
            divisional_chart_info = {
                1: {'name': 'Rasi Chart', 'description': 'Basic birth chart showing planetary positions at birth'},
                2: {'name': 'Hora Chart', 'description': 'Chart of wealth and prosperity'},
                3: {'name': 'Drekkana Chart', 'description': 'Chart of siblings and courage'},
                4: {'name': 'Chaturthamsa Chart', 'description': 'Chart of fortune and property'},
                5: {'name': 'Panchamsa Chart', 'description': 'Chart of spiritual merits from past lives'},
                6: {'name': 'Shashthamsa Chart', 'description': 'Chart of health, disease, and enemies'},
                7: {'name': 'Saptamsa Chart', 'description': 'Chart of children and progeny'},
                8: {'name': 'Ashtamsa Chart', 'description': 'Chart of unexpected events and obstacles'},
                9: {'name': 'Navamsa Chart', 'description': 'Chart of spouse and general fortune'},
                10: {'name': 'Dasamsa Chart', 'description': 'Chart of career and professional life'},
                11: {'name': 'Rudramsa Chart', 'description': 'Chart of gains and fulfillment of desires'},
                12: {'name': 'Dwadasamsa Chart', 'description': 'Chart of parents and ancestry'},
                16: {'name': 'Shodasamsa Chart', 'description': 'Chart of vehicles and comforts'},
                20: {'name': 'Vimsamsa Chart', 'description': 'Chart of spiritual pursuits and worship'},
                24: {'name': 'Chaturvimsamsa Chart', 'description': 'Chart of education and learning'},
                27: {'name': 'Nakshatramsa Chart', 'description': 'Chart of strengths and weaknesses'},
                30: {'name': 'Trimsamsa Chart', 'description': 'Chart of misfortunes and challenges'},
                40: {'name': 'Khavedamsa Chart', 'description': 'Chart of auspicious and inauspicious effects'},
                45: {'name': 'Akshavedamsa Chart', 'description': 'Chart of general indications'},
                60: {'name': 'Shashtyamsa Chart', 'description': 'Chart of overall life indications'},
                81: {'name': 'Nava Navamsa Chart', 'description': 'Chart of hidden aspects'},
                108: {'name': 'Ashtotharamsa Chart', 'description': 'Chart of specific spiritual indications'},
                144: {'name': 'Dwadas Dwadasamsa Chart', 'description': 'Chart of final conclusions'}
            }

            # Create dummy chart data for all divisional charts
            for factor, info in divisional_chart_info.items():
                chart_key = f'D{factor}'
                chart_data[chart_key] = {
                    'chart_info': {
                        'name': info['name'],
                        'description': info['description'],
                        'divisional_factor': factor
                    },
                    'houses': [],
                    'planets': []
                }

            # Add chart data to astro profile
            astro_profile['chart_data'] = chart_data

        # Save the astro profile to the database
        mongo.db[BaseConfiguration.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].insert_one(astro_profile)

        # Convert ObjectId to string for response
        profile['_id'] = str(profile['_id'])
        profile['user_profile_id'] = str(profile['user_profile_id'])

        return profile

    @staticmethod
    def get_member_profile_by_id(profile_id):
        """
        Get member profile by ID

        Args:
            profile_id (str): Profile ID

        Returns:
            dict: Member profile document if found, None otherwise
        """
        try:
            # Try to find by integer ID first
            if isinstance(profile_id, int):
                profile = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one({'_id': profile_id})
            else:
                # If not an integer, try ObjectId
                profile = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one({'_id': ObjectId(profile_id)})

            if profile:
                # Convert ObjectId to string
                profile['_id'] = str(profile['_id'])
                profile['user_profile_id'] = str(profile['user_profile_id'])

            return profile
        except:
            return None

    @staticmethod
    def get_member_profiles_by_user_id(user_id):
        """
        Get member profiles by user ID

        Args:
            user_id (str): User ID

        Returns:
            list: List of member profile documents
        """
        try:
            # Try to find by string ID first
            profiles = list(mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find({'user_profile_id': str(user_id)}))

            # If no profiles found, try with ObjectId
            if not profiles:
                try:
                    profiles = list(mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find({'user_profile_id': ObjectId(user_id)}))
                except:
                    pass

            # Convert ObjectId to string and ensure member_profile_id is present
            for profile in profiles:
                profile['_id'] = str(profile['_id'])
                profile['user_profile_id'] = str(profile['user_profile_id'])
                # Add member_profile_id if not present
                if Field.MEMBER_PROFILE_ID not in profile:
                    profile[Field.MEMBER_PROFILE_ID] = profile['_id']

            return profiles
        except Exception as e:
            # Log error and return empty list
            return []

    @staticmethod
    def update_member_profile(profile_id, data):
        """
        Update member profile

        Args:
            profile_id (str): Profile ID
            data (dict): Member profile data to update

        Returns:
            dict: Updated member profile document
        """
        # Prepare update data
        update_data = {
            'updated_at': datetime.utcnow()
        }

        # Add fields to update
        for field in ['name', 'relation', 'birth_date', 'birth_time', 'birth_place', 'latitude', 'longitude', 'gender']:
            if field in data:
                update_data[field] = data[field]

        # Update profile
        try:
            # Try to update by integer ID first
            if isinstance(profile_id, int) or (isinstance(profile_id, str) and profile_id.isdigit()):
                profile_id = int(profile_id)
                mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].update_one(
                    {'_id': profile_id},
                    {'$set': update_data}
                )
                # Get updated profile
                profile = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one({'_id': profile_id})
            else:
                # If not an integer, try ObjectId
                mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].update_one(
                    {'_id': ObjectId(profile_id)},
                    {'$set': update_data}
                )
                # Get updated profile
                profile = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one({'_id': ObjectId(profile_id)})
        except Exception as e:
            # Log error and return None
            return None

        # Also update the astro data collection
        astro_profile = mongo.db[BaseConfiguration.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].find_one({'unique_key': profile['unique_key']})
        if astro_profile:
            update_data['astro_data'] = generate_astro_data(profile)
            mongo.db[BaseConfiguration.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].update_one(
                {'_id': astro_profile['_id']},
                {'$set': update_data}
            )

        # Convert ObjectId to string and ensure member_profile_id is present
        profile['_id'] = str(profile['_id'])
        profile['user_profile_id'] = str(profile['user_profile_id'])
        # Add member_profile_id if not present
        if Field.MEMBER_PROFILE_ID not in profile:
            profile[Field.MEMBER_PROFILE_ID] = profile['_id']

        return profile

    @staticmethod
    def delete_member_profile(profile_id):
        """
        Delete member profile

        Args:
            profile_id (str): Profile ID

        Returns:
            bool: True if profile deleted, False otherwise
        """
        # Get profile to get the unique key
        profile = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one({'_id': ObjectId(profile_id)})
        if profile:
            # Delete from member profile collection
            result = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].delete_one({'_id': ObjectId(profile_id)})

            # Also delete from astro data collection
            mongo.db[BaseConfiguration.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].delete_one({'unique_key': profile['unique_key']})

            return result.deleted_count > 0
        return False


    @staticmethod
    def get_user_as_member_profile(user_id):
        """
        Get user's own profile as a member profile

        Args:
            user_id (str): User ID

        Returns:
            dict: User's member profile document
        """
        # Get user data
        user = mongo.db[BaseConfiguration.MONGO_USER_COLLECTION].find_one({'_id': ObjectId(user_id)})

        if not user:
            return None

        # Check if user already has a self-profile
        existing_profile = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one({
            'user_profile_id': ObjectId(user_id),
            'relation': 'self'
        })

        if existing_profile:
            # Convert ObjectId to string
            existing_profile['_id'] = str(existing_profile['_id'])
            existing_profile['user_profile_id'] = str(existing_profile['user_profile_id'])
            return existing_profile

        # Generate a unique key for joining collections
        unique_key = MemberProfileService.generate_unique_key()

        # Get user profile to get the user_profile_id
        user = mongo.db[BaseConfiguration.MONGO_USER_COLLECTION].find_one({'_id': ObjectId(user_id)})
        if not user:
            raise ValueError(f'User with ID {user_id} not found')

        # Check if this is the first member for this user
        existing_members = list(mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find({'user_profile_id': user.get('user_profile_id')}))

        # If this is the first member, use member_profile_id = 1
        # Otherwise, find the maximum member_profile_id for this user and increment it
        if not existing_members:
            member_profile_id = 1
        else:
            # Find the maximum member_profile_id for this user
            max_member = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one(
                {'user_profile_id': user.get('user_profile_id')},
                sort=[('member_profile_id', -1)]
            )
            if max_member and 'member_profile_id' in max_member:
                member_profile_id = max_member['member_profile_id'] + 1
            else:
                member_profile_id = 1

        # Create a new self-profile
        profile = {
            '_id': NewSequenceService.get_new_object_id(),  # Using ObjectId for _id field
            Field.MEMBER_PROFILE_ID: member_profile_id,  # Sequential ID for member_profile_id
            'user_profile_id': user.get('user_profile_id'),  # Use the user's user_profile_id
            'unique_key': unique_key,  # Add unique key for joining
            'name': user.get('name'),
            'relation': 'self',
            'birth_date': None,
            'birth_time': None,
            'birth_place': None,
            'latitude': None,
            'longitude': None,
            'gender': None,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }

        # Update the user document with the same unique key
        mongo.db[BaseConfiguration.MONGO_USER_PROFILE_COLLECTION].update_one(
            {'_id': ObjectId(user_id)},
            {'$set': {'profile_key': unique_key}}
        )

        # Also add this profile to the astro data collection
        astro_profile = dict(profile)
        astro_profile['_id'] = NewSequenceService.get_new_object_id()  # Generate a new ID
        astro_profile['astro_data'] = generate_astro_data(profile)
        mongo.db[BaseConfiguration.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].insert_one(astro_profile)

        # Insert profile into database
        result = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].insert_one(profile)
        profile['_id'] = result.inserted_id

        # Convert ObjectId to string for response and ensure member_profile_id is present
        profile['_id'] = str(profile['_id'])
        profile['user_profile_id'] = str(profile['user_profile_id'])
        # Add member_profile_id if not present
        if Field.MEMBER_PROFILE_ID not in profile:
            profile[Field.MEMBER_PROFILE_ID] = profile['_id']

        return profile

    @staticmethod
    def create_member_profile_with_charts(user_id, member_data):
        """
        Create a member profile and automatically generate astrological data

        This function handles the complete workflow:
        1. Creates a member profile with the user_id
        2. Generates astrological charts for the member
        3. Stores the chart data in user_member_astro_profile_data collection

        Args:
            user_id (str): ID of the user creating the member profile
            member_data (dict): Member profile data including birth details

        Returns:
            dict: Result with success status, message, and created profile
        """
        try:
            # Validate required fields
            required_fields = [
                'member_name', 'member_gender', 'member_relation',
                'user_birthdate', 'user_birthtime', 'user_birthplace',
                'user_state', 'user_country'
            ]

            for field in required_fields:
                if field not in member_data:
                    return {'success': False, 'message': f'Missing required field: {field}'}

            # Get user profile to get the user_profile_id
            user = mongo.db[BaseConfiguration.MONGO_USER_COLLECTION].find_one({'_id': ObjectId(user_id)})
            if not user:
                return {'success': False, 'message': f'User with ID {user_id} not found'}

            # Check if this is the first member for this user
            existing_members = list(mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find({'user_profile_id': user.get('user_profile_id')}))

            # If this is the first member, use member_profile_id = 1
            # Otherwise, find the maximum member_profile_id for this user and increment it
            if not existing_members:
                member_profile_id = 1
            else:
                # Find the maximum member_profile_id for this user
                max_member = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one(
                    {'user_profile_id': user.get('user_profile_id')},
                    sort=[('member_profile_id', -1)]
                )
                if max_member and 'member_profile_id' in max_member:
                    member_profile_id = max_member['member_profile_id'] + 1
                else:
                    member_profile_id = 1
            unique_key = MemberProfileService.generate_unique_key()

            # Create member profile document
            member_profile = {
                '_id': NewSequenceService.get_new_object_id(),  # Using ObjectId for _id field
                Field.MEMBER_PROFILE_ID: member_profile_id,  # Sequential ID for member_profile_id
                'user_profile_id': user.get('user_profile_id'),  # Use the user's user_profile_id
                'unique_key': unique_key,
                'name': member_data['member_name'],
                'relation': member_data['member_relation'],
                'gender': member_data['member_gender'],
                'birth_date': member_data['user_birthdate'],
                'birth_time': member_data['user_birthtime'],
                'birth_place': member_data['user_birthplace'],
                'state': member_data['user_state'],
                'country': member_data['user_country'],
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            }

            # Insert member profile
            mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].insert_one(member_profile)

            # Generate astrological charts
            try:
                chart_data = generate_chart({
                    'user_birthdate': member_data['user_birthdate'],
                    'user_birthtime': member_data['user_birthtime'],
                    'user_birthplace': member_data['user_birthplace'],
                    'user_state': member_data['user_state'],
                    'user_country': member_data['user_country']
                })

                # Chart data generated successfully
            except Exception as e:
                # Log error but continue with empty chart data
                chart_data = None
            # print(chart_data)
            # Create astro profile document
            astro_profile = {'user_profile_id': member_profile['user_profile_id'], 'member_profile_id': member_profile[Field.MEMBER_PROFILE_ID], 'unique_key': unique_key,
                             'name': member_data['member_name'], 'relation': member_data['member_relation'],
                             'gender': member_data['member_gender'], 'birth_date': member_data['user_birthdate'],
                             'birth_time': member_data['user_birthtime'], 'birth_place': member_data['user_birthplace'],
                             'state': member_data['user_state'], 'country': member_data['user_country'],
                             'created_at': datetime.utcnow(), 'updated_at': datetime.utcnow(),
                             '_id': NewSequenceService.get_new_object_id(), 'chart_data': chart_data}
            db = mongo.db[BaseConfiguration.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].insert_one(astro_profile)
            # chart = mongo.db[BaseConfiguration.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].insert_one(chart_data)
            # Astro profile saved successfully

            return {
                'success': True,
                'message': 'Member profile and astrological data created successfully',
                'member_profile': member_profile,
                'profile_id': str(db)
            }

        except Exception as e:
            return {'success': False, 'message': str(e)}

    @staticmethod
    def get_member_profile_with_charts(user_id, profile_id):
        """
        Get member profile with all chart data

        Args:
            user_id (str): ID of the user requesting the profile
            profile_id (str): ID of the member profile

        Returns:
            dict: Result with success status, message, and profile data with charts
        """
        try:
            # Convert profile_id to int if it's a digit string
            if isinstance(profile_id, str) and profile_id.isdigit():
                profile_id = int(profile_id)
            elif isinstance(profile_id, str):
                profile_id = ObjectId(profile_id)

            # Get member profile
            member_profile = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one({'_id': profile_id})

            if not member_profile:
                return {'success': False, 'message': 'Member profile not found'}

            # Check if the profile belongs to the user
            if isinstance(user_id, str) and user_id.isdigit():
                user_id = int(user_id)
            elif isinstance(user_id, str):
                user_id = ObjectId(user_id)

            if member_profile['user_profile_id'] != user_id:
                return {'success': False, 'message': 'Unauthorized access to this profile'}

            # Generate chart data on-the-fly
            try:
                chart_data = generate_chart({
                    'user_birthdate': member_profile['birth_date'],
                    'user_birthtime': member_profile['birth_time'],
                    'user_birthplace': member_profile['birth_place'],
                    'user_state': member_profile['state'],
                    'user_country': member_profile['country']
                })
                # Chart data generated successfully
            except Exception as e:
                # Log error but continue with empty chart data
                chart_data = None

            # Convert ObjectId to string for response
            member_profile['_id'] = str(member_profile['_id'])
            if isinstance(member_profile['user_profile_id'], ObjectId):
                member_profile['user_profile_id'] = str(member_profile['user_profile_id'])

            return {
                'success': True,
                'message': 'Member profile and astrological data retrieved successfully',
                'data': {
                    'member_profile': member_profile,
                    'chart_data': chart_data
                }
            }

        except Exception as e:
            return {'success': False, 'message': str(e)}
