#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
Tamil Panchanga (Daily Panchanga) functionality for Fortune Lens application.
This module provides functions to calculate Tamil calendar dates and panchanga elements.
"""

import math
from datetime import datetime, timedelta
import calendar

# Define Tamil month names
TAMIL_MONTH_NAMES = [
    "Chi<PERSON><PERSON>",     # April-May
    "Vaikasi",       # May-June
    "Aani",          # June-July
    "Aadi",          # July-August
    "Aavani",        # August-September
    "Purattasi",     # September-October
    "Aippasi",       # October-November
    "Karthigai",     # November-December
    "Margazhi",      # December-January
    "Thai",          # January-February
    "Maasi",         # February-March
    "Panguni"        # March-April
]

# Define Tamil weekday names
TAMIL_WEEKDAY_NAMES = [
    "<PERSON>ya<PERSON>ru",      # Sunday
    "Thingal",      # Monday
    "Sevvai",       # Tuesday
    "Budhan",       # Wednesday
    "Viyazhan",     # Thursday
    "Velli",        # Friday
    "Sani"          # Saturday
]

# Define Tamil nakshatra names
TAMIL_NAKSHATRA_NAMES = [
    "<PERSON><PERSON><PERSON>",      # 1
    "<PERSON><PERSON><PERSON>",      # 2
    "<PERSON><PERSON><PERSON><PERSON>",    # 3
    "<PERSON><PERSON><PERSON>",       # 4
    "Mir<PERSON><PERSON><PERSON><PERSON>",# 5
    "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",# 6
    "<PERSON><PERSON><PERSON><PERSON><PERSON>",  # 7
    "<PERSON><PERSON><PERSON>",       # 8
    "Ayilyam",      # 9
    "Magam",        # 10
    "Pooram",       # 11
    "Uthiram",      # 12
    "<PERSON>tham",      # 13
    "Chithirai",    # 14
    "Swathi",       # 15
    "<PERSON>gam",      # 16
    "Anusham",      # 17
    "<PERSON>ttai",       # 18
    "Moolam",       # 19
    "Pooradam",     # 20
    "Uthiradam",    # 21
    "Thiruvonam",   # 22
    "Avittam",      # 23
    "Sadayam",      # 24
    "Poorattathi",  # 25
    "Uthirattathi", # 26
    "Revathi"       # 27
]

# Define Tamil tithi names
TAMIL_TITHI_NAMES = [
    "Prathamai",    # 1
    "Dvitiyai",     # 2
    "Tritiyai",     # 3
    "Chaturthi",    # 4
    "Panchami",     # 5
    "Shashti",      # 6
    "Saptami",      # 7
    "Ashtami",      # 8
    "Navami",       # 9
    "Dasami",       # 10
    "Ekadasi",      # 11
    "Dvadasi",      # 12
    "Trayodasi",    # 13
    "Chaturdasi",   # 14
    "Pournami",     # 15 (Full Moon)
    "Prathamai",    # 16
    "Dvitiyai",     # 17
    "Tritiyai",     # 18
    "Chaturthi",    # 19
    "Panchami",     # 20
    "Shashti",      # 21
    "Saptami",      # 22
    "Ashtami",      # 23
    "Navami",       # 24
    "Dasami",       # 25
    "Ekadasi",      # 26
    "Dvadasi",      # 27
    "Trayodasi",    # 28
    "Chaturdasi",   # 29
    "Amavasai"      # 30 (New Moon)
]

# Define Tamil yoga names
TAMIL_YOGA_NAMES = [
    "Vishkambha",   # 1
    "Priti",        # 2
    "Ayushman",     # 3
    "Saubhagya",    # 4
    "Sobhana",      # 5
    "Atiganda",     # 6
    "Sukarma",      # 7
    "Dhriti",       # 8
    "Shula",        # 9
    "Ganda",        # 10
    "Vriddhi",      # 11
    "Dhruva",       # 12
    "Vyaghata",     # 13
    "Harshana",     # 14
    "Vajra",        # 15
    "Siddhi",       # 16
    "Vyatipata",    # 17
    "Variyan",      # 18
    "Parigha",      # 19
    "Shiva",        # 20
    "Siddha",       # 21
    "Sadhya",       # 22
    "Shubha",       # 23
    "Shukla",       # 24
    "Brahma",       # 25
    "Indra",        # 26
    "Vaidhriti"     # 27
]

# Define Tamil karana names
TAMIL_KARANA_NAMES = [
    "Kimstughna",   # 1
    "Bava",         # 2
    "Balava",       # 3
    "Kaulava",      # 4
    "Taitila",      # 5
    "Gara",         # 6
    "Vanija",       # 7
    "Vishti",       # 8
    "Bava",         # 9
    "Balava",       # 10
    "Kaulava",      # 11
    "Taitila",      # 12
    "Gara",         # 13
    "Vanija",       # 14
    "Vishti",       # 15
    "Bava",         # 16
    "Balava",       # 17
    "Kaulava",      # 18
    "Taitila",      # 19
    "Gara",         # 20
    "Vanija",       # 21
    "Vishti",       # 22
    "Bava",         # 23
    "Balava",       # 24
    "Kaulava",      # 25
    "Taitila",      # 26
    "Gara",         # 27
    "Vanija",       # 28
    "Vishti",       # 29
    "Bava",         # 30
    "Balava",       # 31
    "Kaulava",      # 32
    "Taitila",      # 33
    "Gara",         # 34
    "Vanija",       # 35
    "Vishti",       # 36
    "Bava",         # 37
    "Balava",       # 38
    "Kaulava",      # 39
    "Taitila",      # 40
    "Gara",         # 41
    "Vanija",       # 42
    "Vishti",       # 43
    "Bava",         # 44
    "Balava",       # 45
    "Kaulava",      # 46
    "Taitila",      # 47
    "Gara",         # 48
    "Vanija",       # 49
    "Vishti",       # 50
    "Bava",         # 51
    "Balava",       # 52
    "Kaulava",      # 53
    "Taitila",      # 54
    "Gara",         # 55
    "Vanija",       # 56
    "Vishti",       # 57
    "Shakuni",      # 58
    "Chatushpada",  # 59
    "Naga"          # 60
]

def get_tamil_date(date=None, lat=13.0878, lon=80.2785, tz=5.5):
    """
    Get the Tamil date for the given Gregorian date.
    This is a simplified version that approximates the Tamil solar calendar.

    Args:
        date (datetime, optional): Gregorian date. Defaults to current date.
        lat (float, optional): Latitude. Defaults to Chennai latitude.
        lon (float, optional): Longitude. Defaults to Chennai longitude.
        tz (float, optional): Timezone offset. Defaults to IST.

    Returns:
        dict: Tamil date information
    """
    if date is None:
        date = datetime.now()

    # Tamil solar calendar approximately starts on April 14 (Chithirai 1)
    # This is a simplified calculation and may be off by a day in some years

    # Calculate the Tamil year
    tamil_year = date.year
    if date.month < 4 or (date.month == 4 and date.day < 14):
        tamil_year -= 1

    # Calculate the Tamil month
    if date.month == 4 and date.day >= 14:
        tamil_month = 0  # Chithirai
    elif date.month == 5 and date.day < 14:
        tamil_month = 0  # Chithirai
    elif date.month == 5 and date.day >= 14:
        tamil_month = 1  # Vaikasi
    elif date.month == 6 and date.day < 14:
        tamil_month = 1  # Vaikasi
    elif date.month == 6 and date.day >= 14:
        tamil_month = 2  # Aani
    elif date.month == 7 and date.day < 14:
        tamil_month = 2  # Aani
    elif date.month == 7 and date.day >= 14:
        tamil_month = 3  # Aadi
    elif date.month == 8 and date.day < 14:
        tamil_month = 3  # Aadi
    elif date.month == 8 and date.day >= 14:
        tamil_month = 4  # Aavani
    elif date.month == 9 and date.day < 14:
        tamil_month = 4  # Aavani
    elif date.month == 9 and date.day >= 14:
        tamil_month = 5  # Purattasi
    elif date.month == 10 and date.day < 14:
        tamil_month = 5  # Purattasi
    elif date.month == 10 and date.day >= 14:
        tamil_month = 6  # Aippasi
    elif date.month == 11 and date.day < 14:
        tamil_month = 6  # Aippasi
    elif date.month == 11 and date.day >= 14:
        tamil_month = 7  # Karthigai
    elif date.month == 12 and date.day < 14:
        tamil_month = 7  # Karthigai
    elif date.month == 12 and date.day >= 14:
        tamil_month = 8  # Margazhi
    elif date.month == 1 and date.day < 14:
        tamil_month = 8  # Margazhi
    elif date.month == 1 and date.day >= 14:
        tamil_month = 9  # Thai
    elif date.month == 2 and date.day < 14:
        tamil_month = 9  # Thai
    elif date.month == 2 and date.day >= 14:
        tamil_month = 10  # Maasi
    elif date.month == 3 and date.day < 14:
        tamil_month = 10  # Maasi
    elif date.month == 3 and date.day >= 14:
        tamil_month = 11  # Panguni
    elif date.month == 4 and date.day < 14:
        tamil_month = 11  # Panguni

    # Calculate the Tamil day
    if date.day >= 14:
        tamil_day = date.day - 13
    else:
        # Get the number of days in the previous month
        if date.month == 1:  # January
            prev_month = 12
            prev_year = date.year - 1
        else:
            prev_month = date.month - 1
            prev_year = date.year

        days_in_prev_month = calendar.monthrange(prev_year, prev_month)[1]
        tamil_day = days_in_prev_month - 13 + date.day

    # Calculate days in Tamil month (approximation)
    if tamil_month in [0, 2, 4, 6, 8, 10]:  # 31-day months
        days_in_month = 31
    else:  # 30-day months
        days_in_month = 30

    return {
        'tamil_month': tamil_month,
        'tamil_month_name': TAMIL_MONTH_NAMES[tamil_month],
        'tamil_day': tamil_day,
        'tamil_year': tamil_year,
        'days_in_month': days_in_month,
        'gregorian_date': f"{date.year}-{date.month:02d}-{date.day:02d}"
    }

def get_daily_panchanga(date=None, lat=13.0878, lon=80.2785, tz=5.5):
    """
    Get a simplified daily panchanga for the given date.

    Args:
        date (datetime, optional): Date for which to calculate panchanga. Defaults to current date.
        lat (float, optional): Latitude. Defaults to Chennai latitude.
        lon (float, optional): Longitude. Defaults to Chennai longitude.
        tz (float, optional): Timezone offset. Defaults to IST.

    Returns:
        dict: Daily panchanga information
    """
    if date is None:
        date = datetime.now()

    # Get Tamil date
    tamil_date = get_tamil_date(date, lat, lon, tz)

    # Get weekday
    weekday = date.weekday()
    if weekday == 6:  # Convert to 0-6 where 0 is Sunday
        weekday = 0
    else:
        weekday += 1

    # Simplified calculation for tithi (lunar day)
    # This is a very rough approximation based on the day of the month
    day_of_month = date.day
    tithi_number = ((day_of_month + 7) % 30) + 1

    # Simplified calculation for nakshatra (lunar mansion)
    # This is a very rough approximation based on the day of the month
    nakshatra_number = ((day_of_month + 3) % 27) + 1

    # Simplified calculation for yoga
    # This is a very rough approximation based on the day of the month
    yoga_number = ((day_of_month + 5) % 27) + 1

    # Simplified calculation for karana
    # This is a very rough approximation based on the day of the month
    karana_number = ((day_of_month * 2) % 60) + 1

    # Simplified calculation for raahu kaalam
    # This is a traditional calculation based on the day of the week
    raahu_start_hours = [4.5, 3.0, 1.5, 0.0, 6.0, 4.5, 3.0]  # Sun to Sat
    raahu_start_hour = raahu_start_hours[weekday]
    raahu_start = f"{int(raahu_start_hour):02d}:{int((raahu_start_hour % 1) * 60):02d}:00"
    raahu_end_hour = raahu_start_hour + 1.5
    raahu_end = f"{int(raahu_end_hour):02d}:{int((raahu_end_hour % 1) * 60):02d}:00"

    # Simplified calculation for yamagandam
    # This is a traditional calculation based on the day of the week
    yama_start_hours = [6.0, 4.5, 3.0, 1.5, 0.0, 6.0, 4.5]  # Sun to Sat
    yama_start_hour = yama_start_hours[weekday]
    yama_start = f"{int(yama_start_hour):02d}:{int((yama_start_hour % 1) * 60):02d}:00"
    yama_end_hour = yama_start_hour + 1.5
    yama_end = f"{int(yama_end_hour):02d}:{int((yama_end_hour % 1) * 60):02d}:00"

    # Simplified calculation for gulikai
    # This is a traditional calculation based on the day of the week
    gulikai_start_hours = [3.0, 1.5, 0.0, 6.0, 4.5, 3.0, 1.5]  # Sun to Sat
    gulikai_start_hour = gulikai_start_hours[weekday]
    gulikai_start = f"{int(gulikai_start_hour):02d}:{int((gulikai_start_hour % 1) * 60):02d}:00"
    gulikai_end_hour = gulikai_start_hour + 1.5
    gulikai_end = f"{int(gulikai_end_hour):02d}:{int((gulikai_end_hour % 1) * 60):02d}:00"

    # Check for special observances
    special_observances = []

    # Check for Ekadashi
    if tithi_number == 11 or tithi_number == 26:
        special_observances.append({
            'name': 'Ekadashi',
            'type': 'fasting',
            'description': 'Fasting day dedicated to Lord Vishnu'
        })

    # Check for Pradosham
    if tithi_number == 13 or tithi_number == 28:
        special_observances.append({
            'name': 'Pradosham',
            'type': 'worship',
            'description': 'Auspicious time for worshipping Lord Shiva'
        })

    # Check for Amavasya (New Moon)
    if tithi_number == 30:
        special_observances.append({
            'name': 'Amavasai',
            'type': 'special_day',
            'description': 'New Moon day'
        })

    # Check for Pournami (Full Moon)
    if tithi_number == 15:
        special_observances.append({
            'name': 'Pournami',
            'type': 'special_day',
            'description': 'Full Moon day'
        })

    # Check for Sankatahara Chaturthi
    if tithi_number == 4 and date.month % 2 == 0:  # Krishna Paksha Chaturthi
        special_observances.append({
            'name': 'Sankatahara Chaturthi',
            'type': 'worship',
            'description': 'Day for worshipping Lord Ganesha'
        })

    # Compile the panchanga data
    panchanga_data = {
        'date': {
            'gregorian': f"{date.year}-{date.month:02d}-{date.day:02d}",
            'tamil_month': tamil_date['tamil_month'],
            'tamil_month_name': tamil_date['tamil_month_name'],
            'tamil_day': tamil_date['tamil_day'],
            'tamil_year': tamil_date['tamil_year'],
            'weekday': weekday,
            'weekday_name': TAMIL_WEEKDAY_NAMES[weekday]
        },
        'timings': {
            'sunrise': "06:00:00",  # Simplified
            'sunset': "18:00:00",   # Simplified
            'raahu_kaalam': {
                'start': raahu_start,
                'end': raahu_end
            },
            'yamagandam': {
                'start': yama_start,
                'end': yama_end
            },
            'gulikai': {
                'start': gulikai_start,
                'end': gulikai_end
            }
        },
        'panchanga': {
            'tithi': {
                'number': tithi_number,
                'name': TAMIL_TITHI_NAMES[tithi_number - 1]
            },
            'nakshatra': {
                'number': nakshatra_number,
                'name': TAMIL_NAKSHATRA_NAMES[nakshatra_number - 1]
            },
            'yoga': {
                'number': yoga_number,
                'name': TAMIL_YOGA_NAMES[yoga_number - 1]
            },
            'karana': {
                'number': karana_number,
                'name': TAMIL_KARANA_NAMES[karana_number - 1]
            }
        },
        'special_observances': special_observances
    }

    return panchanga_data

def get_monthly_panchanga(year, month, lat=13.0878, lon=80.2785, tz=5.5):
    """
    Get the panchanga for an entire month.

    Args:
        year (int): Year
        month (int): Month (1-12)
        lat (float, optional): Latitude. Defaults to Chennai latitude.
        lon (float, optional): Longitude. Defaults to Chennai longitude.
        tz (float, optional): Timezone offset. Defaults to IST.

    Returns:
        list: List of daily panchanga for each day in the month
    """
    # Determine the number of days in the month
    days_in_month = calendar.monthrange(year, month)[1]

    # Get panchanga for each day
    monthly_panchanga = []
    for day in range(1, days_in_month + 1):
        date = datetime(year, month, day)
        daily_panchanga = get_daily_panchanga(date, lat, lon, tz)
        monthly_panchanga.append(daily_panchanga)

    return monthly_panchanga

def get_festival_dates(year, lat=13.0878, lon=80.2785, tz=5.5):
    """
    Get the dates of major Tamil festivals for the given year.

    Args:
        year (int): Year for which to calculate festival dates
        lat (float, optional): Latitude. Defaults to Chennai latitude.
        lon (float, optional): Longitude. Defaults to Chennai longitude.
        tz (float, optional): Timezone offset. Defaults to IST.

    Returns:
        list: List of festival dates with details
    """
    festivals = []

    # Tamil New Year (April 14)
    festivals.append({
        'name': 'Tamil New Year (Puthandu)',
        'date': f"{year}-04-14",
        'description': 'First day of Tamil month Chithirai',
        'tamil_month': 0,  # Chithirai
        'tamil_day': 1
    })

    # Pongal (January 14)
    festivals.append({
        'name': 'Pongal',
        'date': f"{year}-01-14",
        'description': 'Harvest festival celebrated on the first day of Thai',
        'tamil_month': 9,  # Thai
        'tamil_day': 1
    })

    # Aadi Perukku (August 3, approximate)
    festivals.append({
        'name': 'Aadi Perukku',
        'date': f"{year}-08-03",
        'description': 'Festival celebrating water and fertility',
        'tamil_month': 3,  # Aadi
        'tamil_day': 18
    })

    # Deepavali (approximate - this varies each year based on lunar calendar)
    # Using a simplified approximation for this example
    festivals.append({
        'name': 'Deepavali',
        'date': f"{year}-11-04",
        'description': 'Festival of Lights',
        'tamil_month': 6,  # Aippasi
        'tamil_day': 14
    })

    # Vaikasi Visakam (May/June, full moon day in Vaikasi)
    # Approximate date - usually falls on the full moon day in May/June
    festivals.append({
        'name': 'Vaikasi Visakam',
        'date': f"{year}-05-23",
        'description': 'Birthday of Lord Murugan',
        'tamil_month': 1,  # Vaikasi
        'tamil_day': 15
    })

    # Aadi Amavasai (July/August, new moon day in Aadi)
    # Approximate date - usually falls on the new moon day in July/August
    festivals.append({
        'name': 'Aadi Amavasai',
        'date': f"{year}-07-24",
        'description': 'Day to honor ancestors',
        'tamil_month': 3,  # Aadi
        'tamil_day': 30
    })

    # Navaratri (September/October, first nine days of Purattasi)
    # Approximate date - usually starts in late September or early October
    festivals.append({
        'name': 'Navaratri',
        'date': f"{year}-09-25",
        'description': 'Nine-night festival honoring the goddess Durga',
        'tamil_month': 5,  # Purattasi
        'tamil_day': 1
    })

    # Karthigai Deepam (November/December, full moon day in Karthigai)
    # Approximate date - usually falls on the full moon day in November/December
    festivals.append({
        'name': 'Karthigai Deepam',
        'date': f"{year}-11-27",
        'description': 'Festival of lights dedicated to Lord Shiva',
        'tamil_month': 7,  # Karthigai
        'tamil_day': 15
    })

    return festivals

def format_time(time_str):
    """
    Format time string for display.

    Args:
        time_str (str): Time string in the format "HH:MM:SS"

    Returns:
        str: Formatted time string
    """
    if isinstance(time_str, str):
        parts = time_str.split(':')
        if len(parts) == 3:
            hour = int(parts[0])
            minute = int(parts[1])

            if hour < 12:
                am_pm = 'AM'
                if hour == 0:
                    hour = 12
            else:
                am_pm = 'PM'
                if hour > 12:
                    hour -= 12

            return f"{hour}:{minute:02d} {am_pm}"

    return time_str
