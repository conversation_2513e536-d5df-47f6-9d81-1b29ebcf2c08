"""
Rule Engine Service - Main Module (Clean Version)

This module provides functionality to evaluate complex astrological rules and conditions.
It supports logical operators (AND, OR) and comparison operators (IN, NOT IN, etc.).
Enhanced to support ruling planet relationships and planetary relationships.

This is the main entry point that imports from organized sub-modules.
"""

from bson import ObjectId
import re
from datetime import datetime, timedelta
from dateutil import parser

# Try to import extensions, but handle gracefully if not available
try:
    from ...extensions import mongo
except ImportError:
    mongo = None
    print("Warning: MongoDB extensions not available")

try:
    from ..astrology.planetary_relationships import get_planet_relationship, is_planet_aspecting_planet
except ImportError:
    print("Warning: Planetary relationships module not available")
    def get_planet_relationship(*args, **kwargs):
        return "Unknown"
    def is_planet_aspecting_planet(*args, **kwargs):
        return False

# Import from the new modular rule engine to avoid duplication
try:
    from .rule_engine.astrology import (
        get_standardized_sign_names, get_standardized_sign_map,
        get_sign_name_from_longitude, get_sign_start_degree
    )
except ImportError:
    print("Warning: Rule engine astrology module not available")
    def get_standardized_sign_names():
        return ['<PERSON><PERSON>', 'Rish<PERSON>m', '<PERSON>una<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>mma<PERSON>', '<PERSON>nni',
                'Thulam', 'Virichigam', 'Dhanusu', 'MAGARAM', 'Kumbam', 'Meenam']
    def get_standardized_sign_map():
        return {'Mesham': 0, 'Rishabam': 1, 'Midunam': 2, 'Kadagam': 3,
                'Simmam': 4, 'Kanni': 5, 'Thulam': 6, 'Virichigam': 7,
                'Dhanusu': 8, 'MAGARAM': 9, 'Kumbam': 10, 'Meenam': 11}
    def get_sign_name_from_longitude(longitude):
        return "Unknown"
    def get_sign_start_degree(sign_name):
        return 0

# Import from organized sub-modules with error handling
try:
    from .rule_engine_legacy.planetary_motion import (
        get_fast_planetary_daily_motion,
        get_dynamic_planetary_daily_motion,
        calculate_planet_transit_to_house,
        calculate_all_planet_transits_in_period,
        calculate_accurate_planet_transit,
        get_current_planetary_position
    )
except ImportError as e:
    print(f"Warning: Planetary motion module not available: {e}")

try:
    from .rule_engine_legacy.chart_processing import (
        get_default_house_ruling_planets,
        get_standard_planet_names,
        get_dynamic_house_name_ruling_planets,
        get_full_d1_chart_details,
        get_user_house_system,
        get_chart_data,
        get_planet_house_mapping
    )
except ImportError as e:
    print(f"Warning: Chart processing module not available: {e}")

try:
    from .rule_engine_legacy.condition_evaluation import (
        parse_condition,
        parse_complex_query,
        evaluate_condition,
        evaluate_parsed_query,
        evaluate_rule
    )
except ImportError as e:
    print(f"Warning: Condition evaluation module not available: {e}")

try:
    from .rule_engine_legacy.relationship_checking import (
        check_comprehensive_relationship,
        check_house_ruling_planet_relationship,
        check_planet_to_house_planet_relationship,
        check_house_planet_relationship
    )
except ImportError as e:
    print(f"Warning: Relationship checking module not available: {e}")

try:
    from .rule_engine_legacy.dasha_processing import (
        parse_dasha_string,
        get_dasha_periods_for_planet,
        get_house_ruling_planet_dasha_periods,
        parse_and_evaluate_dasha_query
    )
except ImportError as e:
    print(f"Warning: Dasha processing module not available: {e}")

# KOCHARAM filtering removed - use main_rule_engine.py for KOCHARAM functionality

try:
    from .rule_engine_legacy.response_formatting import (
        create_clean_dasha_response,
        format_dasha_periods_clean,
        format_successful_conditions_clean
    )
except ImportError as e:
    print(f"Warning: Response formatting module not available: {e}")


# Main evaluation function that uses the sub-modules
def evaluate_rule_legacy(query, user_profile_id, member_profile_id, chart_type="D1"):
    """
    Main entry point for rule evaluation using the organized sub-modules.

    Args:
        query (str): Query string to evaluate
        user_profile_id (str): User profile ID
        member_profile_id (str): Member profile ID
        chart_type (str): Chart type

    Returns:
        dict: Complete evaluation results
    """
    try:
        return evaluate_rule(query, user_profile_id, member_profile_id, chart_type)
    except NameError:
        return {
            'success': False,
            'error': 'evaluate_rule function not available - missing imports',
            'query': query,
            'chart_type': chart_type
        }
    except Exception as e:
        return {
            'success': False,
            'error': f'Error in rule evaluation: {str(e)}',
            'query': query,
            'chart_type': chart_type
        }


def parse_and_evaluate_dasha_query_legacy(chart_data, query, chart_type="D1", user_profile_id=None, member_profile_id=None):
    """
    Main entry point for dasha query evaluation using the organized sub-modules.

    Args:
        chart_data (dict): Chart data
        query (str): Query string
        chart_type (str): Chart type
        user_profile_id (str): User profile ID
        member_profile_id (str): Member profile ID

    Returns:
        dict: Evaluation results
    """
    try:
        return parse_and_evaluate_dasha_query(chart_data, query, chart_type, user_profile_id, member_profile_id)
    except NameError:
        return {
            'success': False,
            'error': 'parse_and_evaluate_dasha_query function not available - missing imports',
            'query': query,
            'chart_type': chart_type
        }
    except Exception as e:
        return {
            'success': False,
            'error': f'Error in dasha query evaluation: {str(e)}',
            'query': query,
            'chart_type': chart_type
        }


# KOCHARAM filtering functionality removed
# Use main_rule_engine.py for complete KOCHARAM implementation with 100% accuracy


# All other functions have been moved to organized sub-modules:
# - planetary_motion.py: Planetary motion calculations and transit predictions
# - chart_processing.py: Chart data processing and house system mappings
# - condition_evaluation.py: Condition parsing and evaluation logic
# - relationship_checking.py: Astrological relationship checking functions
# - dasha_processing.py: Dasha-related calculations and processing
# - response_formatting.py: Response formatting and analysis creation
#
# Note: KOCHARAM filtering has been removed from this clean version.
# For KOCHARAM functionality with 100% accuracy, use main_rule_engine.py
#
# Import functions from these modules as needed for backward compatibility
