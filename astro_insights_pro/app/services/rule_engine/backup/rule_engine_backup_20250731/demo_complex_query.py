"""
Complex Query Processing Demonstration

This script demonstrates how the enhanced rule engine v2.0 processes
the complex query with logical operators and KOCHARAM filtering.

Query: (2nd House Ruling Planet Dasa_Bhukti_Dates OR 7th House Ruling Planet Bhukti_Dates OR VENUS Bhukti_Dates) 
       AND KOCHARAM_FILTER(JUPITER ASPECT 7th_House AND JUPITER IN 7th_House)

Version: 2.0
Author: Fortune Lens Team
Last Updated: 2025-07-31
"""

import re
from datetime import datetime
from typing import Dict, List, Any


def analyze_complex_query(query: str) -> Dict[str, Any]:
    """
    Analyze the structure of a complex astrological query.
    
    Args:
        query: Complex query string
        
    Returns:
        Analysis results with query breakdown
    """
    analysis = {
        "original_query": query,
        "query_type": "unknown",
        "has_dasha_patterns": False,
        "has_logical_operators": False,
        "has_kocharam_filter": False,
        "components": {},
        "processing_strategy": []
    }
    
    # Check for dasha patterns
    dasha_patterns = ['Bhukti_Dates', 'Dasa_Dates', 'House_Ruling_Planet']
    analysis["has_dasha_patterns"] = any(pattern in query for pattern in dasha_patterns)
    
    # Check for logical operators
    logical_operators = ['OR', 'AND', 'NOT']
    analysis["has_logical_operators"] = any(op in query.upper() for op in logical_operators)
    
    # Check for KOCHARAM filter
    analysis["has_kocharam_filter"] = 'KOCHARAM_FILTER' in query
    
    # Determine query type
    if analysis["has_dasha_patterns"]:
        analysis["query_type"] = "dasha_based"
    elif analysis["has_logical_operators"]:
        analysis["query_type"] = "logical_combination"
    else:
        analysis["query_type"] = "basic_rule"
    
    # Extract KOCHARAM condition
    if analysis["has_kocharam_filter"]:
        kocharam_match = re.search(r'KOCHARAM_FILTER\((.*?)\)', query)
        if kocharam_match:
            kocharam_condition = kocharam_match.group(1)
            analysis["components"]["kocharam_condition"] = kocharam_condition
            
            # Analyze KOCHARAM condition
            kocharam_analysis = analyze_kocharam_condition(kocharam_condition)
            analysis["components"]["kocharam_analysis"] = kocharam_analysis
    
    # Extract base query (without KOCHARAM)
    base_query = re.sub(r'AND\s+KOCHARAM_FILTER\([^)]+\)', '', query).strip()
    analysis["components"]["base_query"] = base_query
    
    # Analyze base query structure
    if '(' in base_query and ')' in base_query:
        analysis["components"]["has_grouping"] = True
        
        # Extract grouped content
        grouped_content = re.search(r'\((.*?)\)', base_query)
        if grouped_content:
            group_content = grouped_content.group(1)
            or_parts = [part.strip() for part in group_content.split(' OR ')]
            analysis["components"]["or_conditions"] = or_parts
    
    # Define processing strategy
    analysis["processing_strategy"] = create_processing_strategy(analysis)
    
    return analysis


def analyze_kocharam_condition(condition: str) -> Dict[str, Any]:
    """
    Analyze KOCHARAM condition structure.
    
    Args:
        condition: KOCHARAM condition string
        
    Returns:
        KOCHARAM analysis results
    """
    kocharam_analysis = {
        "condition": condition,
        "has_aspect": "ASPECT" in condition,
        "has_transit": " IN " in condition,
        "has_logical_and": " AND " in condition,
        "components": []
    }
    
    # Split by AND to get individual conditions
    if " AND " in condition:
        parts = [part.strip() for part in condition.split(" AND ")]
        for part in parts:
            component = analyze_single_kocharam_component(part)
            kocharam_analysis["components"].append(component)
    else:
        component = analyze_single_kocharam_component(condition)
        kocharam_analysis["components"].append(component)
    
    return kocharam_analysis


def analyze_single_kocharam_component(component: str) -> Dict[str, Any]:
    """
    Analyze a single KOCHARAM component.
    
    Args:
        component: Single KOCHARAM component
        
    Returns:
        Component analysis
    """
    analysis = {
        "component": component,
        "type": "unknown",
        "planet": None,
        "house": None,
        "valid": False
    }
    
    # Check for aspect pattern
    if " ASPECT " in component:
        parts = component.split(" ASPECT ")
        if len(parts) == 2:
            analysis["type"] = "aspect"
            analysis["planet"] = parts[0].strip()
            house_part = parts[1].strip()
            analysis["house"] = extract_house_number(house_part)
            analysis["valid"] = True
    
    # Check for transit pattern
    elif " IN " in component:
        parts = component.split(" IN ")
        if len(parts) == 2:
            analysis["type"] = "transit"
            analysis["planet"] = parts[0].strip()
            house_part = parts[1].strip()
            analysis["house"] = extract_house_number(house_part)
            analysis["valid"] = True
    
    return analysis


def extract_house_number(house_part: str) -> int:
    """Extract house number from house string."""
    try:
        if "_House" in house_part:
            house_str = house_part.replace("th_House", "").replace("st_House", "").replace("nd_House", "").replace("rd_House", "")
            return int(house_str)
    except (ValueError, AttributeError):
        pass
    return None


def create_processing_strategy(analysis: Dict[str, Any]) -> List[str]:
    """
    Create processing strategy based on query analysis.
    
    Args:
        analysis: Query analysis results
        
    Returns:
        List of processing steps
    """
    strategy = []
    
    if analysis["query_type"] == "dasha_based":
        strategy.extend([
            "1. Route to parse_and_evaluate_dasha_query()",
            "2. Extract age constraints (if any)",
            "3. Parse KOCHARAM filter separately",
            "4. Split base query by logical operators",
            "5. Evaluate each dasha condition:",
        ])
        
        # Add specific dasha conditions
        if "or_conditions" in analysis["components"]:
            for i, condition in enumerate(analysis["components"]["or_conditions"]):
                strategy.append(f"   - Condition {i+1}: {condition}")
        
        strategy.extend([
            "6. Apply KOCHARAM filter to results:",
        ])
        
        # Add KOCHARAM components
        if "kocharam_analysis" in analysis["components"]:
            kocharam = analysis["components"]["kocharam_analysis"]
            for i, comp in enumerate(kocharam["components"]):
                strategy.append(f"   - {comp['type'].upper()}: {comp['planet']} -> {comp['house']}th house")
        
        strategy.extend([
            "7. Combine results with logical operators",
            "8. Return comprehensive analysis"
        ])
    
    return strategy


def demonstrate_query_processing():
    """Demonstrate the processing of the complex query."""
    
    print("🚀 Enhanced Rule Engine v2.0 - Complex Query Processing Demo")
    print("=" * 80)
    
    # Test query
    query = "(2nd House Ruling Planet  Dasa_Bhukti_Dates OR 7th House Ruling Planet Bhukti_Dates OR VENUS Bhukti_Dates) AND KOCHARAM_FILTER(JUPITER ASPECT 7th_House AND JUPITER IN 7th_House)"
    
    print(f"Query: {query}")
    print("=" * 80)
    
    # Analyze the query
    analysis = analyze_complex_query(query)
    
    print("📊 Query Analysis Results:")
    print(f"  Query Type: {analysis['query_type']}")
    print(f"  Has Dasha Patterns: {analysis['has_dasha_patterns']}")
    print(f"  Has Logical Operators: {analysis['has_logical_operators']}")
    print(f"  Has KOCHARAM Filter: {analysis['has_kocharam_filter']}")
    
    print("\n🔍 Query Components:")
    components = analysis["components"]
    
    if "base_query" in components:
        print(f"  Base Query: {components['base_query']}")
    
    if "or_conditions" in components:
        print(f"  OR Conditions ({len(components['or_conditions'])}):")
        for i, condition in enumerate(components["or_conditions"]):
            print(f"    {i+1}. {condition}")
    
    if "kocharam_condition" in components:
        print(f"  KOCHARAM Condition: {components['kocharam_condition']}")
        
        if "kocharam_analysis" in components:
            kocharam = components["kocharam_analysis"]
            print(f"  KOCHARAM Components ({len(kocharam['components'])}):")
            for i, comp in enumerate(kocharam["components"]):
                print(f"    {i+1}. {comp['type'].upper()}: {comp['planet']} -> {comp['house']}th house")
    
    print("\n🎯 Processing Strategy:")
    for step in analysis["processing_strategy"]:
        print(f"  {step}")
    
    print("\n✨ Enhanced Features in v2.0:")
    print("  ✅ Modular architecture with separated concerns")
    print("  ✅ Type-safe parsing with comprehensive validation")
    print("  ✅ Performance optimization with caching")
    print("  ✅ Standardized error handling")
    print("  ✅ Comprehensive logging and monitoring")
    print("  ✅ Backward compatibility with existing APIs")
    
    print("\n🔧 Technical Improvements:")
    print("  • ConditionParser class for robust parsing")
    print("  • KocharamProcessor for advanced transit calculations")
    print("  • PerformanceOptimizer for caching and metrics")
    print("  • ErrorHandler for standardized error responses")
    print("  • ValidationUtils for input validation")
    print("  • Configuration management for easy maintenance")
    
    print("\n" + "=" * 80)
    print("🏁 Demo Complete - Enhanced Rule Engine v2.0 Ready for Production")


if __name__ == "__main__":
    demonstrate_query_processing()
