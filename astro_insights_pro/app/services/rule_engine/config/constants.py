"""
Database Constants Service for Rule Engine

This module provides functions to fetch astrological constants from MongoDB
with fallback to hardcoded values for reliability. It handles caching and
error recovery to ensure the rule engine always has access to required data.
"""

from datetime import datetime, timedelta
from ....extensions import mongo
import logging

# Setup logging
logger = logging.getLogger(__name__)

# Cache for database constants (expires after 1 hour)
_constants_cache = {}
_cache_expiry = {}
CACHE_DURATION = timedelta(hours=1)

def _is_cache_valid(key):
    """Check if cache entry is still valid"""
    return key in _cache_expiry and datetime.now() < _cache_expiry[key]

def _set_cache(key, value):
    """Set cache entry with expiry"""
    _constants_cache[key] = value
    _cache_expiry[key] = datetime.now() + CACHE_DURATION

def get_planetary_rotation_periods():
    """
    Get planetary rotation periods from MongoDB with fallback to hardcoded values.
    
    Returns:
        dict: Planetary rotation periods in days
    """
    cache_key = 'planetary_rotation_periods'
    
    # Check cache first
    if _is_cache_valid(cache_key):
        return _constants_cache[cache_key]
    
    try:
        # Try to get from MongoDB
        collection = mongo.db.astro_planetary_rotation_periods
        periods = {}
        
        for doc in collection.find():
            planet = doc.get('planet', '').upper()
            rotation_days = doc.get('rotation_period_days')
            if planet and rotation_days:
                periods[planet] = rotation_days
        
        if periods:
            logger.info(f"Loaded {len(periods)} planetary rotation periods from MongoDB")
            _set_cache(cache_key, periods)
            return periods
            
    except Exception as e:
        logger.warning(f"Failed to load planetary rotation periods from MongoDB: {e}")
    
    # Fallback to hardcoded values
    fallback_periods = {
        'SUN': 365,
        'MOON': 30,
        'MERCURY': 88,
        'VENUS': 225,
        'MARS': 687,
        'JUPITER': 4333,
        'RAHU': 6790,
        'KETU': 6790,
        'SATURN': 10756
    }
    
    logger.info("Using fallback planetary rotation periods")
    _set_cache(cache_key, fallback_periods)
    return fallback_periods

def get_planetary_aspects():
    """
    Get planetary aspects from MongoDB with fallback to hardcoded values.
    
    Returns:
        dict: Planetary aspect houses
    """
    cache_key = 'planetary_aspects'
    
    # Check cache first
    if _is_cache_valid(cache_key):
        return _constants_cache[cache_key]
    
    try:
        # Try to get from existing MongoDB collection
        collection = mongo.db.astro_planets_aspects
        aspects = {}
        
        for doc in collection.find():
            planet = doc.get('Planet', '').upper()
            aspect_houses_str = doc.get('Aspect Houses', '')
            
            if planet and aspect_houses_str:
                # Parse aspect houses (e.g., "7th" -> [7], "3rd, 7th, 10th" -> [3, 7, 10])
                aspect_houses = []
                for house_str in aspect_houses_str.split(','):
                    house_str = house_str.strip().replace('th', '').replace('st', '').replace('nd', '').replace('rd', '')
                    try:
                        house_num = int(house_str)
                        aspect_houses.append(house_num)
                    except ValueError:
                        continue
                
                if aspect_houses:
                    aspects[planet] = aspect_houses
        
        if aspects:
            logger.info(f"Loaded {len(aspects)} planetary aspects from MongoDB")
            _set_cache(cache_key, aspects)
            return aspects
            
    except Exception as e:
        logger.warning(f"Failed to load planetary aspects from MongoDB: {e}")
    
    # Fallback to hardcoded values
    fallback_aspects = {
        'SUN': [7],           # Sun aspects 7th house
        'MOON': [7],          # Moon aspects 7th house
        'MERCURY': [7],       # Mercury aspects 7th house
        'VENUS': [7],         # Venus aspects 7th house
        'SATURN': [3, 7, 10], # Saturn aspects 3rd, 7th, 10th houses
        'MARS': [4, 7, 8],    # Mars aspects 4th, 7th, 8th houses
        'JUPITER': [5, 7, 9], # Jupiter aspects 5th, 7th, 9th houses
        'RAHU': [5, 7, 9],    # Rahu aspects same as Jupiter (default)
        'KETU': [5, 7, 9]     # Ketu aspects same as Jupiter (default)
    }
    
    logger.info("Using fallback planetary aspects")
    _set_cache(cache_key, fallback_aspects)
    return fallback_aspects

def get_sign_degree_mappings():
    """
    Get zodiac sign degree mappings from MongoDB with fallback to hardcoded values.
    
    Returns:
        dict: Sign name to degree range mappings
    """
    cache_key = 'sign_degree_mappings'
    
    # Check cache first
    if _is_cache_valid(cache_key):
        return _constants_cache[cache_key]
    
    try:
        # Try to get from MongoDB
        collection = mongo.db.astro_sign_degree_mappings
        mappings = {}
        
        for doc in collection.find():
            sign_name = doc.get('sign_name', '')
            start_degree = doc.get('start_degree')
            end_degree = doc.get('end_degree')
            
            if sign_name and start_degree is not None and end_degree is not None:
                mappings[sign_name] = {
                    'start': start_degree,
                    'end': end_degree
                }
        
        if mappings:
            logger.info(f"Loaded {len(mappings)} sign degree mappings from MongoDB")
            _set_cache(cache_key, mappings)
            return mappings
            
    except Exception as e:
        logger.warning(f"Failed to load sign degree mappings from MongoDB: {e}")
    
    # Fallback to hardcoded values
    fallback_mappings = {
        "Mesham": {"start": 0, "end": 30},       # Aries: 0-30°
        "Rishabam": {"start": 30, "end": 60},    # Taurus: 30-60°
        "Midunam": {"start": 60, "end": 90},     # Gemini: 60-90°
        "Kadagam": {"start": 90, "end": 120},    # Cancer: 90-120°
        "Simmam": {"start": 120, "end": 150},    # Leo: 120-150°
        "Kanni": {"start": 150, "end": 180},     # Virgo: 150-180°
        "Thulam": {"start": 180, "end": 210},    # Libra: 180-210°
        "Virichigam": {"start": 210, "end": 240}, # Scorpio: 210-240°
        "Dhanusu": {"start": 240, "end": 270},   # Sagittarius: 240-270°
        "MAGARAM": {"start": 270, "end": 300},   # Capricorn: 270-300°
        "Kumbam": {"start": 300, "end": 330},    # Aquarius: 300-330°
        "Meenam": {"start": 330, "end": 360}     # Pisces: 330-360°
    }
    
    logger.info("Using fallback sign degree mappings")
    _set_cache(cache_key, fallback_mappings)
    return fallback_mappings

def get_house_ruling_planets():
    """
    Get house ruling planets from MongoDB with fallback to hardcoded values.
    
    Returns:
        dict: House name to ruling planet mappings
    """
    cache_key = 'house_ruling_planets'
    
    # Check cache first
    if _is_cache_valid(cache_key):
        return _constants_cache[cache_key]
    
    try:
        # Try to get from existing MongoDB collection
        collection = mongo.db.astro_house_names
        mappings = {}
        
        for doc in collection.find():
            house_name = doc.get('House Name', '').upper()
            ruling_planet = doc.get('Ruling Planets', '').upper()
            
            if house_name and ruling_planet:
                mappings[house_name] = ruling_planet
        
        if mappings:
            logger.info(f"Loaded {len(mappings)} house ruling planets from MongoDB")
            _set_cache(cache_key, mappings)
            return mappings
            
    except Exception as e:
        logger.warning(f"Failed to load house ruling planets from MongoDB: {e}")
    
    # Fallback to hardcoded values
    fallback_mappings = {
        'MESHAM': 'MARS',      # Aries
        'RISHABAM': 'VENUS',   # Taurus
        'MIDUNAM': 'MERCURY',  # Gemini
        'KADAGAM': 'MOON',     # Cancer
        'SIMMAM': 'SUN',       # Leo
        'KANNI': 'MERCURY',    # Virgo
        'THULAM': 'VENUS',     # Libra
        'VIRICHIGAM': 'MARS',  # Scorpio
        'DHANUSU': 'JUPITER',  # Sagittarius
        'MAGARAM': 'SATURN',   # Capricorn
        'KUMBAM': 'SATURN',    # Aquarius
        'MEENAM': 'JUPITER'    # Pisces
    }
    
    logger.info("Using fallback house ruling planets")
    _set_cache(cache_key, fallback_mappings)
    return fallback_mappings

def clear_constants_cache():
    """Clear the constants cache to force reload from database"""
    global _constants_cache, _cache_expiry
    _constants_cache.clear()
    _cache_expiry.clear()
    logger.info("Constants cache cleared")

def initialize_missing_collections():
    """
    Initialize missing MongoDB collections with default data if they don't exist.
    This should be called during application startup.
    """
    try:
        # Initialize planetary rotation periods collection
        if 'astro_planetary_rotation_periods' not in mongo.db.list_collection_names():
            rotation_data = [
                {'planet': 'SUN', 'rotation_period_days': 365, 'description': 'Solar year'},
                {'planet': 'MOON', 'rotation_period_days': 30, 'description': 'Lunar month'},
                {'planet': 'MERCURY', 'rotation_period_days': 88, 'description': 'Mercury orbital period'},
                {'planet': 'VENUS', 'rotation_period_days': 225, 'description': 'Venus orbital period'},
                {'planet': 'MARS', 'rotation_period_days': 687, 'description': 'Mars orbital period'},
                {'planet': 'JUPITER', 'rotation_period_days': 4333, 'description': 'Jupiter orbital period'},
                {'planet': 'RAHU', 'rotation_period_days': 6790, 'description': 'Rahu nodal period'},
                {'planet': 'KETU', 'rotation_period_days': 6790, 'description': 'Ketu nodal period'},
                {'planet': 'SATURN', 'rotation_period_days': 10756, 'description': 'Saturn orbital period'}
            ]
            
            # Add timestamps
            now = datetime.utcnow()
            for item in rotation_data:
                item['created_at'] = now
                item['updated_at'] = now
            
            mongo.db.astro_planetary_rotation_periods.insert_many(rotation_data)
            logger.info("Created astro_planetary_rotation_periods collection")
        
        # Initialize sign degree mappings collection
        if 'astro_sign_degree_mappings' not in mongo.db.list_collection_names():
            degree_data = [
                {'sign_name': 'Mesham', 'start_degree': 0, 'end_degree': 30, 'english_name': 'Aries'},
                {'sign_name': 'Rishabam', 'start_degree': 30, 'end_degree': 60, 'english_name': 'Taurus'},
                {'sign_name': 'Midunam', 'start_degree': 60, 'end_degree': 90, 'english_name': 'Gemini'},
                {'sign_name': 'Kadagam', 'start_degree': 90, 'end_degree': 120, 'english_name': 'Cancer'},
                {'sign_name': 'Simmam', 'start_degree': 120, 'end_degree': 150, 'english_name': 'Leo'},
                {'sign_name': 'Kanni', 'start_degree': 150, 'end_degree': 180, 'english_name': 'Virgo'},
                {'sign_name': 'Thulam', 'start_degree': 180, 'end_degree': 210, 'english_name': 'Libra'},
                {'sign_name': 'Virichigam', 'start_degree': 210, 'end_degree': 240, 'english_name': 'Scorpio'},
                {'sign_name': 'Dhanusu', 'start_degree': 240, 'end_degree': 270, 'english_name': 'Sagittarius'},
                {'sign_name': 'MAGARAM', 'start_degree': 270, 'end_degree': 300, 'english_name': 'Capricorn'},
                {'sign_name': 'Kumbam', 'start_degree': 300, 'end_degree': 330, 'english_name': 'Aquarius'},
                {'sign_name': 'Meenam', 'start_degree': 330, 'end_degree': 360, 'english_name': 'Pisces'}
            ]
            
            # Add timestamps
            now = datetime.utcnow()
            for item in degree_data:
                item['created_at'] = now
                item['updated_at'] = now
            
            mongo.db.astro_sign_degree_mappings.insert_many(degree_data)
            logger.info("Created astro_sign_degree_mappings collection")
            
        logger.info("Database constants initialization completed")
        
    except Exception as e:
        logger.error(f"Failed to initialize missing collections: {e}")
