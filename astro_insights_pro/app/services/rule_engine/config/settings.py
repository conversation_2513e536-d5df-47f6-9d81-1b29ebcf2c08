"""
Rule Engine Configuration

This module contains configuration settings, constants, and default values
for the rule engine system.

Version: 2.0
Author: Fortune Lens Team
Last Updated: 2025-07-31
"""

import logging
from typing import Dict, List
from enum import Enum


class LogLevel(Enum):
    """Logging levels"""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class RuleEngineSettings:
    """Main configuration settings for the rule engine"""
    
    # Performance settings
    MAX_CACHE_SIZE = 1000
    CACHE_TTL_SECONDS = 3600
    MAX_TRANSIT_CALCULATIONS = 50
    MAX_PROCESSING_TIME_SECONDS = 300
    
    # Calculation settings
    DEGREE_PRECISION = 0.1
    TIME_PRECISION_HOURS = 1
    ANGULAR_TOLERANCE = 0.5
    
    # Validation settings
    MIN_HOUSE_NUMBER = 1
    MAX_HOUSE_NUMBER = 12
    MIN_PLANET_LONGITUDE = 0.0
    MAX_PLANET_LONGITUDE = 360.0
    
    # Logging settings
    LOG_LEVEL = LogLevel.INFO
    ENABLE_DEBUG_LOGGING = False
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Database settings
    MONGODB_TIMEOUT_SECONDS = 30
    MAX_RETRY_ATTEMPTS = 3
    
    # KOCHARAM specific settings
    KOCHARAM_MAX_ITERATIONS = 100
    KOCHARAM_CONVERGENCE_THRESHOLD = 0.01
    KOCHARAM_DEFAULT_STEP_SIZE = 1.0


class PlanetaryConstants:
    """Planetary constants and default values"""
    
    # Planetary rotation periods (in days) - fallback values
    ROTATION_PERIODS = {
        'SUN': 365,
        'MOON': 30,
        'MERCURY': 88,
        'VENUS': 225,
        'MARS': 687,
        'JUPITER': 4333,
        'SATURN': 10756,
        'RAHU': 6790,
        'KETU': 6790
    }
    
    # Planetary aspects - traditional Vedic astrology
    ASPECTS = {
        'SUN': [7],
        'MOON': [7],
        'MERCURY': [7],
        'VENUS': [7],
        'MARS': [4, 7, 8],
        'JUPITER': [5, 7, 9],
        'SATURN': [3, 7, 10],
        'RAHU': [5, 7, 9],
        'KETU': [5, 7, 9]
    }
    
    # Planet names standardization
    PLANET_NAMES = [
        'SUN', 'MOON', 'MERCURY', 'VENUS', 'MARS', 
        'JUPITER', 'SATURN', 'RAHU', 'KETU'
    ]
    
    # Planet aliases for input normalization
    PLANET_ALIASES = {
        'SURYA': 'SUN',
        'CHANDRA': 'MOON',
        'BUDHA': 'MERCURY',
        'SHUKRA': 'VENUS',
        'MANGAL': 'MARS',
        'GURU': 'JUPITER',
        'BRIHASPATI': 'JUPITER',
        'SHANI': 'SATURN',
        'DRAGON_HEAD': 'RAHU',
        'DRAGON_TAIL': 'KETU'
    }


class HouseConstants:
    """House-related constants and mappings"""
    
    # House numbers
    HOUSE_NUMBERS = list(range(1, 13))
    
    # House names in Tamil (written in English)
    HOUSE_NAMES = [
        'MESHAM', 'RISHABAM', 'MITHUNAM', 'KATAKAM',
        'SIMHAM', 'KANNI', 'THULAM', 'VRICHIGAM',
        'DHANUSU', 'MAKARAM', 'KUMBAM', 'MEENAM'
    ]
    
    # House ruling planets (default)
    HOUSE_RULING_PLANETS = {
        'MESHAM': 'MARS',
        'RISHABAM': 'VENUS',
        'MITHUNAM': 'MERCURY',
        'KATAKAM': 'MOON',
        'SIMHAM': 'SUN',
        'KANNI': 'MERCURY',
        'THULAM': 'VENUS',
        'VRICHIGAM': 'MARS',
        'DHANUSU': 'JUPITER',
        'MAKARAM': 'SATURN',
        'KUMBAM': 'SATURN',
        'MEENAM': 'JUPITER'
    }
    
    # Degree ranges for houses (0-30 degrees each)
    HOUSE_DEGREE_RANGES = {
        i: (i * 30, (i + 1) * 30) for i in range(12)
    }


class QueryConstants:
    """Query parsing and evaluation constants"""
    
    # Logical operators
    LOGICAL_OPERATORS = ['AND', 'OR', 'NOT']
    
    # Comparison operators
    COMPARISON_OPERATORS = ['IN', 'NOT IN', '=', '!=', '>', '<', '>=', '<=']
    
    # Aspect keywords (including common typos)
    ASPECT_KEYWORDS = ['ASPECT', 'ACPECT', 'ASPECTS']
    
    # Transit keywords
    TRANSIT_KEYWORDS = ['IN', 'TRANSITS', 'MOVES TO']
    
    # Relationship keywords
    RELATIONSHIP_KEYWORDS = ['WITH', 'TOGETHER_WITH', 'RULING_PLANET', 'WITH_STARS_OF']
    
    # Query patterns
    QUERY_PATTERNS = {
        'PLANET_IN_HOUSE': r'(\w+)\s+IN\s+(\d+)',
        'PLANET_ASPECT_HOUSE': r'(\w+)\s+ASPECT\s+(\d+)',
        'PLANET_WITH_PLANET': r'(\w+)\s+WITH\s+(\w+)',
        'KOCHARAM_FILTER': r'KOCHARAM_FILTER\((.*?)\)'
    }


class ErrorMessages:
    """Standardized error messages"""
    
    INVALID_CONDITION = "Invalid condition format"
    INVALID_PLANET = "Invalid planet name"
    INVALID_HOUSE = "Invalid house number"
    INVALID_DATE = "Invalid date format"
    CALCULATION_ERROR = "Calculation error occurred"
    DATABASE_ERROR = "Database connection error"
    TIMEOUT_ERROR = "Operation timeout"
    VALIDATION_ERROR = "Validation failed"
    
    # Error codes
    ERROR_CODES = {
        'PARSE_ERROR': 'PE001',
        'VALIDATION_ERROR': 'VE001',
        'CALCULATION_ERROR': 'CE001',
        'DATABASE_ERROR': 'DE001',
        'TIMEOUT_ERROR': 'TE001',
        'UNKNOWN_ERROR': 'UE001'
    }


class PerformanceSettings:
    """Performance optimization settings"""
    
    # Caching settings
    ENABLE_CACHING = True
    CACHE_SIZE_LIMIT = 1000
    CACHE_EXPIRY_MINUTES = 60
    
    # Parallel processing
    ENABLE_PARALLEL_PROCESSING = True
    MAX_WORKER_THREADS = 4
    
    # Memory management
    MAX_MEMORY_USAGE_MB = 512
    GARBAGE_COLLECTION_THRESHOLD = 100
    
    # Database optimization
    BATCH_SIZE = 100
    CONNECTION_POOL_SIZE = 10
    QUERY_TIMEOUT_SECONDS = 30


class ValidationRules:
    """Validation rules and constraints"""
    
    # Input validation
    MAX_CONDITION_LENGTH = 1000
    MAX_QUERY_COMPLEXITY = 10
    MAX_NESTED_CONDITIONS = 5
    
    # Date validation
    MIN_YEAR = 1900
    MAX_YEAR = 2100
    
    # Coordinate validation
    MIN_LATITUDE = -90.0
    MAX_LATITUDE = 90.0
    MIN_LONGITUDE = -180.0
    MAX_LONGITUDE = 180.0
    
    # Chart validation
    REQUIRED_CHART_FIELDS = ['houses', 'planets', 'lagna']
    REQUIRED_BIRTH_FIELDS = ['date', 'time', 'place']


# Export main configuration classes
__all__ = [
    'RuleEngineSettings',
    'PlanetaryConstants',
    'HouseConstants',
    'QueryConstants',
    'ErrorMessages',
    'PerformanceSettings',
    'ValidationRules',
    'LogLevel'
]
