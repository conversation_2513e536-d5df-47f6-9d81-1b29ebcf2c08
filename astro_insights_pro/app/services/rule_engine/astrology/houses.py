"""
House-related functions for the rule engine.
"""

from ..utils.constants import HOUSE_NAMES
from .signs import get_sign_lord


def get_house_name(house_number):
    """
    Get house name from house number.
    
    Args:
        house_number (int): House number (1-12)
        
    Returns:
        str: House name or 'Unknown' if invalid
    """
    if 1 <= house_number <= 12:
        return HOUSE_NAMES[house_number - 1]
    return 'Unknown'


def get_house_number_from_name(house_name):
    """
    Get house number from house name.
    
    Args:
        house_name (str): House name
        
    Returns:
        int: House number (1-12) or -1 if not found
    """
    try:
        return HOUSE_NAMES.index(house_name) + 1
    except ValueError:
        return -1


def get_house_sign_and_ruling_planet_from_chart(chart_data, house_number, chart_type="D1"):
    """
    Get house sign and ruling planet from chart data.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        house_number (int): House number (1-12)
        chart_type (str): Chart type (D1, D9, etc.)
        
    Returns:
        tuple: (sign_name, ruling_planet) or (None, None) if not found
    """
    try:
        if not chart_data or 'chart_data' not in chart_data:
            return None, None
        
        chart_info = chart_data['chart_data'].get(chart_type.lower(), {})
        houses = chart_info.get('houses', {})
        
        house_key = str(house_number)
        if house_key not in houses:
            return None, None
        
        house_info = houses[house_key]
        sign_name = house_info.get('sign_name', '')
        
        if not sign_name:
            return None, None
        
        ruling_planet = get_sign_lord(sign_name)
        return sign_name, ruling_planet
        
    except Exception:
        return None, None


def get_planets_in_house(chart_data, house_number, chart_type="D1"):
    """
    Get all planets in a specific house.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        house_number (int): House number (1-12)
        chart_type (str): Chart type (D1, D9, etc.)
        
    Returns:
        list: List of planet names in the house
    """
    try:
        if not chart_data or 'chart_data' not in chart_data:
            return []
        
        chart_info = chart_data['chart_data'].get(chart_type.lower(), {})
        houses = chart_info.get('houses', {})
        
        house_key = str(house_number)
        if house_key not in houses:
            return []
        
        house_info = houses[house_key]
        planets = house_info.get('planets', [])
        
        # Extract planet names
        planet_names = []
        for planet in planets:
            if isinstance(planet, dict) and 'name' in planet:
                planet_names.append(planet['name'])
            elif isinstance(planet, str):
                planet_names.append(planet)
        
        return planet_names
        
    except Exception:
        return []


def get_planet_house_mapping(chart_data, chart_type="D1"):
    """
    Get mapping of planets to their house positions.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        chart_type (str): Chart type (D1, D9, etc.)
        
    Returns:
        dict: Mapping of planet names to house numbers
    """
    planet_house_mapping = {}
    
    try:
        if not chart_data or 'chart_data' not in chart_data:
            return planet_house_mapping
        
        chart_info = chart_data['chart_data'].get(chart_type.lower(), {})
        houses = chart_info.get('houses', {})
        
        for house_num_str, house_info in houses.items():
            try:
                house_number = int(house_num_str)
                planets = house_info.get('planets', [])
                
                for planet in planets:
                    if isinstance(planet, dict) and 'name' in planet:
                        planet_name = planet['name'].upper()
                        planet_house_mapping[planet_name] = house_number
                    elif isinstance(planet, str):
                        planet_name = planet.upper()
                        planet_house_mapping[planet_name] = house_number
                        
            except (ValueError, TypeError):
                continue
                
    except Exception:
        pass
    
    return planet_house_mapping


def get_house_aspects(house_number):
    """
    Get houses that aspect the given house.
    
    Args:
        house_number (int): House number (1-12)
        
    Returns:
        list: List of house numbers that aspect the given house
    """
    if not (1 <= house_number <= 12):
        return []
    
    # Standard house aspects (7th house aspects)
    aspects = []
    
    # 7th house aspect
    seventh_house = ((house_number + 6 - 1) % 12) + 1
    aspects.append(seventh_house)
    
    return aspects


def get_house_trines(house_number):
    """
    Get trine houses (5th and 9th from given house).
    
    Args:
        house_number (int): House number (1-12)
        
    Returns:
        list: List of trine house numbers
    """
    if not (1 <= house_number <= 12):
        return []
    
    trines = []
    
    # 5th house (trine)
    fifth_house = ((house_number + 4 - 1) % 12) + 1
    trines.append(fifth_house)
    
    # 9th house (trine)
    ninth_house = ((house_number + 8 - 1) % 12) + 1
    trines.append(ninth_house)
    
    return trines


def get_house_squares(house_number):
    """
    Get square houses (4th and 10th from given house).
    
    Args:
        house_number (int): House number (1-12)
        
    Returns:
        list: List of square house numbers
    """
    if not (1 <= house_number <= 12):
        return []
    
    squares = []
    
    # 4th house (square)
    fourth_house = ((house_number + 3 - 1) % 12) + 1
    squares.append(fourth_house)
    
    # 10th house (square)
    tenth_house = ((house_number + 9 - 1) % 12) + 1
    squares.append(tenth_house)
    
    return squares


def is_angular_house(house_number):
    """
    Check if house is angular (1st, 4th, 7th, 10th).
    
    Args:
        house_number (int): House number (1-12)
        
    Returns:
        bool: True if angular house
    """
    return house_number in [1, 4, 7, 10]


def is_succedent_house(house_number):
    """
    Check if house is succedent (2nd, 5th, 8th, 11th).
    
    Args:
        house_number (int): House number (1-12)
        
    Returns:
        bool: True if succedent house
    """
    return house_number in [2, 5, 8, 11]


def is_cadent_house(house_number):
    """
    Check if house is cadent (3rd, 6th, 9th, 12th).
    
    Args:
        house_number (int): House number (1-12)
        
    Returns:
        bool: True if cadent house
    """
    return house_number in [3, 6, 9, 12]


def get_house_significance(house_number):
    """
    Get the significance/meaning of a house.
    
    Args:
        house_number (int): House number (1-12)
        
    Returns:
        str: House significance
    """
    significances = {
        1: "Self, Personality, Appearance",
        2: "Wealth, Family, Speech",
        3: "Siblings, Courage, Communication",
        4: "Home, Mother, Happiness",
        5: "Children, Education, Intelligence",
        6: "Enemies, Disease, Service",
        7: "Marriage, Partnership, Business",
        8: "Longevity, Transformation, Occult",
        9: "Fortune, Father, Religion",
        10: "Career, Status, Reputation",
        11: "Gains, Friends, Desires",
        12: "Loss, Expenses, Spirituality"
    }
    
    return significances.get(house_number, "Unknown")
