"""
Sign-related functions for the rule engine.
"""

from ..utils.constants import SIGN_NAMES, SIGN_TO_NUMBER, SIGN_TO_DEGREE


def get_standardized_sign_names():
    """
    Get standardized sign names that match MongoDB data.
    
    Returns:
        list: List of standardized sign names
    """
    return SIGN_NAMES.copy()


def get_standardized_sign_map():
    """
    Get standardized sign to number mapping.
    
    Returns:
        dict: Sign name to number mapping
    """
    return SIGN_TO_NUMBER.copy()


def get_sign_name_from_longitude(longitude):
    """
    Get sign name from longitude using degree ranges.
    
    Args:
        longitude (float): Planet longitude in degrees (0-360)
        
    Returns:
        str: Sign name based on degree range
    """
    # Normalize longitude to 0-360 range
    longitude = longitude % 360
    
    # Direct degree-to-sign mapping (each sign is 30 degrees)
    sign_index = int(longitude // 30)
    
    if 0 <= sign_index < len(SIGN_NAMES):
        return SIGN_NAMES[sign_index]
    
    return 'Unknown'


def get_sign_start_degree(sign_name):
    """
    Get the starting degree of a sign.
    
    Args:
        sign_name (str): Sign name
        
    Returns:
        float: Starting degree of the sign (0° of the sign)
    """
    return SIGN_TO_DEGREE.get(sign_name, 0)


def get_sign_number_from_name(sign_name):
    """
    Get sign number from sign name.
    
    Args:
        sign_name (str): Sign name
        
    Returns:
        int: Sign number (0-11) or -1 if not found
    """
    return SIGN_TO_NUMBER.get(sign_name, -1)


def get_sign_name_from_number(sign_number):
    """
    Get sign name from sign number.
    
    Args:
        sign_number (int): Sign number (0-11)
        
    Returns:
        str: Sign name or 'Unknown' if invalid
    """
    if 0 <= sign_number < len(SIGN_NAMES):
        return SIGN_NAMES[sign_number]
    return 'Unknown'


def calculate_sign_distance(from_sign, to_sign):
    """
    Calculate the distance between two signs.
    
    Args:
        from_sign (str): Starting sign name
        to_sign (str): Ending sign name
        
    Returns:
        int: Distance in signs (0-11)
    """
    from_num = get_sign_number_from_name(from_sign)
    to_num = get_sign_number_from_name(to_sign)
    
    if from_num == -1 or to_num == -1:
        return -1
    
    distance = (to_num - from_num) % 12
    return distance


def get_opposite_sign(sign_name):
    """
    Get the opposite sign (7th from given sign).
    
    Args:
        sign_name (str): Sign name
        
    Returns:
        str: Opposite sign name
    """
    sign_num = get_sign_number_from_name(sign_name)
    if sign_num == -1:
        return 'Unknown'
    
    opposite_num = (sign_num + 6) % 12
    return get_sign_name_from_number(opposite_num)


def is_same_sign(longitude1, longitude2):
    """
    Check if two longitudes are in the same sign.
    
    Args:
        longitude1 (float): First longitude
        longitude2 (float): Second longitude
        
    Returns:
        bool: True if both longitudes are in the same sign
    """
    sign1 = get_sign_name_from_longitude(longitude1)
    sign2 = get_sign_name_from_longitude(longitude2)
    return sign1 == sign2 and sign1 != 'Unknown'


def get_sign_lord(sign_name):
    """
    Get the ruling planet of a sign.
    
    Args:
        sign_name (str): Sign name
        
    Returns:
        str: Ruling planet name
    """
    sign_lords = {
        'Mesham': 'Mars',        # Aries
        'Rishabam': 'Venus',     # Taurus
        'Midunam': 'Mercury',    # Gemini
        'Kadagam': 'Moon',       # Cancer
        'Simmam': 'Sun',         # Leo
        'Kanni': 'Mercury',      # Virgo
        'Thulam': 'Venus',       # Libra
        'Virichigam': 'Mars',    # Scorpio
        'Dhanusu': 'Jupiter',    # Sagittarius
        'MAGARAM': 'Saturn',     # Capricorn
        'Kumbam': 'Saturn',      # Aquarius
        'Meenam': 'Jupiter'      # Pisces
    }
    
    return sign_lords.get(sign_name, 'Unknown')


def get_sign_element(sign_name):
    """
    Get the element of a sign.
    
    Args:
        sign_name (str): Sign name
        
    Returns:
        str: Element (Fire, Earth, Air, Water)
    """
    elements = {
        'Mesham': 'Fire', 'Simmam': 'Fire', 'Dhanusu': 'Fire',
        'Rishabam': 'Earth', 'Kanni': 'Earth', 'MAGARAM': 'Earth',
        'Midunam': 'Air', 'Thulam': 'Air', 'Kumbam': 'Air',
        'Kadagam': 'Water', 'Virichigam': 'Water', 'Meenam': 'Water'
    }
    
    return elements.get(sign_name, 'Unknown')


def get_sign_quality(sign_name):
    """
    Get the quality of a sign.
    
    Args:
        sign_name (str): Sign name
        
    Returns:
        str: Quality (Cardinal, Fixed, Mutable)
    """
    qualities = {
        'Mesham': 'Cardinal', 'Kadagam': 'Cardinal', 'Thulam': 'Cardinal', 'MAGARAM': 'Cardinal',
        'Rishabam': 'Fixed', 'Simmam': 'Fixed', 'Virichigam': 'Fixed', 'Kumbam': 'Fixed',
        'Midunam': 'Mutable', 'Kanni': 'Mutable', 'Dhanusu': 'Mutable', 'Meenam': 'Mutable'
    }
    
    return qualities.get(sign_name, 'Unknown')
