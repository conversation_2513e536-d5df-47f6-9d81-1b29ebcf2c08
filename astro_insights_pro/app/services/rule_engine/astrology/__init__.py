"""
Astrology module for the rule engine.
"""

from .signs import (
    get_standardized_sign_names, get_standardized_sign_map,
    get_sign_name_from_longitude, get_sign_start_degree,
    get_sign_number_from_name, get_sign_name_from_number,
    calculate_sign_distance, get_opposite_sign, is_same_sign,
    get_sign_lord, get_sign_element, get_sign_quality
)

from .houses import (
    get_house_name, get_house_number_from_name,
    get_house_sign_and_ruling_planet_from_chart,
    get_planets_in_house, get_planet_house_mapping,
    get_house_aspects, get_house_trines, get_house_squares,
    is_angular_house, is_succedent_house, is_cadent_house,
    get_house_significance
)

from .planets import (
    get_planet_position, get_planet_house, get_planet_sign,
    get_planet_longitude, get_planet_aspects, is_planet_aspecting_house,
    get_planet_strength, get_planet_speed, is_retrograde_planet,
    get_planet_nature
)

__all__ = [
    # Signs
    'get_standardized_sign_names', 'get_standardized_sign_map',
    'get_sign_name_from_longitude', 'get_sign_start_degree',
    'get_sign_number_from_name', 'get_sign_name_from_number',
    'calculate_sign_distance', 'get_opposite_sign', 'is_same_sign',
    'get_sign_lord', 'get_sign_element', 'get_sign_quality',
    
    # Houses
    'get_house_name', 'get_house_number_from_name',
    'get_house_sign_and_ruling_planet_from_chart',
    'get_planets_in_house', 'get_planet_house_mapping',
    'get_house_aspects', 'get_house_trines', 'get_house_squares',
    'is_angular_house', 'is_succedent_house', 'is_cadent_house',
    'get_house_significance',
    
    # Planets
    'get_planet_position', 'get_planet_house', 'get_planet_sign',
    'get_planet_longitude', 'get_planet_aspects', 'is_planet_aspecting_house',
    'get_planet_strength', 'get_planet_speed', 'is_retrograde_planet',
    'get_planet_nature'
]
