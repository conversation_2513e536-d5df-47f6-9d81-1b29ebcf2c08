"""
Planet-related functions for the rule engine.
"""

from ..utils.constants import PLANET_NAMES, PLANET_SPEEDS
from ..utils.helpers import normalize_planet_name


def get_planet_position(chart_data, planet_name, chart_type="D1"):
    """
    Get planet position from chart data.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        planet_name (str): Planet name
        chart_type (str): Chart type (D1, D9, etc.)
        
    Returns:
        dict: Planet position info or None if not found
    """
    try:
        if not chart_data or 'chart_data' not in chart_data:
            return None
        
        chart_info = chart_data['chart_data'].get(chart_type.lower(), {})
        houses = chart_info.get('houses', {})
        
        planet_name = normalize_planet_name(planet_name)
        
        for house_num_str, house_info in houses.items():
            planets = house_info.get('planets', [])
            
            for planet in planets:
                if isinstance(planet, dict):
                    name = normalize_planet_name(planet.get('name', ''))
                    if name == planet_name:
                        return {
                            'house': int(house_num_str),
                            'sign': house_info.get('sign_name', ''),
                            'longitude': planet.get('longitude', 0),
                            'degree': planet.get('degree', 0),
                            'nakshatra': planet.get('nakshatra', ''),
                            'pada': planet.get('pada', 0)
                        }
                elif isinstance(planet, str):
                    name = normalize_planet_name(planet)
                    if name == planet_name:
                        return {
                            'house': int(house_num_str),
                            'sign': house_info.get('sign_name', ''),
                            'longitude': 0,
                            'degree': 0,
                            'nakshatra': '',
                            'pada': 0
                        }
        
        return None
        
    except Exception:
        return None


def get_planet_house(chart_data, planet_name, chart_type="D1"):
    """
    Get the house number where a planet is placed.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        planet_name (str): Planet name
        chart_type (str): Chart type (D1, D9, etc.)
        
    Returns:
        int: House number or None if not found
    """
    position = get_planet_position(chart_data, planet_name, chart_type)
    return position['house'] if position else None


def get_planet_sign(chart_data, planet_name, chart_type="D1"):
    """
    Get the sign where a planet is placed.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        planet_name (str): Planet name
        chart_type (str): Chart type (D1, D9, etc.)
        
    Returns:
        str: Sign name or None if not found
    """
    position = get_planet_position(chart_data, planet_name, chart_type)
    return position['sign'] if position else None


def get_planet_longitude(chart_data, planet_name, chart_type="D1"):
    """
    Get the longitude of a planet.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        planet_name (str): Planet name
        chart_type (str): Chart type (D1, D9, etc.)
        
    Returns:
        float: Planet longitude or None if not found
    """
    position = get_planet_position(chart_data, planet_name, chart_type)
    return position['longitude'] if position else None


def get_planet_aspects(planet_name):
    """
    Get the aspects of a planet.
    
    Args:
        planet_name (str): Planet name
        
    Returns:
        list: List of aspect numbers (houses from planet)
    """
    planet_name = normalize_planet_name(planet_name)
    
    # Standard aspects for all planets (7th house aspect)
    aspects = [7]
    
    # Special aspects for specific planets
    special_aspects = {
        'MARS': [4, 8],      # 4th and 8th aspects
        'JUPITER': [5, 9],   # 5th and 9th aspects  
        'SATURN': [3, 10]    # 3rd and 10th aspects
    }
    
    if planet_name in special_aspects:
        aspects.extend(special_aspects[planet_name])
    
    return aspects


def is_planet_aspecting_house(chart_data, planet_name, target_house, chart_type="D1"):
    """
    Check if a planet is aspecting a specific house.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        planet_name (str): Planet name
        target_house (int): Target house number
        chart_type (str): Chart type (D1, D9, etc.)
        
    Returns:
        bool: True if planet aspects the house
    """
    planet_house = get_planet_house(chart_data, planet_name, chart_type)
    if not planet_house:
        return False
    
    aspects = get_planet_aspects(planet_name)
    
    for aspect in aspects:
        aspected_house = ((planet_house + aspect - 1) % 12) + 1
        if aspected_house == target_house:
            return True
    
    return False


def get_planet_strength(chart_data, planet_name, chart_type="D1"):
    """
    Get basic strength assessment of a planet.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        planet_name (str): Planet name
        chart_type (str): Chart type (D1, D9, etc.)
        
    Returns:
        dict: Strength assessment
    """
    position = get_planet_position(chart_data, planet_name, chart_type)
    if not position:
        return {'strength': 'Unknown', 'factors': []}
    
    strength_factors = []
    strength_score = 0
    
    # House strength (angular houses are stronger)
    house = position['house']
    if house in [1, 4, 7, 10]:  # Angular houses
        strength_factors.append('Angular house placement')
        strength_score += 3
    elif house in [2, 5, 8, 11]:  # Succedent houses
        strength_factors.append('Succedent house placement')
        strength_score += 2
    else:  # Cadent houses
        strength_factors.append('Cadent house placement')
        strength_score += 1
    
    # Sign strength (own sign, exaltation, etc.)
    sign = position['sign']
    planet_name_norm = normalize_planet_name(planet_name)
    
    # Own signs
    own_signs = {
        'SUN': ['Simmam'],
        'MOON': ['Kadagam'],
        'MARS': ['Mesham', 'Virichigam'],
        'MERCURY': ['Midunam', 'Kanni'],
        'JUPITER': ['Dhanusu', 'Meenam'],
        'VENUS': ['Rishabam', 'Thulam'],
        'SATURN': ['MAGARAM', 'Kumbam']
    }
    
    if planet_name_norm in own_signs and sign in own_signs[planet_name_norm]:
        strength_factors.append('Own sign placement')
        strength_score += 4
    
    # Exaltation signs
    exaltation_signs = {
        'SUN': 'Mesham',
        'MOON': 'Rishabam',
        'MARS': 'MAGARAM',
        'MERCURY': 'Kanni',
        'JUPITER': 'Kadagam',
        'VENUS': 'Meenam',
        'SATURN': 'Thulam'
    }
    
    if planet_name_norm in exaltation_signs and sign == exaltation_signs[planet_name_norm]:
        strength_factors.append('Exaltation placement')
        strength_score += 5
    
    # Determine overall strength
    if strength_score >= 7:
        strength = 'Very Strong'
    elif strength_score >= 5:
        strength = 'Strong'
    elif strength_score >= 3:
        strength = 'Average'
    else:
        strength = 'Weak'
    
    return {
        'strength': strength,
        'score': strength_score,
        'factors': strength_factors,
        'house': house,
        'sign': sign
    }


def get_planet_speed(planet_name):
    """
    Get the average speed of a planet in degrees per day.
    
    Args:
        planet_name (str): Planet name
        
    Returns:
        float: Speed in degrees per day
    """
    planet_name = normalize_planet_name(planet_name)
    return PLANET_SPEEDS.get(planet_name, 1.0)


def is_retrograde_planet(planet_name):
    """
    Check if a planet can be retrograde.
    
    Args:
        planet_name (str): Planet name
        
    Returns:
        bool: True if planet can be retrograde
    """
    planet_name = normalize_planet_name(planet_name)
    # Sun and Moon are never retrograde
    return planet_name not in ['SUN', 'MOON']


def get_planet_nature(planet_name):
    """
    Get the basic nature of a planet.
    
    Args:
        planet_name (str): Planet name
        
    Returns:
        dict: Planet nature information
    """
    planet_name = normalize_planet_name(planet_name)
    
    natures = {
        'SUN': {'type': 'Malefic', 'element': 'Fire', 'gender': 'Male'},
        'MOON': {'type': 'Benefic', 'element': 'Water', 'gender': 'Female'},
        'MARS': {'type': 'Malefic', 'element': 'Fire', 'gender': 'Male'},
        'MERCURY': {'type': 'Neutral', 'element': 'Earth', 'gender': 'Neutral'},
        'JUPITER': {'type': 'Benefic', 'element': 'Ether', 'gender': 'Male'},
        'VENUS': {'type': 'Benefic', 'element': 'Water', 'gender': 'Female'},
        'SATURN': {'type': 'Malefic', 'element': 'Air', 'gender': 'Neutral'},
        'RAHU': {'type': 'Malefic', 'element': 'Air', 'gender': 'Neutral'},
        'KETU': {'type': 'Malefic', 'element': 'Fire', 'gender': 'Neutral'}
    }
    
    return natures.get(planet_name, {'type': 'Unknown', 'element': 'Unknown', 'gender': 'Unknown'})
