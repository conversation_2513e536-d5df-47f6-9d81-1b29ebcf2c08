"""
Helper functions for the rule engine.

Enhanced with new utility classes for improved functionality.
"""

import logging
import re
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, timedelta
from dateutil import parser
import math

from .constants import SUCCESS_RATINGS


def get_success_rating(percentage):
    """
    Get success rating based on percentage.
    
    Args:
        percentage (float): Success percentage
        
    Returns:
        str: Success rating
    """
    for (min_val, max_val), rating in SUCCESS_RATINGS.items():
        if min_val <= percentage <= max_val:
            return rating
    return "Unknown"


def normalize_planet_name(planet_name):
    """
    Normalize planet name to standard format.
    
    Args:
        planet_name (str): Planet name in any format
        
    Returns:
        str: Normalized planet name
    """
    if not planet_name:
        return ""
    
    planet_name = planet_name.strip().upper()
    
    # Handle common variations
    variations = {
        'JUPITER': ['GURU', 'BRIHASPATI'],
        'VENUS': ['SHUKRA'],
        'SATURN': ['SHANI'],
        'MARS': ['MANG<PERSON>', 'ANGARA<PERSON>'],
        'MERCURY': ['BUDHA'],
        'SUN': ['SURYA', 'RAVI'],
        'MOON': ['CHANDRA']
    }
    
    for standard_name, variants in variations.items():
        if planet_name in variants:
            return standard_name
    
    return planet_name


def normalize_house_number(house_input):
    """
    Normalize house input to integer.
    
    Args:
        house_input: House number as string or int
        
    Returns:
        int: House number (1-12) or None if invalid
    """
    try:
        if isinstance(house_input, str):
            # Extract number from strings like "1st_House", "7th_House_Planet"
            import re
            match = re.search(r'(\d+)', house_input)
            if match:
                house_num = int(match.group(1))
            else:
                return None
        else:
            house_num = int(house_input)
        
        # Validate house number range
        if 1 <= house_num <= 12:
            return house_num
        return None
    except (ValueError, TypeError):
        return None


def clean_query_string(query):
    """
    Clean and normalize query string.
    
    Args:
        query (str): Raw query string
        
    Returns:
        str: Cleaned query string
    """
    if not query:
        return ""
    
    import re
    
    # Remove extra whitespace
    query = re.sub(r'\s+', ' ', query.strip())
    
    # Normalize logical operators
    query = re.sub(r'\bAND\b', 'AND', query, flags=re.IGNORECASE)
    query = re.sub(r'\bOR\b', 'OR', query, flags=re.IGNORECASE)
    query = re.sub(r'\bNOT\b', 'NOT', query, flags=re.IGNORECASE)
    
    return query


def validate_chart_type(chart_type):
    """
    Validate chart type.
    
    Args:
        chart_type (str): Chart type
        
    Returns:
        str: Valid chart type or 'D1' as default
    """
    from .constants import CHART_TYPES
    
    if not chart_type:
        return 'D1'
    
    chart_type = chart_type.upper()
    return chart_type if chart_type in CHART_TYPES else 'D1'


def format_duration(days):
    """
    Format duration in days to human-readable format.
    
    Args:
        days (int): Number of days
        
    Returns:
        dict: Formatted duration
    """
    if not days or days < 0:
        return {"days": 0, "months": 0, "years": 0, "formatted": "0 days"}
    
    years = days // 365
    remaining_days = days % 365
    months = remaining_days // 30
    remaining_days = remaining_days % 30
    
    return {
        "days": remaining_days,
        "months": months,
        "years": years,
        "formatted": f"{years} years, {months} months, {remaining_days} days"
    }


# Enhanced Utility Classes for Rule Engine v2.0

class ValidationUtils:
    """Utility functions for input validation"""

    @staticmethod
    def validate_planet_name(planet_name: str) -> bool:
        """Validate planet name"""
        if not planet_name or not isinstance(planet_name, str):
            return False

        from .constants import PLANET_NAMES
        normalized_name = normalize_planet_name(planet_name)
        return normalized_name in PLANET_NAMES

    @staticmethod
    def validate_house_number(house_number: Union[int, str]) -> bool:
        """Validate house number"""
        normalized = normalize_house_number(house_number)
        return normalized is not None

    @staticmethod
    def validate_date_string(date_string: str) -> bool:
        """Validate date string format"""
        try:
            parsed_date = parser.parse(date_string)
            return 1900 <= parsed_date.year <= 2100
        except (ValueError, TypeError):
            return False

    @staticmethod
    def validate_longitude(longitude: Union[float, int]) -> bool:
        """Validate longitude value"""
        try:
            lon = float(longitude)
            return 0.0 <= lon <= 360.0
        except (ValueError, TypeError):
            return False


class NormalizationUtils:
    """Utility functions for data normalization"""

    @staticmethod
    def normalize_planet_name(planet_name: str) -> str:
        """Normalize planet name to standard format"""
        return normalize_planet_name(planet_name)

    @staticmethod
    def normalize_house_name(house_name: str) -> str:
        """Normalize house name to standard format"""
        if not house_name:
            return ""
        return house_name.upper().strip()

    @staticmethod
    def normalize_condition_string(condition: str) -> str:
        """Normalize condition string for parsing"""
        return clean_query_string(condition)


class CalculationUtils:
    """Utility functions for astronomical calculations"""

    @staticmethod
    def normalize_angle(angle: float) -> float:
        """Normalize angle to 0-360 degrees"""
        return angle % 360.0

    @staticmethod
    def calculate_angular_distance(angle1: float, angle2: float) -> float:
        """Calculate the shortest angular distance between two angles"""
        diff = abs(angle1 - angle2)
        return min(diff, 360.0 - diff)

    @staticmethod
    def degrees_to_dms(degrees: float) -> str:
        """Convert decimal degrees to degrees, minutes, seconds format"""
        try:
            deg = int(degrees)
            minutes_decimal = (degrees - deg) * 60
            min_val = int(minutes_decimal)
            seconds = (minutes_decimal - min_val) * 60
            sec_val = int(seconds)

            return f"{deg}° {min_val}' {sec_val}\""
        except (ValueError, TypeError):
            return f"{degrees:.2f}°"


class DateTimeUtils:
    """Utility functions for date and time operations"""

    @staticmethod
    def parse_date_string(date_string: str) -> Optional[datetime]:
        """Parse date string to datetime object"""
        try:
            return parser.parse(date_string)
        except (ValueError, TypeError):
            return None

    @staticmethod
    def format_datetime(dt: datetime, format_string: str = "%Y-%m-%d %H:%M:%S") -> str:
        """Format datetime object to string"""
        try:
            return dt.strftime(format_string)
        except (ValueError, AttributeError):
            return str(dt)


class LoggingUtils:
    """Utility functions for logging and debugging"""

    @staticmethod
    def setup_logger(name: str, level: int = logging.INFO) -> logging.Logger:
        """Setup logger with standard configuration"""
        logger = logging.getLogger(name)
        logger.setLevel(level)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    @staticmethod
    def log_performance(operation_name: str, start_time: datetime, end_time: datetime):
        """Log performance metrics for an operation"""
        duration = (end_time - start_time).total_seconds()
        logger = logging.getLogger(__name__)
        logger.info(f"Performance: {operation_name} completed in {duration:.3f} seconds")


class ErrorUtils:
    """Utility functions for error handling"""

    @staticmethod
    def create_error_response(error_code: str, message: str, details: Dict = None) -> Dict:
        """Create standardized error response"""
        return {
            "success": False,
            "error_code": error_code,
            "message": message,
            "details": details or {},
            "timestamp": datetime.now().isoformat()
        }

    @staticmethod
    def create_success_response(data: Any, message: str = "Operation successful") -> Dict:
        """Create standardized success response"""
        return {
            "success": True,
            "message": message,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
