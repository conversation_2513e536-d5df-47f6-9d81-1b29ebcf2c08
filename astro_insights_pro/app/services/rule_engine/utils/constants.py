"""
Constants and mappings for the rule engine.
"""

# Standardized sign names that match MongoDB data
SIGN_NAMES = [
    '<PERSON><PERSON>', '<PERSON><PERSON>abam', 'Miduna<PERSON>', 'Kadagam', 'Simmam', '<PERSON>nni',
    '<PERSON>hul<PERSON>', '<PERSON>irichi<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>GARA<PERSON>', '<PERSON>mba<PERSON>', '<PERSON>ena<PERSON>'
]

# Sign to number mapping
SIGN_TO_NUMBER = {
    'Mesham': 0, 'Rishabam': 1, 'Midunam': 2, 'Kadagam': 3,
    'Simmam': 4, 'Kanni': 5, 'Thulam': 6, 'Virichigam': 7,
    'Dhanusu': 8, 'MAGARAM': 9, 'Kumbam': 10, 'Meenam': 11
}

# Sign to starting degree mapping
SIGN_TO_DEGREE = {
    'Mesham': 0, 'Rishabam': 30, 'Midunam': 60, 'Kadagam': 90,
    'Simmam': 120, 'Kanni': 150, 'Thulam': 180, 'Virichigam': 210,
    '<PERSON>han<PERSON><PERSON>': 240, 'MAGARAM': 270, '<PERSON>mba<PERSON>': 300, 'Meena<PERSON>': 330
}

# Planet names mapping
PLANET_NAMES = {
    'SUN': 'Sun', 'MOON': 'Moon', 'MARS': 'Mars', 'MERCURY': 'Mercury',
    'JUPITER': 'Jupiter', 'VENUS': 'Venus', 'SATURN': 'Saturn',
    'RAHU': 'Rahu', 'KETU': 'Ketu'
}

# House names in Tamil (English transliteration)
HOUSE_NAMES = [
    'Lagna', 'Dhana', 'Sahaja', 'Sukha', 'Putra', 'Ari',
    'Kalatra', 'Ayu', 'Bhagya', 'Karma', 'Labha', 'Vyaya'
]

# Dasha patterns for query routing
DASHA_PATTERNS = [
    'Bhukti_Dates', 'Dasa_Dates', 'House_Ruling_Planet Bhukti_Dates',
    'House_Ruling_Planet Dasa_Dates', 'VENUS Bhukti_Dates',
    'JUPITER Bhukti_Dates', 'Member_Age', 'PREDICTION_DURATION'
]

# Logical operators
LOGICAL_OPERATORS = ['OR', 'AND', 'NOT']

# Relationship operators
RELATIONSHIP_OPERATORS = [
    'IS_RELATED_TO', 'IS_ASPECTING_BIRTH', 'WITH_RULING_PLANET',
    'TOGETHER_WITH', 'WITH_STARS_OF', 'IN', 'NOT_IN'
]

# Planet transit speeds (degrees per day - approximate)
PLANET_SPEEDS = {
    'SUN': 1.0, 'MOON': 13.2, 'MARS': 0.5, 'MERCURY': 1.4,
    'JUPITER': 0.083, 'VENUS': 1.2, 'SATURN': 0.033,
    'RAHU': -0.053, 'KETU': -0.053  # Retrograde motion
}

# Default prediction duration in years
DEFAULT_PREDICTION_DURATION = 2

# Chart types supported
CHART_TYPES = ['D1', 'D2', 'D3', 'D4', 'D7', 'D9', 'D10', 'D12', 'D16', 'D20', 'D24', 'D27', 'D30']

# Success rating thresholds
SUCCESS_RATINGS = {
    (90, 100): "Excellent",
    (80, 89): "Very Good", 
    (70, 79): "Good",
    (60, 69): "Average",
    (50, 59): "Below Average",
    (0, 49): "Poor"
}
