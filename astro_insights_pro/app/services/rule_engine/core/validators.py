"""
Input validation functions for the rule engine.
"""

from bson import ObjectId
from ..utils.helpers import normalize_house_number, validate_chart_type


def validate_user_profile_id(user_profile_id):
    """
    Validate user profile ID.
    
    Args:
        user_profile_id: User profile ID (int or ObjectId)
        
    Returns:
        tuple: (is_valid, normalized_id, error_message)
    """
    if user_profile_id is None:
        return False, None, "User profile ID is required"
    
    try:
        if isinstance(user_profile_id, str):
            if user_profile_id.isdigit():
                return True, int(user_profile_id), None
            else:
                # Try ObjectId
                ObjectId(user_profile_id)
                return True, user_profile_id, None
        elif isinstance(user_profile_id, int):
            return True, user_profile_id, None
        elif isinstance(user_profile_id, ObjectId):
            return True, str(user_profile_id), None
        else:
            return False, None, "Invalid user profile ID format"
    except Exception:
        return False, None, "Invalid user profile ID"


def validate_member_profile_id(member_profile_id):
    """
    Validate member profile ID.
    
    Args:
        member_profile_id: Member profile ID (int or ObjectId)
        
    Returns:
        tuple: (is_valid, normalized_id, error_message)
    """
    if member_profile_id is None:
        return False, None, "Member profile ID is required"
    
    try:
        if isinstance(member_profile_id, str):
            if member_profile_id.isdigit():
                return True, int(member_profile_id), None
            else:
                # Try ObjectId
                ObjectId(member_profile_id)
                return True, member_profile_id, None
        elif isinstance(member_profile_id, int):
            return True, member_profile_id, None
        elif isinstance(member_profile_id, ObjectId):
            return True, str(member_profile_id), None
        else:
            return False, None, "Invalid member profile ID format"
    except Exception:
        return False, None, "Invalid member profile ID"


def validate_query_string(query):
    """
    Validate query string.
    
    Args:
        query (str): Query string
        
    Returns:
        tuple: (is_valid, cleaned_query, error_message)
    """
    if not query:
        return False, None, "Query is required"
    
    if not isinstance(query, str):
        return False, None, "Query must be a string"
    
    query = query.strip()
    if len(query) == 0:
        return False, None, "Query cannot be empty"
    
    if len(query) > 1000:
        return False, None, "Query is too long (max 1000 characters)"
    
    return True, query, None


def validate_chart_type_input(chart_type):
    """
    Validate chart type input.
    
    Args:
        chart_type (str): Chart type
        
    Returns:
        tuple: (is_valid, normalized_chart_type, error_message)
    """
    if not chart_type:
        return True, 'D1', None  # Default to D1
    
    if not isinstance(chart_type, str):
        return False, None, "Chart type must be a string"
    
    normalized_chart_type = validate_chart_type(chart_type)
    return True, normalized_chart_type, None


def validate_house_number_input(house_input):
    """
    Validate house number input.
    
    Args:
        house_input: House number input
        
    Returns:
        tuple: (is_valid, house_number, error_message)
    """
    house_number = normalize_house_number(house_input)
    
    if house_number is None:
        return False, None, f"Invalid house number: {house_input}"
    
    return True, house_number, None


def validate_planet_name_input(planet_name):
    """
    Validate planet name input.
    
    Args:
        planet_name (str): Planet name
        
    Returns:
        tuple: (is_valid, normalized_planet_name, error_message)
    """
    if not planet_name:
        return False, None, "Planet name is required"
    
    if not isinstance(planet_name, str):
        return False, None, "Planet name must be a string"
    
    from ..utils.helpers import normalize_planet_name
    normalized_name = normalize_planet_name(planet_name)
    
    if not normalized_name:
        return False, None, f"Invalid planet name: {planet_name}"
    
    valid_planets = ['SUN', 'MOON', 'MARS', 'MERCURY', 'JUPITER', 'VENUS', 'SATURN', 'RAHU', 'KETU']
    if normalized_name not in valid_planets:
        return False, None, f"Unsupported planet: {planet_name}"
    
    return True, normalized_name, None


def validate_api_request_data(data):
    """
    Validate complete API request data.
    
    Args:
        data (dict): Request data
        
    Returns:
        tuple: (is_valid, validated_data, error_message)
    """
    if not data:
        return False, None, "Request data is required"
    
    if not isinstance(data, dict):
        return False, None, "Request data must be a JSON object"
    
    validated_data = {}
    
    # Validate user_profile_id
    user_profile_id = data.get('user_profile_id')
    is_valid, normalized_id, error = validate_user_profile_id(user_profile_id)
    if not is_valid:
        return False, None, error
    validated_data['user_profile_id'] = normalized_id
    
    # Validate member_profile_id
    member_profile_id = data.get('member_profile_id')
    is_valid, normalized_id, error = validate_member_profile_id(member_profile_id)
    if not is_valid:
        return False, None, error
    validated_data['member_profile_id'] = normalized_id
    
    # Validate query
    query = data.get('query')
    is_valid, cleaned_query, error = validate_query_string(query)
    if not is_valid:
        return False, None, error
    validated_data['query'] = cleaned_query
    
    # Validate chart_type (optional)
    chart_type = data.get('chart_type', 'D1')
    is_valid, normalized_chart_type, error = validate_chart_type_input(chart_type)
    if not is_valid:
        return False, None, error
    validated_data['chart_type'] = normalized_chart_type
    
    return True, validated_data, None


def validate_dasha_query_parameters(data):
    """
    Validate dasha query specific parameters.
    
    Args:
        data (dict): Query data
        
    Returns:
        tuple: (is_valid, validated_params, error_message)
    """
    validated_params = {}
    
    # Check for age parameters
    if 'Member_Age' in data.get('query', ''):
        # Extract age range from query if present
        import re
        query = data['query']
        
        # Look for age patterns like "Member_Age >= 25 AND <= 35"
        age_pattern = r'Member_Age\s*>=\s*(\d+)\s*AND\s*<=\s*(\d+)'
        match = re.search(age_pattern, query)
        
        if match:
            min_age = int(match.group(1))
            max_age = int(match.group(2))
            
            if min_age < 0 or max_age < 0:
                return False, None, "Age values must be positive"
            
            if min_age > max_age:
                return False, None, "Minimum age cannot be greater than maximum age"
            
            if max_age > 120:
                return False, None, "Maximum age seems unrealistic (>120)"
            
            validated_params['min_age'] = min_age
            validated_params['max_age'] = max_age
    
    # Check for prediction duration
    if 'PREDICTION_DURATION' in data.get('query', ''):
        import re
        query = data['query']
        
        # Look for duration patterns like "PREDICTION_DURATION = 10"
        duration_pattern = r'PREDICTION_DURATION\s*=\s*(\d+)'
        match = re.search(duration_pattern, query)
        
        if match:
            duration = int(match.group(1))
            
            if duration <= 0:
                return False, None, "Prediction duration must be positive"
            
            if duration > 50:
                return False, None, "Prediction duration too long (>50 years)"
            
            validated_params['prediction_duration'] = duration
    
    return True, validated_params, None
