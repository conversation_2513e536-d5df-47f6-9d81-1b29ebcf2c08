"""
Rule Engine Package - Enhanced Implementation v2.0

This package contains the enhanced rule engine implementation with improved
architecture, performance optimizations, and comprehensive error handling.

Version: 2.0
Author: Fortune Lens Team
Last Updated: 2025-07-31

Features:
- Modular architecture with separate config and utilities
- Enhanced performance optimization with caching
- Comprehensive error handling and validation
- Type safety with full type annotations
- Backward compatibility with existing APIs
"""

# Import configuration and utilities
from .config import (
    RuleEngineSettings, PlanetaryConstants, HouseConstants,
    QueryConstants, ErrorMessages, PerformanceSettings, ValidationRules
)

from .utils import (
    ValidationUtils, NormalizationUtils, CalculationUtils,
    DateTimeUtils, LoggingUtils, ErrorUtils
)

# Import processors
from .processors import (
    KocharamProcessor, DashaProcessor
)

# Import enhanced classes from API
from .api.main_rule_engine import (
    PerformanceOptimizer, ErrorHandler, ConditionParser,
    QueryType, LogicalOperator, ParsedCondition, TransitResult
)

# Import main API functions (backward compatibility)
from .api import (
    parse_and_evaluate_dasha_query,
    evaluate_rule,
    get_chart_data,
    get_success_rating,
    get_planet_house_mapping,
    get_house_sign_and_ruling_planet_from_chart,
    check_comprehensive_relationship,
    validate_api_request_data,
    process_rule_engine_request,
    HOUSE_RULING_PLANETS,
    PLANET_NAMES,
    get_standardized_sign_names,
    get_standardized_sign_map,
    get_sign_name_from_longitude,
    get_sign_start_degree,
    parse_kocharam_condition,
    parse_single_kocharam_condition,
    parse_complex_kocharam_condition,
    process_complex_kocharam_filter
)


# Export main functions and classes for API
__all__ = [
    # Configuration classes
    'RuleEngineSettings',
    'PlanetaryConstants',
    'HouseConstants',
    'QueryConstants',
    'ErrorMessages',
    'PerformanceSettings',
    'ValidationRules',

    # Utility classes
    'ValidationUtils',
    'NormalizationUtils',
    'CalculationUtils',
    'DateTimeUtils',
    'LoggingUtils',
    'ErrorUtils',

    # Processor classes
    'KocharamProcessor',
    'DashaProcessor',

    # Enhanced classes
    'PerformanceOptimizer',
    'ErrorHandler',
    'ConditionParser',
    'QueryType',
    'LogicalOperator',
    'ParsedCondition',
    'TransitResult',

    # Main API functions (backward compatibility)
    'process_rule_engine_request',
    'validate_api_request_data',
    'parse_and_evaluate_dasha_query',
    'evaluate_rule',
    'get_chart_data',
    'get_success_rating',
    'get_planet_house_mapping',
    'get_house_sign_and_ruling_planet_from_chart',
    'check_comprehensive_relationship',
    'HOUSE_RULING_PLANETS',
    'PLANET_NAMES',
    'get_standardized_sign_names',
    'get_standardized_sign_map',
    'get_sign_name_from_longitude',
    'get_sign_start_degree',
    'parse_kocharam_condition',
    'parse_single_kocharam_condition',
    'parse_complex_kocharam_condition',
    'process_complex_kocharam_filter'
]
