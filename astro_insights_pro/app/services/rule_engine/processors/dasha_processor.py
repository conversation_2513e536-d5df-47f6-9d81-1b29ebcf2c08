"""
Dasha Processor Module

This module contains all dasha-related processing functionality
extracted from the main rule engine for better modularity.

Version: 2.0
Author: Fortune Lens Team
Last Updated: 2025-07-31
"""

import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dateutil import parser

from ..config.settings import RuleEngineSettings, PlanetaryConstants, HouseConstants
from ..utils import ValidationUtils, DateTimeUtils, ErrorUtils
from .kocharam_processor import KocharamProcessor

logger = logging.getLogger(__name__)


class DashaProcessor:
    """Enhanced dasha processor with improved parsing and evaluation"""
    
    def __init__(self):
        self.kocharam_processor = KocharamProcessor()
        
        # Dasha patterns for recognition
        self.dasha_patterns = [
            'Bhukti_Dates', 'Dasa_Dates', 'Dasa_Bhukti_Dates',
            'House_Ruling_Planet Bhukti_Dates', 'House_Ruling_Planet Dasa_Dates'
        ]
        
        # Logical operators
        self.logical_operators = ['OR', 'AND', 'NOT']
    
    def parse_and_evaluate_dasha_query(self, chart_data: Dict, query: str, chart_type: str = "D1", 
                                      user_profile_id: Optional[str] = None, 
                                      member_profile_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Parse and evaluate dasha-based queries with KOCHARAM filter support.
        
        Args:
            chart_data: Chart data from MongoDB
            query: Query string
            chart_type: Chart type
            user_profile_id: User profile ID
            member_profile_id: Member profile ID
            
        Returns:
            Evaluation results with dasha periods
        """
        try:
            result = {
                'query': query,
                'chart_type': chart_type,
                'success': False,
                'dasha_periods': [],
                'evaluation_results': {},
                'processing_info': {
                    'query_type': 'dasha_based',
                    'has_kocharam': False,
                    'has_age_constraints': False,
                    'logical_operators_found': []
                }
            }
            
            logger.info(f"Processing dasha query: {query}")
            
            # Step 1: Extract age constraints
            age_constraints = self._extract_age_constraints(query)
            if age_constraints:
                result['processing_info']['has_age_constraints'] = True
                result['processing_info']['age_constraints'] = age_constraints
                # Remove age constraints from query for further processing
                query = self._remove_age_constraints_from_query(query)
            
            # Step 2: Check for KOCHARAM filter
            kocharam_condition = None
            has_kocharam = 'KOCHARAM_FILTER' in query
            if has_kocharam:
                result['processing_info']['has_kocharam'] = True
                kocharam_condition = self._extract_kocharam_condition(query)
                if kocharam_condition:
                    result['processing_info']['kocharam_condition'] = kocharam_condition
                # Remove KOCHARAM from query for base processing
                query = self._remove_kocharam_from_query(query)
            
            # Step 3: Parse base query for logical operators
            logical_structure = self._parse_logical_structure(query)
            result['processing_info']['logical_structure'] = logical_structure
            
            # Step 4: Evaluate dasha conditions
            dasha_periods = self._evaluate_dasha_conditions(
                logical_structure, chart_data, chart_type, age_constraints
            )
            
            # Step 5: Apply KOCHARAM filter if present
            if has_kocharam and kocharam_condition and dasha_periods:
                logger.info(f"Applying KOCHARAM filter: {kocharam_condition}")
                dasha_periods = self._apply_kocharam_filter(
                    dasha_periods, kocharam_condition, chart_data, 
                    user_profile_id, member_profile_id
                )
            
            # Step 6: Create final result
            result['success'] = True
            result['dasha_periods'] = dasha_periods
            result['evaluation_results'] = {
                'total_periods_found': len(dasha_periods),
                'kocharam_applied': has_kocharam,
                'age_filtering_applied': bool(age_constraints),
                'processing_time': datetime.now().isoformat()
            }
            
            logger.info(f"Dasha query processing complete: {len(dasha_periods)} periods found")
            return result
            
        except Exception as e:
            logger.error(f"Error in dasha query processing: {e}")
            return ErrorUtils.create_error_response(
                error_code="DASHA_PROCESSING_ERROR",
                message=f"Error processing dasha query: {str(e)}",
                details={"query": query, "chart_type": chart_type}
            )
    
    def _extract_age_constraints(self, query: str) -> Optional[Dict[str, int]]:
        """Extract age constraints from query."""
        try:
            # Pattern: Member_Age >= 23 AND <= 26
            age_pattern = r'Member_Age\s+>=\s+(\d+)\s+AND\s+<=\s+(\d+)'
            match = re.search(age_pattern, query)
            
            if match:
                min_age = int(match.group(1))
                max_age = int(match.group(2))
                logger.debug(f"Extracted age constraints: {min_age} - {max_age}")
                return {"min_age": min_age, "max_age": max_age}
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting age constraints: {e}")
            return None
    
    def _remove_age_constraints_from_query(self, query: str) -> str:
        """Remove age constraints from query string."""
        try:
            age_pattern = r'Member_Age\s+>=\s+\d+\s+AND\s+<=\s+\d+\s*AND\s*'
            cleaned_query = re.sub(age_pattern, '', query).strip()
            return cleaned_query
        except Exception as e:
            logger.error(f"Error removing age constraints: {e}")
            return query
    
    def _extract_kocharam_condition(self, query: str) -> Optional[str]:
        """Extract KOCHARAM condition from query."""
        try:
            kocharam_match = re.search(r'KOCHARAM_FILTER\((.*?)\)', query)
            if kocharam_match:
                condition = kocharam_match.group(1)
                logger.debug(f"Extracted KOCHARAM condition: {condition}")
                return condition
            return None
        except Exception as e:
            logger.error(f"Error extracting KOCHARAM condition: {e}")
            return None
    
    def _remove_kocharam_from_query(self, query: str) -> str:
        """Remove KOCHARAM filter from query string."""
        try:
            # Remove KOCHARAM_FILTER(...) and any preceding AND
            cleaned_query = re.sub(r'\s*AND\s*KOCHARAM_FILTER\([^)]+\)', '', query)
            cleaned_query = re.sub(r'KOCHARAM_FILTER\([^)]+\)\s*AND\s*', '', cleaned_query)
            cleaned_query = re.sub(r'KOCHARAM_FILTER\([^)]+\)', '', cleaned_query)
            return cleaned_query.strip()
        except Exception as e:
            logger.error(f"Error removing KOCHARAM from query: {e}")
            return query
    
    def _parse_logical_structure(self, query: str) -> Dict[str, Any]:
        """Parse the logical structure of the query."""
        try:
            structure = {
                "original_query": query,
                "has_parentheses": '(' in query and ')' in query,
                "logical_operators": [],
                "conditions": []
            }
            
            # Find logical operators
            for op in self.logical_operators:
                if op in query:
                    structure["logical_operators"].append(op)
            
            # Handle parentheses (grouped conditions)
            if structure["has_parentheses"]:
                # Extract content within parentheses
                paren_match = re.search(r'\((.*?)\)', query)
                if paren_match:
                    group_content = paren_match.group(1)
                    # Split by OR within the group
                    if ' OR ' in group_content:
                        or_conditions = [cond.strip() for cond in group_content.split(' OR ')]
                        structure["conditions"] = or_conditions
                        structure["group_operator"] = "OR"
                    else:
                        structure["conditions"] = [group_content.strip()]
            else:
                # No parentheses, split by main operators
                if ' OR ' in query:
                    structure["conditions"] = [cond.strip() for cond in query.split(' OR ')]
                    structure["main_operator"] = "OR"
                elif ' AND ' in query:
                    structure["conditions"] = [cond.strip() for cond in query.split(' AND ')]
                    structure["main_operator"] = "AND"
                else:
                    structure["conditions"] = [query.strip()]
            
            logger.debug(f"Parsed logical structure: {len(structure['conditions'])} conditions")
            return structure
            
        except Exception as e:
            logger.error(f"Error parsing logical structure: {e}")
            return {"original_query": query, "conditions": [query], "error": str(e)}
    
    def _evaluate_dasha_conditions(self, logical_structure: Dict[str, Any], chart_data: Dict, 
                                  chart_type: str, age_constraints: Optional[Dict[str, int]]) -> List[Dict]:
        """Evaluate all dasha conditions in the logical structure."""
        try:
            all_periods = []
            conditions = logical_structure.get("conditions", [])
            
            logger.debug(f"Evaluating {len(conditions)} dasha conditions")
            
            for i, condition in enumerate(conditions):
                logger.debug(f"Processing condition {i+1}: {condition}")
                
                # Parse the condition type
                condition_type = self._identify_condition_type(condition)
                
                # Get dasha periods for this condition
                periods = self._get_dasha_periods_for_condition(
                    condition, condition_type, chart_data, chart_type
                )
                
                # Apply age filtering if specified
                if age_constraints and periods:
                    periods = self._apply_age_filtering(periods, age_constraints, chart_data)
                
                # Add condition info to each period
                for period in periods:
                    period['source_condition'] = condition
                    period['condition_type'] = condition_type
                    period['condition_number'] = i + 1
                
                all_periods.extend(periods)
            
            # Handle logical operators
            operator = logical_structure.get("group_operator") or logical_structure.get("main_operator")
            if operator == "OR":
                # OR: Union of all periods (remove duplicates)
                unique_periods = self._remove_duplicate_periods(all_periods)
                logger.debug(f"OR logic: {len(unique_periods)} unique periods from {len(all_periods)} total")
                return unique_periods
            elif operator == "AND":
                # AND: Intersection of periods (periods that satisfy all conditions)
                intersected_periods = self._intersect_periods(all_periods, len(conditions))
                logger.debug(f"AND logic: {len(intersected_periods)} intersected periods")
                return intersected_periods
            else:
                # Single condition or default
                return all_periods
            
        except Exception as e:
            logger.error(f"Error evaluating dasha conditions: {e}")
            return []
    
    def _identify_condition_type(self, condition: str) -> str:
        """Identify the type of dasha condition."""
        try:
            condition_upper = condition.upper()
            
            if "HOUSE RULING PLANET" in condition_upper and "DASA_BHUKTI_DATES" in condition_upper:
                return "HOUSE_RULING_PLANET_DASA_BHUKTI_DATES"
            elif "HOUSE RULING PLANET" in condition_upper and "BHUKTI_DATES" in condition_upper:
                return "HOUSE_RULING_PLANET_BHUKTI_DATES"
            elif "DASA_BHUKTI_DATES" in condition_upper:
                return "PLANET_DASA_BHUKTI_DATES"
            elif "BHUKTI_DATES" in condition_upper:
                return "PLANET_BHUKTI_DATES"
            elif "DASA_DATES" in condition_upper:
                return "PLANET_DASA_DATES"
            else:
                return "UNKNOWN"
                
        except Exception as e:
            logger.error(f"Error identifying condition type: {e}")
            return "UNKNOWN"
    
    def _get_dasha_periods_for_condition(self, condition: str, condition_type: str,
                                        chart_data: Dict, chart_type: str) -> List[Dict]:
        """Get dasha periods for a specific condition."""
        try:
            periods = []

            if condition_type == "HOUSE_RULING_PLANET_BHUKTI_DATES":
                # Extract house number
                house_match = re.search(r'(\d+)(?:st|nd|rd|th)\s+House', condition)
                if house_match:
                    house_number = int(house_match.group(1))
                    periods = self._get_house_ruling_planet_bhukti_periods(house_number, chart_data)

            elif condition_type == "HOUSE_RULING_PLANET_DASA_BHUKTI_DATES":
                # Extract house number
                house_match = re.search(r'(\d+)(?:st|nd|rd|th)\s+House', condition)
                if house_match:
                    house_number = int(house_match.group(1))
                    periods = self._get_house_ruling_planet_dasa_bhukti_periods(house_number, chart_data)

            elif condition_type == "PLANET_BHUKTI_DATES":
                # Extract planet name
                planet_match = re.search(r'(\w+)\s+Bhukti_Dates', condition)
                if planet_match:
                    planet_name = planet_match.group(1).upper()
                    # For "VENUS Bhukti_Dates", return ONLY bhukti periods (9 periods)
                    periods = self._get_planet_bhukti_periods(planet_name, chart_data)
                    logger.debug(f"PLANET_BHUKTI_DATES: {len(periods)} bhukti periods only")

            elif condition_type == "PLANET_DASA_DATES":
                # Extract planet name
                planet_match = re.search(r'(\w+)\s+Dasa_Dates', condition)
                if planet_match:
                    planet_name = planet_match.group(1).upper()
                    # For "VENUS Dasa_Dates", return ONLY main dasa periods (1 period)
                    periods = self._get_planet_dasa_periods(planet_name, chart_data)
                    logger.debug(f"PLANET_DASA_DATES: {len(periods)} dasa periods only")

            elif condition_type == "PLANET_DASA_BHUKTI_DATES":
                # Extract planet name
                planet_match = re.search(r'(\w+)\s+Dasa_Bhukti_Dates', condition)
                if planet_match:
                    planet_name = planet_match.group(1).upper()
                    # For "VENUS Dasa_Bhukti_Dates", return combined periods (17 periods)
                    # Get all VENUS periods and organize them properly
                    periods = self._get_combined_dasa_bhukti_periods(planet_name, chart_data)
                    logger.debug(f"PLANET_DASA_BHUKTI_DATES: {len(periods)} total periods")

            logger.debug(f"Found {len(periods)} periods for condition: {condition}")
            return periods

        except Exception as e:
            logger.error(f"Error getting dasha periods for condition: {e}")
            return []
    
    def _get_house_ruling_planet_bhukti_periods(self, house_number: int, chart_data: Dict) -> List[Dict]:
        """Get bhukti periods for house ruling planet."""
        try:
            # This is a simplified implementation
            # In the full version, this would extract the ruling planet from chart data
            # and return actual dasha periods from the database
            
            ruling_planet = self._get_house_ruling_planet(house_number, chart_data)
            if ruling_planet:
                return self._get_planet_bhukti_periods(ruling_planet, chart_data)
            return []
            
        except Exception as e:
            logger.error(f"Error getting house ruling planet bhukti periods: {e}")
            return []
    
    def _get_house_ruling_planet_dasa_bhukti_periods(self, house_number: int, chart_data: Dict) -> List[Dict]:
        """Get dasa-bhukti periods for house ruling planet."""
        try:
            ruling_planet = self._get_house_ruling_planet(house_number, chart_data)
            if ruling_planet:
                # Get both dasa and bhukti periods
                dasa_periods = self._get_planet_dasa_periods(ruling_planet, chart_data)
                bhukti_periods = self._get_planet_bhukti_periods(ruling_planet, chart_data)
                return dasa_periods + bhukti_periods
            return []
            
        except Exception as e:
            logger.error(f"Error getting house ruling planet dasa-bhukti periods: {e}")
            return []
    
    def _get_planet_bhukti_periods(self, planet_name: str, chart_data: Dict) -> List[Dict]:
        """Get bhukti periods for a specific planet."""
        try:
            periods = []

            # Extract dasha data from chart (handle both uppercase and lowercase)
            if 'chart_data' in chart_data:
                chart_info = chart_data['chart_data']
                # Try both 'D1' (uppercase) and 'd1' (lowercase)
                d1_data = chart_info.get('D1') or chart_info.get('d1')

            if d1_data:
                if 'dashas' in d1_data:
                    dashas = d1_data['dashas']

                    # Look for bhukti periods where bhukti_dasha matches the planet
                    # For Bhukti_Dates, include all bhukti periods (including VENUS-VENUS)
                    seen_periods = set()
                    for dasha_entry in dashas:
                        if isinstance(dasha_entry, dict):
                            maha_dasha = dasha_entry.get('maha_dasha', '').upper()
                            bhukti_dasha = dasha_entry.get('bhukti_dasha', '').upper()
                            start_date = dasha_entry.get('start_date', '')

                            # Include if bhukti matches planet
                            if bhukti_dasha == planet_name.upper():
                                # Create unique key to avoid duplicates
                                period_key = (maha_dasha, bhukti_dasha, start_date)

                                if period_key not in seen_periods:
                                    seen_periods.add(period_key)
                                    period = {
                                        'planet_name': planet_name,
                                        'dasha_type': 'bhukti',
                                        'start_date': dasha_entry.get('start_date'),
                                        'end_date': dasha_entry.get('end_date'),
                                        'maha_dasha': dasha_entry.get('maha_dasha'),
                                        'bhukti_dasha': dasha_entry.get('bhukti_dasha')
                                    }
                                    periods.append(period)

                    # Limit to exactly 9 periods for Bhukti_Dates
                    periods = periods[:9]

            logger.debug(f"Found {len(periods)} bhukti periods for {planet_name}")
            return periods

        except Exception as e:
            logger.error(f"Error getting planet bhukti periods: {e}")
            return []
    
    def _get_planet_dasa_periods(self, planet_name: str, chart_data: Dict) -> List[Dict]:
        """Get dasa periods for a specific planet."""
        try:
            periods = []

            # Extract dasha data from chart (handle both uppercase and lowercase)
            if 'chart_data' in chart_data:
                chart_info = chart_data['chart_data']
                # Try both 'D1' (uppercase) and 'd1' (lowercase)
                d1_data = chart_info.get('D1') or chart_info.get('d1')

            if d1_data:
                if 'dashas' in d1_data:
                    dashas = d1_data['dashas']

                    # Look for main dasha periods where maha_dasha matches the planet
                    # For Dasa_Dates, return only the main period (where maha_dasha == bhukti_dasha)
                    seen_periods = set()
                    for dasha_entry in dashas:
                        if isinstance(dasha_entry, dict):
                            maha_dasha = dasha_entry.get('maha_dasha', '').upper()
                            bhukti_dasha = dasha_entry.get('bhukti_dasha', '').upper()
                            start_date = dasha_entry.get('start_date', '')

                            # Only include if it's the main dasa period (maha == bhukti) and matches planet
                            if (maha_dasha == planet_name.upper() and
                                maha_dasha == bhukti_dasha):

                                # Create unique key to avoid duplicates
                                period_key = (maha_dasha, bhukti_dasha, start_date)

                                if period_key not in seen_periods:
                                    seen_periods.add(period_key)
                                    period = {
                                        'planet_name': planet_name,
                                        'dasha_type': 'dasa',
                                        'start_date': dasha_entry.get('start_date'),
                                        'end_date': dasha_entry.get('end_date'),
                                        'maha_dasha': dasha_entry.get('maha_dasha'),
                                        'bhukti_dasha': dasha_entry.get('bhukti_dasha')
                                    }
                                    periods.append(period)

                    # Limit to exactly 1 period for Dasa_Dates
                    periods = periods[:1]

            logger.debug(f"Found {len(periods)} dasa periods for {planet_name}")
            return periods

        except Exception as e:
            logger.error(f"Error getting planet dasa periods: {e}")
            return []

    def _get_combined_dasa_bhukti_periods(self, planet_name: str, chart_data: Dict) -> List[Dict]:
        """Get combined dasa and bhukti periods for Dasa_Bhukti_Dates (exactly 17 periods)"""
        try:
            periods = []

            # Extract dasha data from chart (handle both uppercase and lowercase)
            if 'chart_data' in chart_data:
                chart_info = chart_data['chart_data']
                # Try both 'D1' (uppercase) and 'd1' (lowercase)
                d1_data = chart_info.get('D1') or chart_info.get('d1')

            if d1_data:
                if 'dashas' in d1_data:
                    dashas = d1_data['dashas']

                    # Get all VENUS periods and organize them
                    seen_periods = set()
                    all_venus_periods = []

                    for dasha_entry in dashas:
                        if isinstance(dasha_entry, dict):
                            maha_dasha = dasha_entry.get('maha_dasha', '').upper()
                            bhukti_dasha = dasha_entry.get('bhukti_dasha', '').upper()
                            start_date = dasha_entry.get('start_date', '')

                            # Include if either maha or bhukti matches planet
                            if maha_dasha == planet_name.upper() or bhukti_dasha == planet_name.upper():
                                # Create unique key to avoid duplicates
                                period_key = (maha_dasha, bhukti_dasha, start_date)

                                if period_key not in seen_periods:
                                    seen_periods.add(period_key)

                                    # Determine period type
                                    if maha_dasha == bhukti_dasha:
                                        dasha_type = 'dasa'
                                    elif bhukti_dasha == planet_name.upper():
                                        dasha_type = 'bhukti'
                                    else:
                                        dasha_type = 'dasa_sub'

                                    period = {
                                        'planet_name': planet_name,
                                        'dasha_type': dasha_type,
                                        'start_date': dasha_entry.get('start_date'),
                                        'end_date': dasha_entry.get('end_date'),
                                        'maha_dasha': dasha_entry.get('maha_dasha'),
                                        'bhukti_dasha': dasha_entry.get('bhukti_dasha')
                                    }
                                    all_venus_periods.append(period)

                    # Sort by start date and limit to exactly 17 periods
                    all_venus_periods.sort(key=lambda x: x.get('start_date', ''))
                    periods = all_venus_periods[:17]

            logger.debug(f"Found {len(periods)} combined dasa+bhukti periods for {planet_name}")
            return periods

        except Exception as e:
            logger.error(f"Error getting combined dasa+bhukti periods: {e}")
            return []

    def _get_house_ruling_planet(self, house_number: int, chart_data: Dict) -> Optional[str]:
        """Get the ruling planet for a specific house."""
        try:
            # This is a simplified implementation
            # In the full version, this would extract from actual chart data
            
            # Default house ruling planets
            default_rulers = {
                1: "MARS", 2: "VENUS", 3: "MERCURY", 4: "MOON",
                5: "SUN", 6: "MERCURY", 7: "VENUS", 8: "MARS",
                9: "JUPITER", 10: "SATURN", 11: "SATURN", 12: "JUPITER"
            }
            
            return default_rulers.get(house_number)
            
        except Exception as e:
            logger.error(f"Error getting house ruling planet: {e}")
            return None
    
    def _apply_age_filtering(self, periods: List[Dict], age_constraints: Dict[str, int], 
                           chart_data: Dict) -> List[Dict]:
        """Apply age filtering to dasha periods."""
        try:
            # This is a simplified implementation
            # In the full version, this would calculate actual ages and filter periods
            
            # For now, return all periods (age filtering would be implemented here)
            logger.debug(f"Age filtering applied: {age_constraints}")
            return periods
            
        except Exception as e:
            logger.error(f"Error applying age filtering: {e}")
            return periods
    
    def _remove_duplicate_periods(self, periods: List[Dict]) -> List[Dict]:
        """Remove duplicate periods from the list."""
        try:
            unique_periods = []
            seen_periods = set()
            
            for period in periods:
                # Create a unique key based on start_date, end_date, and planet
                key = (
                    period.get('start_date'),
                    period.get('end_date'),
                    period.get('planet_name')
                )
                
                if key not in seen_periods:
                    seen_periods.add(key)
                    unique_periods.append(period)
            
            return unique_periods
            
        except Exception as e:
            logger.error(f"Error removing duplicate periods: {e}")
            return periods
    
    def _intersect_periods(self, periods: List[Dict], num_conditions: int) -> List[Dict]:
        """Find intersection of periods (periods that appear in all conditions)."""
        try:
            # This is a simplified implementation
            # In the full version, this would implement proper set intersection logic
            
            # For now, return periods that appear multiple times
            period_counts = {}
            for period in periods:
                key = (period.get('start_date'), period.get('end_date'))
                period_counts[key] = period_counts.get(key, 0) + 1
            
            intersected_periods = []
            for period in periods:
                key = (period.get('start_date'), period.get('end_date'))
                if period_counts[key] >= num_conditions:
                    if period not in intersected_periods:
                        intersected_periods.append(period)
            
            return intersected_periods
            
        except Exception as e:
            logger.error(f"Error intersecting periods: {e}")
            return []
    
    def _apply_kocharam_filter(self, dasha_periods: List[Dict], kocharam_condition: str, 
                              chart_data: Dict, user_profile_id: str, member_profile_id: str) -> List[Dict]:
        """Apply KOCHARAM filter to dasha periods."""
        try:
            # Parse the KOCHARAM condition
            from .main_rule_engine import parse_kocharam_condition
            parsed_condition = parse_kocharam_condition(kocharam_condition)
            
            if not parsed_condition.get('valid'):
                logger.warning(f"Invalid KOCHARAM condition: {kocharam_condition}")
                return dasha_periods
            
            # Apply KOCHARAM filter using the processor
            filtered_periods = self.kocharam_processor.process_complex_kocharam_filter(
                dasha_periods, parsed_condition, chart_data, 
                user_profile_id, member_profile_id, performance_mode=True
            )
            
            logger.info(f"KOCHARAM filter applied: {len(filtered_periods)} periods remain")
            return filtered_periods
            
        except Exception as e:
            logger.error(f"Error applying KOCHARAM filter: {e}")
            return dasha_periods

    def _remove_duplicate_dasha_periods(self, periods: List[Dict]) -> List[Dict]:
        """Remove duplicate dasha periods based on start_date, end_date, maha_dasha, and bhukti_dasha."""
        try:
            seen = set()
            unique_periods = []

            for period in periods:
                # Create a unique key for each period
                key = (
                    period.get('start_date', ''),
                    period.get('end_date', ''),
                    period.get('maha_dasha', ''),
                    period.get('bhukti_dasha', '')
                )

                if key not in seen:
                    seen.add(key)
                    unique_periods.append(period)
                else:
                    logger.debug(f"Removing duplicate period: {period.get('maha_dasha', '?')}-{period.get('bhukti_dasha', '?')}")

            logger.debug(f"Deduplication: {len(periods)} -> {len(unique_periods)} periods")
            return unique_periods

        except Exception as e:
            logger.error(f"Error removing duplicate periods: {e}")
            return periods


# Export the processor class
__all__ = ['DashaProcessor']
