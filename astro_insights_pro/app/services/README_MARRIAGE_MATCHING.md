# Marriage Matching Service

This service provides marriage compatibility analysis based on Vedic astrology principles.

## Overview

The marriage matching service analyzes the compatibility between a bride and groom based on their birth stars (nakshatras) and zodiac signs (rasis). It calculates various compatibility factors (poruthams) and provides an overall compatibility score.

## Compatibility Factors

The service calculates the following compatibility factors:

1. **<PERSON><PERSON>** (Day compatibility)
   - Analyzes compatibility based on the distance between birth stars
   - Affects health and well-being of the couple

2. **<PERSON><PERSON>** (Temperament compatibility)
   - Checks compatibility based on temperament groups (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Rakshasa)
   - Affects mental compatibility and harmony in the relationship

3. **<PERSON><PERSON><PERSON>** (Progeny compatibility)
   - Evaluates compatibility for progeny and children's well-being
   - Affects the couple's ability to have healthy children

4. **<PERSON><PERSON>** (Longevity compatibility)
   - Assesses compatibility for longevity and financial prosperity
   - Affects the longevity of the wife and financial stability

5. **<PERSON><PERSON>** (Zodiac compatibility)
   - Checks zodiac sign compatibility
   - Affects physical compatibility and intimacy

6. **<PERSON><PERSON>** (Physical compatibility)
   - Checks physical and sexual compatibility
   - Affects attraction and mutual respect

7. **<PERSON><PERSON><PERSON>** (Obstruction compatibility)
   - Identifies problematic star combinations
   - Affects protection from negative influences or doshas

## Scoring System

Each compatibility factor is rated as:
- "Excellent Match" (10 points)
- "Good Match" (8 points)
- "Partial Match" (5 points)
- "Poor Match" (2 points)
- "No Match" (0 points)

The overall compatibility is calculated as a percentage of the total possible score:
- 80-100%: Excellent Match
- 60-79%: Good Match
- 40-59%: Partial Match
- 20-39%: Poor Match
- 0-19%: No Match

## Usage

### API Endpoints

#### Analyze Marriage Compatibility
- **Method**: POST
- **URL**: `/api/marriage-matching`
- **Authentication**: Required
- **Request Body**:
  ```json
  {
      "bride_id": 36,
      "groom_id": 6
  }
  ```
- **Response**:
  ```json
  {
      "success": true,
      "bride": {
          "id": 36,
          "name": "Jane Doe",
          "nakshatra": "ROHINI",
          "rasi": "MESHAM"
      },
      "groom": {
          "id": 6,
          "name": "Bob Smith",
          "nakshatra": "ROHINI",
          "rasi": "MESHAM"
      },
      "compatibility_factors": {
          "1.1": {
              "name": "Dina Porutham",
              "description": "Health and well-being",
              "result": "No Match",
              "score": 0
          },
          // Other factors...
      },
      "total_score": 35,
      "max_possible_score": 70,
      "percentage": 50.0,
      "overall_compatibility": "Partial Match",
      "generated_at": "2023-05-08T15:18:05.321496"
  }
  ```

#### Get Marriage Compatibility
- **Method**: GET
- **URL**: `/api/marriage-matching/{bride_id}/{groom_id}`
- **Authentication**: Required
- **Response**: Same as above

### Direct Service Usage

You can also use the service directly in your code:

```python
from fortune_lens.app.services.marriage_matching_service import MarriageMatchingService

# Analyze compatibility
result = MarriageMatchingService.analyze_marriage_compatibility(bride_id, groom_id)
```

### Testing

You can test the service using the provided test script:

```bash
python fortune_lens/tests/test_marriage_matching.py --bride 36 --groom 6
```

## Implementation Details

The service extracts nakshatra and rasi information from the member profiles' astrological data. It then applies various Vedic astrology rules to calculate the compatibility factors.

The implementation follows traditional Vedic astrology principles for marriage compatibility analysis, including:
- Star distance calculations
- Gana (temperament) classifications
- Rasi (zodiac) relationships
- Yoni (animal) compatibility
- Vedhai (obstruction) combinations

## References

- Traditional Vedic astrology texts on marriage compatibility
- South Indian marriage matching principles
- Tamil astrological traditions
