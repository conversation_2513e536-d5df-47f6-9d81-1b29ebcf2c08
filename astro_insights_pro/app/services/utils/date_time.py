"""
Date and Time Utility Functions

This module provides functions for working with dates, times, and
Julian day calculations used in astrological calculations.
"""

import datetime
import swisseph as swe
import numpy as np
from dateutil import relativedelta

from .. import const
from ..panchanga import drik as drig_panchanga


def julian_day_number(date_of_birth_as_tuple, time_of_birth_as_tuple):
    """
    Return Julian day number for given Date of birth and time of birth.

    Args:
        date_of_birth_as_tuple (tuple): Date of birth as tuple (year, month, day)
            For BC dates, use negative year (e.g., (-3114, 1, 1) for 1-Jan of 3114 BC)
            Note: There is no 0 BC or 0 AD, so avoid zero year
        time_of_birth_as_tuple (tuple): Time of birth as tuple (hour, minute, second)

    Returns:
        float: Julian day number
    """
    tob_in_hours = time_of_birth_as_tuple[0] + time_of_birth_as_tuple[1] / 60.0 + time_of_birth_as_tuple[2] / 3600.0
    jd = swe.julday(date_of_birth_as_tuple[0], date_of_birth_as_tuple[1], date_of_birth_as_tuple[2], tob_in_hours)
    return jd


def jd_to_gregorian(jd):
    """
    Convert Julian day to Gregorian date.

    Args:
        jd (float): Julian day number

    Returns:
        tuple: (year, month, day, hour_fraction)
    """
    return swe.revjul(jd, swe.GREG_CAL)  # returns (y, m, d, hour_fraction)


def gregorian_to_jd(date):
    """
    Convert Gregorian date to Julian day number at 00:00 UTC.

    Args:
        date: Date object with year, month, day attributes

    Returns:
        float: Julian day number
    """
    return swe.julday(date.year, date.month, date.day, 0.0)


def jd_to_gregorian_string(jd):
    """
    Convert Julian day to Gregorian date string.

    Args:
        jd (float): Julian day number

    Returns:
        str: Gregorian date string in format "YYYY-MM-DD HH:MM:SS"
    """
    gregorian_date = datetime.datetime(4713, 1, 1, 12) + datetime.timedelta(days=jd - 2440587.5)
    return gregorian_date.strftime("%Y-%m-%d %H:%M:%S")


def jd_to_local(jd, place):
    """
    Convert Julian day to local date and time.

    Args:
        jd (float): Julian day number
        place: Place object with timezone attribute

    Returns:
        tuple: (year, month, day, hour_fraction)
    """
    y, m, d, _ = jd_to_gregorian(jd)
    jd_utc = gregorian_to_jd(drig_panchanga.Date(y, m, d))
    fhl = (jd - jd_utc) * 24 + place.timezone
    return y, m, d, fhl


def julian_day_utc(julian_day, place):
    """
    Convert Julian day to UTC.

    Args:
        julian_day (float): Julian day number
        place: Place object with timezone attribute

    Returns:
        float: Julian day number in UTC
    """
    return julian_day - (place.timezone / 24.)


def local_time_to_jdut1(year, month, day, hour=0, minutes=0, seconds=0, timezone=0.0):
    """
    Convert local time to JD(UT1).

    Args:
        year (int): Year
        month (int): Month
        day (int): Day
        hour (int): Hour
        minutes (int): Minutes
        seconds (int): Seconds
        timezone (float): Timezone offset in hours

    Returns:
        float: Julian day number (UT1)
    """
    y, m, d, h, mnt, s = swe.utc_time_zone(year, month, day, hour, minutes, seconds, timezone)
    # BUG in pyswisseph: replace 0 by s
    jd_et, jd_ut1 = swe.utc_to_jd(y, m, d, h, mnt, s, flag=swe.GREG_CAL)
    return jd_ut1


def from_dms(degs, mins, secs):
    """
    Convert degrees, minutes, seconds to decimal degrees.

    Args:
        degs (int): Degrees
        mins (int): Minutes
        secs (int): Seconds

    Returns:
        float: Decimal degrees
    """
    return degs + mins / 60 + secs / 3600


def to_dms_prec(deg):
    """
    Convert decimal degrees to degrees, minutes, seconds with precision.

    Args:
        deg (float): Decimal degrees

    Returns:
        list: [degrees, minutes, seconds]
    """
    d = int(deg)
    mins = (deg - d) * 60
    m = int(mins)
    s = round((mins - m) * 60, 2)
    return [d, m, s]


def to_dms(deg, as_string=True, is_lat_long=None, round_seconds_to_digits=None, round_to_minutes=None):
    """
    Convert decimal degrees to degrees, minutes, seconds.

    Args:
        deg (float): Decimal degrees
        as_string (bool): If True, return as string with symbols; if False, return as tuple
        is_lat_long (str): 'plong' for planet longitude, 'lat' for latitude, 'long' for longitude
        round_seconds_to_digits (int): Number of digits to round seconds to
        round_to_minutes (bool): If True, round to minutes

    Returns:
        str or list: Degrees, minutes, seconds as string or list
    """
    sep = ':'
    am = " AM"
    pm = " PM"
    ampm = am
    degree_symbol = const._degree_symbol
    minute_symbol = const._minute_symbol
    second_symbol = const._second_symbol
    next_day = ''

    d = int(deg)
    mins = (deg - d) * 60

    if round_to_minutes:
        m = int(round(mins, 0))
    else:
        m = int(mins)

    ss = (mins - m) * 60
    s = round(ss, round_seconds_to_digits) if round_seconds_to_digits else int(ss)

    if is_lat_long is None:
        if d > 23:
            q = d // 24
            d = d % 24
            next_day = f' (+{q})'
        elif d < 0:
            d = abs(d) % 24
            m = abs(m)
            s = abs(s)
            next_day = ' (-1)'

    if d >= 12:
        ampm = pm

    if s == 60:
        m += 1
        s = 0

    if m == 60:
        d += 1
        m = 0

    if round_to_minutes:
        answer = [d, m]
    else:
        answer = [d, m, s]

    if as_string or is_lat_long is not None:
        if is_lat_long == 'plong':
            answer = f"{d}{degree_symbol} {abs(m)}{minute_symbol}"
            if not round_to_minutes:
                answer += f" {abs(s)}{second_symbol}"
        elif is_lat_long == 'lat':
            answer = f"{d}{degree_symbol} {abs(m)}{minute_symbol}"
            if not round_to_minutes:
                answer += f" {abs(s)}{second_symbol}"
            answer += ' N' if d > 0 else ' S'
        elif is_lat_long == 'long':
            answer = f"{d}{degree_symbol} {abs(m)}{minute_symbol}"
            if not round_to_minutes:
                answer += f" {abs(s)}{second_symbol}"
            answer += ' E' if d > 0 else ' W'
        else:  # as_string = True
            answer = f"{str(d).zfill(2)}{sep}{str(m).zfill(2)}"
            if not round_to_minutes:
                answer += f"{sep}{str(s).zfill(2)}"
            answer += f"{ampm}{next_day}"

    return answer


def from_dms_str_to_dms(dms_str):
    """
    Convert DMS string to degrees, minutes, seconds.

    Args:
        dms_str (str): DMS string (e.g., "12:30:45 AM")

    Returns:
        tuple: (degrees, minutes, seconds)
    """
    if '+1' in dms_str:
        dmsh = 24
    elif '-1' in dms_str:
        dmsh = -24
    else:
        dmsh = 0

    dms = dms_str.replace('(+1)', '').replace('(-1)', '').replace(' AM', '').replace(' PM', '').split(':')
    return dmsh + int(dms[0]), int(dms[1]), int(dms[2])


def from_dms_str_to_degrees(dms_str):
    """
    Convert DMS string to decimal degrees.

    Args:
        dms_str (str): DMS string (e.g., "12:30:45 AM")

    Returns:
        float: Decimal degrees
    """
    dms = from_dms_str_to_dms(dms_str)
    return dms[0] + dms[1] / 60. + dms[2] / 3600.


def date_diff_in_years_months_days(start_date_str, end_date_str, date_format_str='%Y-%m-%d'):
    """
    Calculate difference between two dates in years, months, and days.

    Args:
        start_date_str (str): Start date string
        end_date_str (str): End date string
        date_format_str (str): Date format string

    Returns:
        tuple: (years, months, days)
    """
    start_date = datetime.datetime.strptime(start_date_str, date_format_str)
    end_date = datetime.datetime.strptime(end_date_str, date_format_str)
    delta = relativedelta.relativedelta(end_date, start_date)
    return delta.years, delta.months, delta.days


def get_dob_years_months_60hrs_from_today(dob, tob):
    """
    Calculate years, months, and 60-hour periods from date of birth to today.

    Args:
        dob (tuple): Date of birth as tuple (year, month, day)
        tob (tuple): Time of birth as tuple (hour, minute, second)

    Returns:
        tuple: (years, months, 60-hour periods)
    """
    jd_dob = julian_day_number(dob, tob)
    current_date_str, _ = datetime.datetime.now().strftime('%Y,%m,%d;%H:%M:%S').split(';')
    yt, mt, dt = map(int, current_date_str.split(','))
    jd_now = julian_day_number((yt, mt, dt), tob)

    if jd_now > jd_dob:
        years = int((jd_now - jd_dob) / const.sidereal_year)
        jdm = (jd_now - jd_dob) % const.sidereal_year
        months = int(jdm / 30)
        jdh = jdm % 30
        _60hrs = int(jdh / 2.5)
        return years + 1, months + 1, _60hrs
    else:
        return 1, 1, 1


def panchanga_date_diff(panchanga_date1, panchanga_date2):
    """
    Calculate difference between two panchanga dates in years, months, and days.

    Args:
        panchanga_date1: First panchanga date
        panchanga_date2: Second panchanga date

    Returns:
        tuple: (years, months, days)
    """
    npdate1 = np.datetime64(panchanga_date1)
    npdate2 = np.datetime64(panchanga_date2)
    days_diff = (npdate2 - npdate1) / np.timedelta64(1, "D")
    years_diff, days_diff = divmod(days_diff, const.sidereal_year)
    months_diff, days_diff = divmod(days_diff, (const.sidereal_year / 12))
    days_diff = round(days_diff, 0)
    return int(years_diff), int(months_diff), int(days_diff)


def next_panchanga_day(panchanga_date, add_days=1):
    """
    Get the next panchanga date.

    Args:
        panchanga_date: Panchanga date
        add_days (int): Number of days to add

    Returns:
        Date: Next panchanga date
    """
    np_date = np.datetime64(panchanga_date)
    add_days_int = int(add_days)
    next_date = np_date + np.timedelta64(add_days_int, "D")
    p_date_str = np.datetime_as_string(next_date).split('-')

    if len(p_date_str) == 4:
        p_date = drig_panchanga.Date(-int(p_date_str[1]), int(p_date_str[2]), int(p_date_str[3]))
    else:
        p_date = drig_panchanga.Date(int(p_date_str[0]), int(p_date_str[1]), int(p_date_str[2]))

    return p_date


def previous_panchanga_day(panchanga_date, minus_days=1):
    """
    Get the previous panchanga date.

    Args:
        panchanga_date: Panchanga date
        minus_days (int): Number of days to subtract

    Returns:
        Date: Previous panchanga date
    """
    np_date = np.datetime64(panchanga_date)
    prev_date = np_date - np.timedelta64(minus_days, "D")
    p_date_str = np.datetime_as_string(prev_date).split('-')

    if len(p_date_str) == 4:
        p_date = drig_panchanga.Date(-int(p_date_str[1]), int(p_date_str[2]), int(p_date_str[3]))
    else:
        p_date = drig_panchanga.Date(int(p_date_str[0]), int(p_date_str[1]), int(p_date_str[2]))

    return p_date


def get_timezone_offset(latitude, longitude, date=None):
    """
    Get timezone offset for a location based on latitude and longitude.

    Args:
        latitude (float): Latitude in decimal degrees
        longitude (float): Longitude in decimal degrees
        date (datetime.date, optional): Date for which to get the timezone offset

    Returns:
        float: Timezone offset in hours
    """
    import pytz
    from timezonefinder import TimezoneFinder
    from datetime import datetime

    # Initialize TimezoneFinder
    tf = TimezoneFinder()

    # Get timezone name for the location
    timezone_str = tf.timezone_at(lat=latitude, lng=longitude)

    if not timezone_str:
        # If timezone not found, estimate based on longitude
        return round(longitude / 15.0, 1)

    # Get timezone object
    timezone = pytz.timezone(timezone_str)

    # Use current date if not provided
    if date is None:
        date = datetime.now()
    elif isinstance(date, datetime.date) and not isinstance(date, datetime.datetime):
        date = datetime.combine(date, datetime.min.time())

    # Get timezone offset
    offset = timezone.utcoffset(date)

    # Convert to hours
    offset_hours = offset.total_seconds() / 3600.0

    return offset_hours
