"""
Location Utility Functions

This module provides functions for working with geographic locations,
including geocoding, timezone calculations, and elevation data.
"""

import json
import warnings
import requests
import geocoder
import pandas as pd
import numpy as np
from pytz import timezone, utc
from timezonefinder import TimezoneFinder
from geopy.geocoders import Nominatim
import datetime

from .date_time import get_timezone_offset
from .. import const

# Global variables
_world_city_db_df = []
world_cities_db = []
world_cities_list = []
google_maps_url = "https://www.google.cl/maps/place/"


def get_place_from_user_ip_address():
    """
    Get place information from user's IP address.

    Returns:
        tuple: (place, latitude, longitude, time_zone_offset) or empty list if not found
    """
    try:
        print("Trying to get location using IP Address of the user")
        g = geocoder.ip('me')
        if g is None or g == '':
            print('Trying using ipinfo website')
            try:
                place, latitude, longitude, time_zone_offset = _get_place_from_ipinfo()
                return place, latitude, longitude, time_zone_offset
            except:
                print('No latitude/longitude provided. Could not guess location from IP Address')
                return []
        else:
            place, country, [latitude, longitude] = g.city, g.country, g.latlng
            place += ',' + country
            time_zone_offset = get_place_timezone_offset(latitude, longitude)
            print('Location obtained from IP Address:', place, [latitude, longitude, time_zone_offset])
            return place, latitude, longitude, time_zone_offset
    except:
        print('No latitude/longitude provided. Could not guess location from IP Address')
        return []


def _get_place_from_ipinfo():
    """
    Get place information from ipinfo.io service.

    Returns:
        tuple: (place, latitude, longitude, time_zone_offset)
    """
    url = 'http://ipinfo.io/json'
    response = requests.get(url)
    data = json.loads(response.text)
    print('Data received from ipinfo', data)
    place = data['city'] + ',' + data['country']
    latitude, longitude = data['loc'].split(',')
    time_zone_offset = get_place_timezone_offset(float(latitude), float(longitude))
    return place, float(latitude), float(longitude), time_zone_offset


def get_elevation(lat=None, long=None):
    """
    Get elevation/altitude in meters from latitude and longitude.

    Uses the open-elevation.com API to retrieve elevation data.

    Args:
        lat (float): Latitude
        long (float): Longitude

    Returns:
        float: Elevation in meters, or 0.0 if not found
    """
    if lat is None or long is None:
        return 0.0

    query = const._open_elevation_api_url(lat, long)

    # Request with a timeout for slow responses
    try:
        r = requests.get(query, timeout=20)

        # Only get the json response in case of 200 or 201
        if r.status_code == 200 or r.status_code == 201:
            elevation = pd.json_normalize(r.json(), 'results')['elevation'].values[0]
        else:
            elevation = 0.0
    except:
        elevation = 0.0

    return elevation


def get_location(place_name=None):
    """
    Get place's latitude, longitude and timezone.

    This function tries multiple methods to find location information:
    1. If place_name is None, try to get the place from the user's IP address.
    2. Check if lat/long in world cities database file in data folder
    3. Try Google Maps using _scrap_google_map_for_latlongtz_from_city_with_country()
    4. Try OpenStreetMaps using get_location_using_nominatim()

    Args:
        place_name (str): Place name (e.g., 'Shillong, India', 'Hoffman Estates,IL,US')

    Returns:
        list: [place_name, latitude, longitude, time_zone] or empty list if not found
    """
    result = None
    place_found = False

    if place_name is None or place_name.strip() == '':
        result = get_place_from_user_ip_address()
        if result:
            return result

    # First check if lat/long in world cities db
    place_index = -1
    place_name_1 = place_name.split(',')[0]
    place_index = [row for row, city in enumerate(world_cities_list) if place_name.lower() == city.lower()]

    if len(place_index) > 0:
        place_found = True
        print(place_name, 'in the database')
        place_index = int(place_index[0])
        city = world_cities_db[place_index, 1]
        _latitude = round(float(world_cities_db[place_index, 2]), 4)
        _longitude = round(float(world_cities_db[place_index, 3]), 4)
        _time_zone = round(float(world_cities_db[place_index, 5]), 2)
        result = [city, _latitude, _longitude, _time_zone]
    else:
        print(place_name, 'not in database. Trying to get from Google')
        result = _scrap_google_map_for_latlongtz_from_city_with_country(place_name)
        if result is not None and len(result) == 3:
            place_found = True
            print(place_name, ' found from Google Maps')
            _place_name = place_name
            _latitude = round(result[0], 4)
            _longitude = round(result[1], 4)
            _time_zone = round(result[2], 2)
            result = [place_name, _latitude, _longitude, _time_zone]

            # Save to database if not already there
            if ',' in place_name:
                _city, _country = place_name.split(',')
                _tz_str = ''
                if _city not in world_cities_list:
                    save_location_to_database([_country, _city, _latitude, _longitude, _tz_str, _time_zone])
        else:
            print('Could not get', place_name, 'from Google. Trying OpenStreetMaps')
            place_found = False
            result = get_location_using_nominatim(place_name)
            if result:
                place_found = True
                print(place_name, 'found in OpenStreetMap')
                [_place_name, _latitude, _longitude, _time_zone] = result

    if place_found:
        return result

    return []


def _scrap_google_map_for_latlongtz_from_city_with_country(city_with_country):
    """
    Scrape Google Maps to get latitude/longitude of a city/country.

    Args:
        city_with_country (str): City name and country (e.g., "Chennai, India")

    Returns:
        tuple: (latitude, longitude, timezone_offset) or empty list if not found
    """
    url = "https://www.google.cl/maps/place/" + city_with_country
    try:
        resp = requests.request(method="GET", url=url)
        r = requests.get(url)
        txt = r.text

        find1 = "window.APP_INITIALIZATION_STATE="
        find2 = ";window.APP"

        i1 = txt.find(find1)
        i2 = txt.find(find2, i1 + 1)
        js = txt[i1 + len(find1):i2]
        data = json.loads(js)[0][0][1:3]
        latitude = data[1]
        longitude = data[0]
        timezone_offset = get_place_timezone_offset(latitude, longitude)
        print('city', city_with_country, 'lat=', latitude, 'long=', longitude, 'timezone offset', timezone_offset)
        return latitude, longitude, timezone_offset
    except Exception as e:
        print(e)
        warnings.warn("Unable to get location from Google Map Scrape. Aborted")
        return []


def get_location_using_nominatim(place_with_country_code):
    """
    Get latitude/longitude from city with country code using Nominatim.

    Args:
        place_with_country_code (str): City name and country code (e.g., "Chennai, IN")

    Returns:
        list: [city, latitude, longitude, time_zone_offset] or None if not found
    """
    geolocator = Nominatim(user_agent="FortuneLens")
    try:
        address, (latitude, longitude) = geolocator.geocode("city:" + place_with_country_code, featuretype='city')
        if address:
            city = address.split(',')[0]
            time_zone_offset = get_place_timezone_offset(latitude, longitude)
            return [city, latitude, longitude, time_zone_offset]
    except:
        pass
    return None


def get_place_timezone_offset(latitude, longitude):
    """
    Get a location's time zone offset from UTC in hours using latitude/longitude.

    Args:
        latitude (float): Latitude of the place
        longitude (float): Longitude of the place

    Returns:
        float: Time zone offset in hours
    """
    try:
        tf = TimezoneFinder()
        today = datetime.datetime.now()
        tz_target = timezone(tf.timezone_at(lng=longitude, lat=latitude))
        # Handle error case if tz_target is None
        if tz_target is None:
            return 0.0

        today_target = tz_target.localize(today)
        today_utc = utc.localize(today)
        tz_offset = (today_utc - today_target).total_seconds() / 3600.0  # in hours
        return tz_offset
    except Exception as err:
        print('Error in get_place_timezone_offset', err)
        print('WARNING: Time Zone returned as default +0.0')
        return 0.0


def save_location_to_database(location_data):
    """
    Save location data to the world cities database.

    Args:
        location_data (list): Location data to save
    """
    global _world_city_db_df, world_cities_db, world_cities_list
    print('Writing', location_data, 'to database')
    try:
        _world_city_db_df.loc[len(_world_city_db_df.index)] = location_data
        _world_city_db_df.to_csv(const._world_city_csv_file, mode='w', header=None, index=False)
    except Exception as e:
        print(f"Error saving location to database: {e}")
