"""
Profile Service
"""

from datetime import datetime
from bson import ObjectId

from ..extensions import mongo


class ProfileService:
    """Profile Service"""
    
    @staticmethod
    def create_profile(user_id, data):
        """
        Create a new profile
        
        Args:
            user_id (str): User ID
            data (dict): Profile data
            
        Returns:
            dict: Created profile document
        """
        # Create profile document
        profile = {
            'user_id': ObjectId(user_id),
            'birth_date': data['birth_date'],
            'birth_time': data['birth_time'],
            'birth_place': data['birth_place'],
            'latitude': data.get('latitude'),
            'longitude': data.get('longitude'),
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        
        # Insert profile into database
        result = mongo.db.profiles.insert_one(profile)
        profile['_id'] = result.inserted_id
        
        # Convert ObjectId to string for response
        profile['_id'] = str(profile['_id'])
        profile['user_id'] = str(profile['user_id'])
        
        return profile
    
    @staticmethod
    def get_profile_by_id(profile_id):
        """
        Get profile by ID
        
        Args:
            profile_id (str): Profile ID
            
        Returns:
            dict: Profile document if found, None otherwise
        """
        try:
            profile = mongo.db.profiles.find_one({'_id': ObjectId(profile_id)})
            
            if profile:
                # Convert ObjectId to string
                profile['_id'] = str(profile['_id'])
                profile['user_id'] = str(profile['user_id'])
            
            return profile
        except:
            return None
    
    @staticmethod
    def get_profiles_by_user_id(user_id):
        """
        Get profiles by user ID
        
        Args:
            user_id (str): User ID
            
        Returns:
            list: List of profile documents
        """
        try:
            profiles = list(mongo.db.profiles.find({'user_id': ObjectId(user_id)}))
            
            # Convert ObjectId to string
            for profile in profiles:
                profile['_id'] = str(profile['_id'])
                profile['user_id'] = str(profile['user_id'])
            
            return profiles
        except:
            return []
    
    @staticmethod
    def update_profile(profile_id, data):
        """
        Update profile
        
        Args:
            profile_id (str): Profile ID
            data (dict): Profile data to update
            
        Returns:
            dict: Updated profile document
        """
        # Prepare update data
        update_data = {
            'updated_at': datetime.utcnow()
        }
        
        # Add fields to update
        for field in ['birth_date', 'birth_time', 'birth_place', 'latitude', 'longitude']:
            if field in data:
                update_data[field] = data[field]
        
        # Update profile
        mongo.db.profiles.update_one(
            {'_id': ObjectId(profile_id)},
            {'$set': update_data}
        )
        
        # Get updated profile
        profile = mongo.db.profiles.find_one({'_id': ObjectId(profile_id)})
        
        # Convert ObjectId to string
        profile['_id'] = str(profile['_id'])
        profile['user_id'] = str(profile['user_id'])
        
        return profile
    
    @staticmethod
    def delete_profile(profile_id):
        """
        Delete profile
        
        Args:
            profile_id (str): Profile ID
            
        Returns:
            bool: True if profile deleted, False otherwise
        """
        result = mongo.db.profiles.delete_one({'_id': ObjectId(profile_id)})
        return result.deleted_count > 0
