"""
Rasi-based Marriage Matching

This module provides functions for analyzing marriage compatibility
based on <PERSON><PERSON> (Moon Sign) positions in Vedic astrology.
"""

from datetime import datetime
from bson import ObjectId
import pandas as pd
import random
import string
import numpy as np
import json

from ...extensions import mongo
from ...config import BaseConfiguration
from ...constants import Collection, Field
from .utils import get_member_astro_data, extract_planet_positions, extract_house_names, extract_nakshatra_data
from .reference_data import get_planets_friends_neutral_enemies, get_planets_exalt_debilitate, get_house_name_data


class NumpyEncoder(json.JSONEncoder):
    """
    Custom JSON encoder for NumPy types.
    """
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)


def get_member_rasi_data(member_id):
    """
    Get Rasi (Moon Sign) data for a member.

    Args:
        member_id (str or int): Member ID

    Returns:
        dict: Dictionary containing Rasi data
    """
    # Convert to ObjectId if string and valid
    if isinstance(member_id, str) and ObjectId.is_valid(member_id):
        member_id_obj = ObjectId(member_id)
        member_profile = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one({"_id": member_id_obj})
    # Try as member_profile_id if not ObjectId
    elif isinstance(member_id, (str, int)):
        member_id_int = int(member_id) if isinstance(member_id, str) and member_id.isdigit() else member_id
        member_profile = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one({"member_profile_id": member_id_int})
    else:
        return None

    if not member_profile:
        return None

    # Get user_profile_id and member_profile_id
    user_profile_id = member_profile.get("user_profile_id")
    member_profile_id = member_profile.get("member_profile_id")

    # Get astro profile data
    astro_profile = mongo.db[BaseConfiguration.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].find_one({
        "user_profile_id": user_profile_id,
        "member_profile_id": member_profile_id
    })

    if not astro_profile:
        return None

    # Extract Rasi data
    rasi_data = {}

    # Get Moon's position (Rasi)
    if 'chart_data' in astro_profile and 'D1' in astro_profile['chart_data']:
        d1_data = astro_profile['chart_data']['D1']

        # Find Moon's house
        moon_house = None
        moon_nakshatra = None
        moon_pada = None

        if 'houses' in d1_data:
            for house in d1_data['houses']:
                if 'planets' in house and 'moon' in house['planets']:
                    moon_house = house
                    if 'planet_nakshatras' in house and 'moon' in house['planet_nakshatras']:
                        moon_nakshatra = house['planet_nakshatras']['moon']
                    if 'planet_padas' in house and 'moon' in house['planet_padas']:
                        moon_pada = house['planet_padas']['moon']
                    break

        if moon_house:
            rasi_data['moon_rasi'] = moon_house.get('house_name', '')
            rasi_data['moon_nakshatra'] = moon_nakshatra
            rasi_data['moon_pada'] = moon_pada

    # Add member details
    rasi_data['name'] = member_profile.get('name', '')
    rasi_data['gender'] = member_profile.get('gender', '')
    rasi_data['birth_date'] = member_profile.get('birth_date', '')

    return rasi_data


def dina_porutham(bride_nakshatra, groom_nakshatra):
    """
    Calculate Dina Porutham (Day compatibility)

    Args:
        bride_nakshatra (str): Bride's nakshatra
        groom_nakshatra (str): Groom's nakshatra

    Returns:
        dict: Dictionary containing result and points
    """
    # List of nakshatras in order
    nakshatras = [
        "ASHWINI", "BHARANI", "KRITTIKA", "ROHINI", "MRIGASIRA", "ARUDRA",
        "PUNARVASU", "PUSHYAMI", "ASLESHA", "MAGHA", "POORVAPHALGUNI", "UTTARAPHALGUNI",
        "HASTA", "CHITRA", "SWATI", "VISHAKHA", "ANURADHA", "JYESHTHA",
        "MOOLA", "POORVASHADHA", "UTTARASHADHA", "SHRAVANA", "DHANISHTA", "SHATABHISHA",
        "POORVABHADRA", "UTTARABHADRA", "REVATI"
    ]

    # Tamil nakshatra names mapping
    tamil_nakshatra_mapping = {
        "ASHWINI": "ASHWINI",
        "BHARANI": "BHARANI",
        "KRITTIKA": "KARTHIGAI",
        "ROHINI": "ROHINI",
        "MRIGASIRA": "MRIGASHEERSHAM",
        "ARUDRA": "THIRUVADHIRAI",
        "PUNARVASU": "PUNARPOOSAM",
        "PUSHYAMI": "POOSAM",
        "ASLESHA": "AYILYAM",
        "MAGHA": "MAGAM",
        "POORVAPHALGUNI": "POORAM",
        "UTTARAPHALGUNI": "UTHIRAM",
        "HASTA": "HASTHAM",
        "CHITRA": "CHITHIRAI",
        "SWATI": "SWATHI",
        "VISHAKHA": "VISAKAM",
        "ANURADHA": "ANUSHAM",
        "JYESHTHA": "KETTAI",
        "MOOLA": "MOOLAM",
        "POORVASHADHA": "POORADAM",
        "UTTARASHADHA": "UTHIRADAM",
        "SHRAVANA": "THIRUVONAM",
        "DHANISHTA": "AVITTAM",
        "SHATABHISHA": "SADAYAM",
        "POORVABHADRA": "POORATTADHI",
        "UTTARABHADRA": "UTHIRATTADHI",
        "REVATI": "REVATHI"
    }

    try:
        # Convert inputs to uppercase for matching
        bride_nakshatra = bride_nakshatra.upper() if bride_nakshatra else ""
        groom_nakshatra = groom_nakshatra.upper() if groom_nakshatra else ""

        # Create reverse mapping from Tamil to Sanskrit
        reverse_mapping = {}
        for sanskrit, tamil in tamil_nakshatra_mapping.items():
            reverse_mapping[tamil.upper()] = sanskrit

        # Convert Tamil nakshatra names to Sanskrit if needed
        if bride_nakshatra in reverse_mapping:
            bride_nakshatra = reverse_mapping[bride_nakshatra]
        if groom_nakshatra in reverse_mapping:
            groom_nakshatra = reverse_mapping[groom_nakshatra]

        # Get indices of Nakshatras
        bride_index = nakshatras.index(bride_nakshatra) + 1
        groom_index = nakshatras.index(groom_nakshatra) + 1

        # Calculate the distance
        distance = ((groom_index - bride_index) % 27) + 1

        if distance == 0:
            distance = 27  # To handle cyclic case

        # Check Dina Porutham rules
        if distance % 9 == 0:
            return {"result": "No Match", "points": 0}
        elif distance % 2 == 0:
            return {"result": "Excellent Match", "points": 3}
        else:
            return {"result": "No Match", "points": 0}
    except Exception as e:
        print(f"Error calculating Dina Porutham: {str(e)}")
        return {"result": "Error", "points": 0}


def gana_porutham(bride_nakshatra, groom_nakshatra):
    """
    Calculate Gana Porutham (Temperament compatibility)

    Args:
        bride_nakshatra (str): Bride's nakshatra
        groom_nakshatra (str): Groom's nakshatra

    Returns:
        dict: Dictionary containing result and points
    """
    # Define Gana (temperament) for each nakshatra
    nakshatra_gana = {
        "ASHWINI": "Deva",
        "BHARANI": "Manushya",
        "KRITTIKA": "Rakshasa",
        "ROHINI": "Manushya",
        "MRIGASIRA": "Deva",
        "ARUDRA": "Manushya",
        "PUNARVASU": "Deva",
        "PUSHYAMI": "Deva",
        "ASLESHA": "Rakshasa",
        "MAGHA": "Rakshasa",
        "POORVAPHALGUNI": "Manushya",
        "UTTARAPHALGUNI": "Manushya",
        "HASTA": "Deva",
        "CHITRA": "Rakshasa",
        "SWATI": "Deva",
        "VISHAKHA": "Rakshasa",
        "ANURADHA": "Deva",
        "JYESHTHA": "Rakshasa",
        "MOOLA": "Rakshasa",
        "POORVASHADHA": "Manushya",
        "UTTARASHADHA": "Manushya",
        "SHRAVANA": "Deva",
        "DHANISHTA": "Rakshasa",
        "SHATABHISHA": "Rakshasa",
        "POORVABHADRA": "Manushya",
        "UTTARABHADRA": "Manushya",
        "REVATI": "Deva"
    }

    # Tamil nakshatra names mapping
    tamil_nakshatra_mapping = {
        "ASHWINI": "ASHWINI",
        "BHARANI": "BHARANI",
        "KRITTIKA": "KARTHIGAI",
        "ROHINI": "ROHINI",
        "MRIGASIRA": "MRIGASHEERSHAM",
        "ARUDRA": "THIRUVADHIRAI",
        "PUNARVASU": "PUNARPOOSAM",
        "PUSHYAMI": "POOSAM",
        "ASLESHA": "AYILYAM",
        "MAGHA": "MAGAM",
        "POORVAPHALGUNI": "POORAM",
        "UTTARAPHALGUNI": "UTHIRAM",
        "HASTA": "HASTHAM",
        "CHITRA": "CHITHIRAI",
        "SWATI": "SWATHI",
        "VISHAKHA": "VISAKAM",
        "ANURADHA": "ANUSHAM",
        "JYESHTHA": "KETTAI",
        "MOOLA": "MOOLAM",
        "POORVASHADHA": "POORADAM",
        "UTTARASHADHA": "UTHIRADAM",
        "SHRAVANA": "THIRUVONAM",
        "DHANISHTA": "AVITTAM",
        "SHATABHISHA": "SADAYAM",
        "POORVABHADRA": "POORATTADHI",
        "UTTARABHADRA": "UTHIRATTADHI",
        "REVATI": "REVATHI"
    }

    try:
        # Convert inputs to uppercase for matching
        bride_nakshatra = bride_nakshatra.upper() if bride_nakshatra else ""
        groom_nakshatra = groom_nakshatra.upper() if groom_nakshatra else ""

        # Create reverse mapping from Tamil to Sanskrit
        reverse_mapping = {}
        for sanskrit, tamil in tamil_nakshatra_mapping.items():
            reverse_mapping[tamil.upper()] = sanskrit

        # Convert Tamil nakshatra names to Sanskrit if needed
        if bride_nakshatra in reverse_mapping:
            bride_nakshatra = reverse_mapping[bride_nakshatra]
        if groom_nakshatra in reverse_mapping:
            groom_nakshatra = reverse_mapping[groom_nakshatra]

        # Get Gana for bride and groom
        bride_gana = nakshatra_gana.get(bride_nakshatra)
        groom_gana = nakshatra_gana.get(groom_nakshatra)

        if not bride_gana or not groom_gana:
            return {"result": "Invalid Nakshatra", "points": 0}

        # Check Gana Porutham rules
        if bride_gana == groom_gana:
            return {"result": "Excellent Match", "points": 6}
        elif (bride_gana == "Deva" and groom_gana == "Manushya") or (bride_gana == "Manushya" and groom_gana == "Deva"):
            return {"result": "Good Match", "points": 5}
        elif (bride_gana == "Manushya" and groom_gana == "Rakshasa") or (bride_gana == "Rakshasa" and groom_gana == "Manushya"):
            return {"result": "Partial Match", "points": 1}
        else:  # Deva and Rakshasa
            return {"result": "No Match", "points": 0}
    except Exception as e:
        print(f"Error calculating Gana Porutham: {str(e)}")
        return {"result": "Error", "points": 0}


def rasi_porutham(bride_rasi, groom_rasi):
    """
    Calculate Rasi Porutham (Moon sign compatibility)

    Args:
        bride_rasi (str): Bride's rasi (moon sign)
        groom_rasi (str): Groom's rasi (moon sign)

    Returns:
        dict: Dictionary containing result and points
    """
    # Define Rasi relationships
    rasi_data = {
        "MESHAM": {
            "FRIENDLY": ["SIMMAM", "DHANUSU"],
            "NEUTRAL": ["RISHABAM", "MIDUNAM", "KANNI", "THULAM", "MAGARAM", "KUMBAM"],
            "ENEMY": ["KADAGAM", "VIRICHIGAM", "MEENAM"]
        },
        "RISHABAM": {
            "FRIENDLY": ["KADAGAM", "KANNI"],
            "NEUTRAL": ["MESHAM", "MIDUNAM", "SIMMAM", "THULAM", "VIRICHIGAM", "MEENAM"],
            "ENEMY": ["DHANUSU", "MAGARAM", "KUMBAM"]
        },
        "MIDUNAM": {
            "FRIENDLY": ["THULAM", "KUMBAM"],
            "NEUTRAL": ["MESHAM", "RISHABAM", "KADAGAM", "SIMMAM", "DHANUSU", "MEENAM"],
            "ENEMY": ["KANNI", "VIRICHIGAM", "MAGARAM"]
        },
        "KADAGAM": {
            "FRIENDLY": ["VIRICHIGAM", "MEENAM"],
            "NEUTRAL": ["RISHABAM", "MIDUNAM", "SIMMAM", "KANNI", "DHANUSU", "KUMBAM"],
            "ENEMY": ["MESHAM", "THULAM", "MAGARAM"]
        },
        "SIMMAM": {
            "FRIENDLY": ["MESHAM", "DHANUSU"],
            "NEUTRAL": ["RISHABAM", "MIDUNAM", "KADAGAM", "KANNI", "MAGARAM", "KUMBAM"],
            "ENEMY": ["THULAM", "VIRICHIGAM", "MEENAM"]
        },
        "KANNI": {
            "FRIENDLY": ["RISHABAM", "MAGARAM"],
            "NEUTRAL": ["MESHAM", "KADAGAM", "SIMMAM", "THULAM", "VIRICHIGAM", "KUMBAM"],
            "ENEMY": ["MIDUNAM", "DHANUSU", "MEENAM"]
        },
        "THULAM": {
            "FRIENDLY": ["MIDUNAM", "KUMBAM"],
            "NEUTRAL": ["RISHABAM", "KADAGAM", "KANNI", "VIRICHIGAM", "DHANUSU", "MEENAM"],
            "ENEMY": ["MESHAM", "SIMMAM", "MAGARAM"]
        },
        "VIRICHIGAM": {
            "FRIENDLY": ["KADAGAM", "MEENAM"],
            "NEUTRAL": ["RISHABAM", "MIDUNAM", "KANNI", "THULAM", "DHANUSU", "KUMBAM"],
            "ENEMY": ["MESHAM", "SIMMAM", "MAGARAM"]
        },
        "DHANUSU": {
            "FRIENDLY": ["MESHAM", "SIMMAM"],
            "NEUTRAL": ["MIDUNAM", "KADAGAM", "THULAM", "VIRICHIGAM", "MAGARAM", "MEENAM"],
            "ENEMY": ["RISHABAM", "KANNI", "KUMBAM"]
        },
        "MAGARAM": {
            "FRIENDLY": ["KANNI", "KUMBAM"],
            "NEUTRAL": ["MESHAM", "SIMMAM", "THULAM", "DHANUSU", "MEENAM"],
            "ENEMY": ["RISHABAM", "MIDUNAM", "KADAGAM", "VIRICHIGAM"]
        },
        "KUMBAM": {
            "FRIENDLY": ["MIDUNAM", "THULAM"],
            "NEUTRAL": ["MESHAM", "KADAGAM", "SIMMAM", "KANNI", "VIRICHIGAM", "MAGARAM"],
            "ENEMY": ["RISHABAM", "DHANUSU", "MEENAM"]
        },
        "MEENAM": {
            "FRIENDLY": ["KADAGAM", "VIRICHIGAM"],
            "NEUTRAL": ["MIDUNAM", "THULAM", "DHANUSU", "MAGARAM"],
            "ENEMY": ["MESHAM", "RISHABAM", "SIMMAM", "KANNI", "KUMBAM"]
        }
    }

    # Add alternative spellings
    rasi_data["VRICHIGAM"] = rasi_data["VIRICHIGAM"]
    rasi_data["MAKARAM"] = rasi_data["MAGARAM"]

    try:
        # Convert inputs to uppercase for matching
        bride_rasi = bride_rasi.upper() if bride_rasi else ""
        groom_rasi = groom_rasi.upper() if groom_rasi else ""

        if bride_rasi not in rasi_data:
            return {"result": "Invalid Bride Rasi", "points": 0}

        # If bride and groom have the same rasi
        if bride_rasi == groom_rasi:
            return {"result": "Excellent Match", "points": 7}

        if groom_rasi in rasi_data[bride_rasi]["FRIENDLY"]:
            return {"result": "Excellent Match", "points": 7}
        elif groom_rasi in rasi_data[bride_rasi]["NEUTRAL"]:
            return {"result": "Partial Match", "points": 5}
        elif groom_rasi in rasi_data[bride_rasi]["ENEMY"]:
            return {"result": "No Match", "points": 0}
        else:
            return {"result": "Invalid Groom Rasi", "points": 0}
    except Exception as e:
        print(f"Error calculating Rasi Porutham: {str(e)}")
        return {"result": "Error", "points": 0}


def analyze_rasi_marriage_compatibility(bride_id, groom_id, marriage_date=None):
    """
    Analyze marriage compatibility based on Rasi (Moon Sign) positions.

    Args:
        bride_id (str): Bride's member ID
        groom_id (str): Groom's member ID
        marriage_date (str, optional): Proposed marriage date

    Returns:
        dict: Compatibility analysis results
    """
    try:
        # Get Rasi data for bride and groom
        bride_data = get_member_rasi_data(bride_id)
        groom_data = get_member_rasi_data(groom_id)

        if not bride_data or not groom_data:
            return {
                'success': False,
                'message': 'Rasi data not found for one or both members'
            }

        # Check gender
        if bride_data.get('gender', '').lower() != 'female' or groom_data.get('gender', '').lower() != 'male':
            print(f"Warning: Gender mismatch. Bride: {bride_data.get('gender')}, Groom: {groom_data.get('gender')}")

        # Extract Rasi and Nakshatra information
        bride_rasi = bride_data.get('moon_rasi', '')
        groom_rasi = groom_data.get('moon_rasi', '')
        bride_nakshatra = bride_data.get('moon_nakshatra', '')
        groom_nakshatra = groom_data.get('moon_nakshatra', '')

        # Calculate compatibility factors
        compatibility_results = {}

        # 1. Dina Porutham
        dina_result = dina_porutham(bride_nakshatra, groom_nakshatra)
        compatibility_results["1.1"] = {
            "name": "Dina Porutham",
            "description": "Day compatibility",
            "result": dina_result["result"],
            "points": dina_result["points"],
            "max_points": 3
        }

        # 2. Gana Porutham
        gana_result = gana_porutham(bride_nakshatra, groom_nakshatra)
        compatibility_results["1.2"] = {
            "name": "Gana Porutham",
            "description": "Temperament compatibility",
            "result": gana_result["result"],
            "points": gana_result["points"],
            "max_points": 6
        }

        # 3. Rasi Porutham
        rasi_result = rasi_porutham(bride_rasi, groom_rasi)
        compatibility_results["1.3"] = {
            "name": "Rasi Porutham",
            "description": "Moon sign compatibility",
            "result": rasi_result["result"],
            "points": rasi_result["points"],
            "max_points": 7
        }

        # Calculate overall compatibility
        total_points = sum(item["points"] for item in compatibility_results.values())
        max_points = sum(item["max_points"] for item in compatibility_results.values())
        percentage = (total_points / max_points * 100) if max_points > 0 else 0

        # Determine overall compatibility
        if percentage >= 80:
            overall_compatibility = "Excellent Match"
        elif percentage >= 60:
            overall_compatibility = "Good Match"
        elif percentage >= 40:
            overall_compatibility = "Partial Match"
        else:
            overall_compatibility = "Poor Match"

        # Prepare result
        result = {
            'success': True,
            'bride': {
                'id': bride_id,
                'name': bride_data.get('name', ''),
                'rasi': bride_rasi,
                'nakshatra': bride_nakshatra
            },
            'groom': {
                'id': groom_id,
                'name': groom_data.get('name', ''),
                'rasi': groom_rasi,
                'nakshatra': groom_nakshatra
            },
            'compatibility_factors': compatibility_results,
            'total_points': total_points,
            'max_points': max_points,
            'percentage': round(percentage, 2),
            'overall_compatibility': overall_compatibility,
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # Add marriage date if provided
        if marriage_date:
            result['marriage_date'] = marriage_date

        return result

    except Exception as e:
        import traceback
        print(f"Error in analyze_rasi_marriage_compatibility: {str(e)}")
        print(traceback.format_exc())
        return {
            'success': False,
            'message': f'Error analyzing Rasi-based marriage compatibility: {str(e)}'
        }
