"""
Marriage Matching Reference Data

This module provides reference data for marriage matching calculations.
"""

import pandas as pd


def get_planets_friends_neutral_enemies():
    """
    Get planet relationships data.
    
    Returns:
        DataFrame: DataFrame with planet relationships
    """
    data = {
        'Planet': ['SUN', 'MOON', 'MARS', 'MERCURY', '<PERSON><PERSON><PERSON><PERSON>', 'VEN<PERSON>', 'SATUR<PERSON>'],
        'Friends': [
            'MO<PERSON>, MA<PERSON>, J<PERSON>ITER', 
            'SUN, MERCURY', 
            'SUN, MOON, JUPITER', 
            'SUN, VENUS', 
            'SUN, MOON, MARS', 
            'MERCURY, SATURN', 
            'MERCURY, VENUS'
        ],
        'Enemies': [
            'VENUS, SATURN', 
            'NONE', 
            'MERCURY', 
            'MOON', 
            'VENUS', 
            'SUN, MOON, MARS', 
            'SUN, MOON, MARS'
        ],
        'Neutral': [
            'MERCURY', 
            'MA<PERSON>, J<PERSON><PERSON><PERSON>, VENUS, SATURN', 
            'VENUS, <PERSON>URN', 
            '<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>UR<PERSON>', 
            'MERCURY, <PERSON>UR<PERSON>', 
            '<PERSON><PERSON><PERSON><PERSON>', 
            'J<PERSON><PERSON><PERSON>'
        ]
    }
    return pd.DataFrame(data)


def get_planets_exalt_debilitate():
    """
    Get planet exaltation and debilitation data.
    
    Returns:
        DataFrame: DataFrame with planet exaltation and debilitation data
    """
    data = {
        'Planet': ['SUN', 'MOON', 'MARS', 'MERCURY', 'JUPITER', 'VENUS', 'SATURN'],
        'Exalt (Max. Power)': [
            'MESHAM', 
            'RISHABAM', 
            'MAGARAM', 
            'KANNI', 
            'KADAGAM', 
            'MEENAM', 
            'THULAM'
        ],
        'Debilitate (Weakest)': [
            'THULAM', 
            'VRICHIGAM', 
            'KADAGAM', 
            'MEENAM', 
            'MAGARAM', 
            'KANNI', 
            'MESHAM'
        ]
    }
    return pd.DataFrame(data)


def get_house_name_data():
    """
    Get house name data.
    
    Returns:
        DataFrame: DataFrame with house name data
    """
    data = {
        'House Name': [
            'MESHAM', 'RISHABAM', 'MIDUNAM', 'KADAGAM', 'SIMMAM', 
            'KANNI', 'THULAM', 'VRICHIGAM', 'DHANUSU', 'MAGARAM', 
            'KUMBAM', 'MEENAM'
        ],
        'Ruling Planets': [
            'MARS', 'VENUS', 'MERCURY', 'MOON', 'SUN', 
            'MERCURY', 'VENUS', 'MARS', 'JUPITER', 'SATURN', 
            'SATURN', 'JUPITER'
        ]
    }
    return pd.DataFrame(data)


def get_nakshatra_data():
    """
    Get nakshatra data.
    
    Returns:
        DataFrame: DataFrame with nakshatra data
    """
    data = {
        'Nakshatra': [
            'ASHWINI', 'BHARANI', 'KRITTIKA', 'ROHINI', 'MRIGASIRA', 'ARUDRA',
            'PUNARVASU', 'PUSHYAMI', 'ASLESHA', 'MAGHA', 'POORVA PHALGUNI', 'UTTARA PHALGUNI',
            'HASTA', 'CHITRA', 'SWATI', 'VISHAKHA', 'ANURADHA', 'JYESHTHA',
            'MOOLA', 'POORVASHADA', 'UTTARASHADA', 'SHRAVANA', 'DHANISHTA', 'SHATABHISHA',
            'POORVA BHADRAPADA', 'UTTARA BHADRAPADA', 'REVATI'
        ],
        'Planet': [
            'KETU', 'VENUS', 'SUN', 'MOON', 'MARS', 'RAHU',
            'JUPITER', 'SATURN', 'MERCURY', 'KETU', 'VENUS', 'SUN',
            'MOON', 'MARS', 'RAHU', 'JUPITER', 'SATURN', 'MERCURY',
            'KETU', 'VENUS', 'SUN', 'MOON', 'MARS', 'RAHU',
            'JUPITER', 'SATURN', 'MERCURY'
        ],
        'Stars (Tamil)': [
            'ASHWINI', 'BARANI', 'KARTHIKAI', 'ROHINI', 'MIRIGASIRISHAM', 'THIRUVADIRAI',
            'PUNARPOOSAM', 'POOSAM', 'AYILYAM', 'MAGAM', 'POORAM', 'UTHIRAM',
            'HASTHAM', 'CHITHIRAI', 'SWATHI', 'VISAGAM', 'ANUSHAM', 'KETTAI',
            'MOOLAM', 'POORADAM', 'UTHIRADAM', 'THIRUVONAM', 'AVITTAM', 'SADAYAM',
            'POORATADHI', 'UTHIRATTADHI', 'REVATHI'
        ]
    }
    return pd.DataFrame(data)


def get_yoni_data():
    """
    Get yoni data for marriage matching.
    
    Returns:
        dict: Dictionary with yoni data
    """
    return {
        "ASHWINI": {"Animal": "Horse", "Gender": "Male", "Enemies": ["Buffalo"]},
        "BARANI": {"Animal": "Elephant", "Gender": "Male", "Enemies": ["Lion"]},
        "KARTHIKAI": {"Animal": "Goat", "Gender": "Female", "Enemies": ["Monkey"]},
        "ROHINI": {"Animal": "Snake", "Gender": "Male", "Enemies": ["Rat", "Mongoose"]},
        "MIRIGASIRISHAM": {"Animal": "Snake", "Gender": "Female", "Enemies": ["Rat", "Mongoose"]},
        "THIRUVADIRAI": {"Animal": "Dog", "Gender": "Male", "Enemies": ["Deer"]},
        "PUNARPOOSAM": {"Animal": "Cat", "Gender": "Female", "Enemies": ["Rat"]},
        "POOSAM": {"Animal": "Goat", "Gender": "Male", "Enemies": ["Monkey"]},
        "AYILYAM": {"Animal": "Cat", "Gender": "Male", "Enemies": ["Rat"]},
        "MAGAM": {"Animal": "Rat", "Gender": "Female", "Enemies": ["Cat", "Snake"]},
        "POORAM": {"Animal": "Rat", "Gender": "Male", "Enemies": ["Cat", "Snake"]},
        "UTHIRAM": {"Animal": "Cow", "Gender": "Female", "Enemies": ["Tiger"]},
        "HASTHAM": {"Animal": "Buffalo", "Gender": "Male", "Enemies": ["Horse"]},
        "CHITHIRAI": {"Animal": "Tiger", "Gender": "Female", "Enemies": ["Cow"]},
        "SWATHI": {"Animal": "Buffalo", "Gender": "Female", "Enemies": ["Horse"]},
        "VISAGAM": {"Animal": "Tiger", "Gender": "Male", "Enemies": ["Cow"]},
        "ANUSHAM": {"Animal": "Deer", "Gender": "Female", "Enemies": ["Dog"]},
        "KETTAI": {"Animal": "Deer", "Gender": "Male", "Enemies": ["Dog"]},
        "MOOLAM": {"Animal": "Dog", "Gender": "Female", "Enemies": ["Deer"]},
        "POORADAM": {"Animal": "Monkey", "Gender": "Male", "Enemies": ["Goat"]},
        "UTHIRADAM": {"Animal": "Mongoose", "Gender": "Female", "Enemies": ["Snake"]},
        "THIRUVONAM": {"Animal": "Lion", "Gender": "Male", "Enemies": ["Elephant"]},
        "AVITTAM": {"Animal": "Cow", "Gender": "Male", "Enemies": ["Tiger"]},
        "SADAYAM": {"Animal": "Horse", "Gender": "Female", "Enemies": ["Buffalo"]},
        "POORATADHI": {"Animal": "Lion", "Gender": "Female", "Enemies": ["Elephant"]},
        "UTHIRATTADHI": {"Animal": "Cow", "Gender": "Female", "Enemies": ["Tiger"]},
        "REVATHI": {"Animal": "Elephant", "Gender": "Female", "Enemies": ["Lion"]}
    }


def get_vedhai_mismatch_map():
    """
    Get vedhai mismatch map for marriage matching.
    
    Returns:
        dict: Dictionary with vedhai mismatch map
    """
    return {
        "ASHWINI": ["KETTAI"],
        "BARANI": ["ANUSHAM"],
        "KARTHIKAI": ["VISAGAM"],
        "ROHINI": ["SWATHI"],
        "MIRIGASIRISHAM": ["CHITHIRAI", "AVITTAM"],
        "THIRUVADIRAI": ["THIRUVONAM"],
        "PUNARPOOSAM": ["UTHIRADAM"],
        "POOSAM": ["POORADAM"],
        "AYILYAM": ["MOOLAM"],
        "MAGAM": ["REVATHI"],
        "POORAM": ["UTHIRATTADHI"],
        "UTHIRAM": ["POORATADHI"],
        "HASTHAM": ["SADAYAM"],
        "CHITHIRAI": ["AVITTAM", "MIRIGASIRISHAM"],
        "SWATHI": ["ROHINI"],
        "VISAGAM": ["KARTHIKAI"],
        "ANUSHAM": ["BARANI"],
        "KETTAI": ["ASHWINI"],
        "MOOLAM": ["AYILYAM"],
        "POORADAM": ["POOSAM"],
        "UTHIRADAM": ["PUNARPOOSAM"],
        "THIRUVONAM": ["THIRUVADIRAI"],
        "AVITTAM": ["CHITHIRAI", "MIRIGASIRISHAM"],
        "SADAYAM": ["HASTHAM"],
        "POORATADHI": ["UTHIRAM"],
        "UTHIRATTADHI": ["POORAM"],
        "REVATHI": ["MAGAM"]
    }


def get_rasi_relationships():
    """
    Get rasi relationships for marriage matching.
    
    Returns:
        dict: Dictionary with rasi relationships
    """
    return {
        "MESHAM": {
            "FRIENDLY": ["MIDUNAM", "SIMMAM", "DHANUSU", "KUMBAM"],
            "ENEMY": ["VRICHIGAM"],
            "NEUTRAL": ["RISHABAM", "KADAGAM", "KANNI", "THULAM", "MAGARAM", "MEENAM"]
        },
        "RISHABAM": {
            "FRIENDLY": ["KADAGAM", "KANNI", "MAGARAM", "MEENAM"],
            "ENEMY": ["DHANUSU"],
            "NEUTRAL": ["MIDUNAM", "SIMMAM", "THULAM", "VRICHIGAM", "KUMBAM", "MESHAM"]
        },
        "MIDUNAM": {
            "FRIENDLY": ["MESHAM", "SIMMAM", "THULAM", "KUMBAM"],
            "ENEMY": ["VRICHIGAM"],
            "NEUTRAL": ["RISHABAM", "KADAGAM", "KANNI", "THULAM", "MAGARAM", "MEENAM"]
        },
        "KADAGAM": {
            "FRIENDLY": ["RISHABAM", "KANNI", "VRICHIGAM", "MEENAM"],
            "ENEMY": ["KUMBAM"],
            "NEUTRAL": ["SIMMAM", "THULAM", "DHANUSU", "MAGARAM", "MESHAM", "MIDUNAM"]
        },
        "SIMMAM": {
            "FRIENDLY": ["MESHAM", "MIDUNAM", "THULAM", "DHANUSU"],
            "ENEMY": ["MAGARAM"],
            "NEUTRAL": ["RISHABAM", "KADAGAM", "KANNI", "VRICHIGAM", "KUMBAM", "MEENAM"]
        },
        "KANNI": {
            "FRIENDLY": ["RISHABAM", "KADAGAM", "VRICHIGAM", "MAGARAM"],
            "ENEMY": ["MEENAM"],
            "NEUTRAL": ["MESHAM", "MIDUNAM", "SIMMAM", "THULAM", "DHANUSU", "KUMBAM"]
        },
        "THULAM": {
            "FRIENDLY": ["MIDUNAM", "SIMMAM", "KUMBAM", "MEENAM"],
            "ENEMY": ["MESHAM"],
            "NEUTRAL": ["RISHABAM", "KADAGAM", "KANNI", "VRICHIGAM", "DHANUSU", "MAGARAM"]
        },
        "VRICHIGAM": {
            "FRIENDLY": ["KADAGAM", "KANNI", "MAGARAM", "MEENAM"],
            "ENEMY": ["MESHAM", "MIDUNAM"],
            "NEUTRAL": ["RISHABAM", "SIMMAM", "THULAM", "DHANUSU", "KUMBAM"]
        },
        "DHANUSU": {
            "FRIENDLY": ["MESHAM", "SIMMAM", "KUMBAM", "MEENAM"],
            "ENEMY": ["RISHABAM"],
            "NEUTRAL": ["MIDUNAM", "KADAGAM", "KANNI", "THULAM", "VRICHIGAM", "MAGARAM"]
        },
        "MAGARAM": {
            "FRIENDLY": ["RISHABAM", "KANNI", "VRICHIGAM", "KUMBAM"],
            "ENEMY": ["SIMMAM"],
            "NEUTRAL": ["MESHAM", "MIDUNAM", "KADAGAM", "THULAM", "DHANUSU", "MEENAM"]
        },
        "KUMBAM": {
            "FRIENDLY": ["MESHAM", "MIDUNAM", "THULAM", "DHANUSU", "MAGARAM"],
            "ENEMY": ["KADAGAM"],
            "NEUTRAL": ["RISHABAM", "SIMMAM", "KANNI", "VRICHIGAM", "MEENAM"]
        },
        "MEENAM": {
            "FRIENDLY": ["RISHABAM", "KADAGAM", "THULAM", "VRICHIGAM", "DHANUSU"],
            "ENEMY": ["KANNI"],
            "NEUTRAL": ["MESHAM", "MIDUNAM", "SIMMAM", "MAGARAM", "KUMBAM"]
        }
    }


def get_vasya_relationships():
    """
    Get vasya relationships for marriage matching.
    
    Returns:
        dict: Dictionary with vasya relationships
    """
    return {
        "MESHAM": ["SIMMAM", "RISHABAM"],
        "RISHABAM": ["MESHAM", "KADAGAM"],
        "MIDUNAM": ["KANNI"],
        "KADAGAM": ["RISHABAM", "MEENAM"],
        "SIMMAM": ["MESHAM", "THULAM"],
        "KANNI": ["MIDUNAM", "DHANUSU"],
        "THULAM": ["SIMMAM", "VRICHIGAM"],
        "VRICHIGAM": ["THULAM", "KUMBAM"],
        "DHANUSU": ["MEENAM", "KANNI"],
        "MAGARAM": ["KUMBAM", "MEENAM"],
        "KUMBAM": ["VRICHIGAM", "MAGARAM"],
        "MEENAM": ["KADAGAM", "DHANUSU"]
    }
