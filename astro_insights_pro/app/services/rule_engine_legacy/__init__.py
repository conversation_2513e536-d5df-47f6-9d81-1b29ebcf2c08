"""
Rule Engine Legacy Package

This package contains the organized sub-modules of the rule engine legacy system.
Each module handles a specific aspect of the rule engine functionality.

Modules:
- planetary_motion: Planetary motion calculations and transit predictions
- chart_processing: Chart data processing and house system mappings
- condition_evaluation: Condition parsing and evaluation logic
- relationship_checking: Astrological relationship checking functions
- dasha_processing: Dasha-related calculations and processing
- kocharam_filter: KOCHARAM (transit) filtering functionality
- response_formatting: Response formatting and analysis creation
"""

# Import key functions from each module for easy access

from .planetary_motion import (
    get_fast_planetary_daily_motion,
    get_dynamic_planetary_daily_motion,
    calculate_planet_transit_to_house,
    calculate_all_planet_transits_in_period,
    get_current_planetary_position
)

from .chart_processing import (
    get_default_house_ruling_planets,
    get_standard_planet_names,
    get_dynamic_house_name_ruling_planets,
    get_full_d1_chart_details,
    get_user_house_system,
    get_chart_data,
    get_planet_house_mapping,
    normalize_planet_name
)

from .condition_evaluation import (
    parse_condition,
    parse_complex_query,
    evaluate_condition,
    evaluate_parsed_query,
    evaluate_rule
)

from .relationship_checking import (
    check_comprehensive_relationship,
    check_house_ruling_planet_relationship,
    check_planet_to_house_planet_relationship,
    check_house_planet_relationship
)

from .dasha_processing import (
    parse_dasha_string,
    get_member_age_from_chart_data,
    get_dasha_periods_for_planet,
    get_house_ruling_planet_dasha_periods,
    get_planets_with_stars_of_planet,
    get_planets_in_house_list,
    get_dasha_periods_for_planets_list,
    check_age_condition,
    check_dasha_dates_in_prediction_period,
    filter_dasha_periods_within_prediction_window,
    parse_and_evaluate_dasha_query
)

from .kocharam_filter import (
    validate_kocharam_filter,
    parse_single_kocharam_condition,
    process_single_kocharam_condition,
    process_planet_in_house_condition,
    apply_simplified_kocharam_logic,
    validate_transit_dates_in_period,
    calculate_filtering_efficiency
)

from .response_formatting import (
    create_clean_dasha_response,
    format_dasha_periods_clean,
    format_successful_conditions_clean,
    format_condition_breakdown_clean,
    create_evaluation_summary,
    create_response_summary,
    create_kocharam_analysis_summary,
    get_prediction_period_dates
)

__all__ = [
    # Planetary Motion
    'get_fast_planetary_daily_motion',
    'get_dynamic_planetary_daily_motion',
    'calculate_planet_transit_to_house',
    'calculate_all_planet_transits_in_period',
    'get_current_planetary_position',
    
    # Chart Processing
    'get_default_house_ruling_planets',
    'get_standard_planet_names',
    'get_dynamic_house_name_ruling_planets',
    'get_full_d1_chart_details',
    'get_user_house_system',
    'get_chart_data',
    'get_planet_house_mapping',
    'normalize_planet_name',
    
    # Condition Evaluation
    'parse_condition',
    'parse_complex_query',
    'evaluate_condition',
    'evaluate_parsed_query',
    'evaluate_rule',
    
    # Relationship Checking
    'check_comprehensive_relationship',
    'check_house_ruling_planet_relationship',
    'check_planet_to_house_planet_relationship',
    'check_house_planet_relationship',
    
    # Dasha Processing
    'parse_dasha_string',
    'get_member_age_from_chart_data',
    'get_dasha_periods_for_planet',
    'get_house_ruling_planet_dasha_periods',
    'get_planets_with_stars_of_planet',
    'get_planets_in_house_list',
    'get_dasha_periods_for_planets_list',
    'check_age_condition',
    'check_dasha_dates_in_prediction_period',
    'filter_dasha_periods_within_prediction_window',
    'parse_and_evaluate_dasha_query',
    
    # KOCHARAM Filter
    'validate_kocharam_filter',
    'parse_single_kocharam_condition',
    'process_single_kocharam_condition',
    'process_planet_in_house_condition',
    'apply_simplified_kocharam_logic',
    'validate_transit_dates_in_period',
    'calculate_filtering_efficiency',
    
    # Response Formatting
    'create_clean_dasha_response',
    'format_dasha_periods_clean',
    'format_successful_conditions_clean',
    'format_condition_breakdown_clean',
    'create_evaluation_summary',
    'create_response_summary',
    'create_kocharam_analysis_summary',
    'get_prediction_period_dates'
]
