"""
Relationship Checking Functions

This module handles checking various astrological relationships between
planets, houses, and ruling planets.
"""

from .chart_processing import get_planet_house_mapping


def check_comprehensive_relationship(chart_data, planet, house_number, chart_type="D1"):
    """
    Check comprehensive relationship between a planet and house ruling planet.
    
    This function checks multiple types of relationships:
    1. Position exchange (mutual reception)
    2. WITH ruling planet (conjunction)
    3. TOGETHER_WITH (same house placement)
    4. Nakshatra relationships
    5. Aspecting relationships
    
    Args:
        chart_data (dict): Chart data from MongoDB
        planet (str): Planet name (e.g., 'JUPITER')
        house_number (int): House number (1-12)
        chart_type (str): Chart type (default: "D1")
        
    Returns:
        dict: Comprehensive relationship analysis
    """
    try:
        result = {
            'planet': planet.upper(),
            'house_number': house_number,
            'chart_type': chart_type,
            'relationships_found': [],
            'relationship_summary': {},
            'success': False
        }
        
        # Get planet-house mapping
        planet_house_mapping = get_planet_house_mapping(chart_data, chart_type)
        
        # Get the house ruling planet
        house_ruling_planet = get_house_ruling_planet_from_chart(chart_data, house_number, chart_type)
        
        if not house_ruling_planet:
            result['error'] = f'Could not determine ruling planet for house {house_number}'
            return result
        
        result['house_ruling_planet'] = house_ruling_planet
        
        # Check different types of relationships
        relationships = []
        
        # 1. Check if planet is WITH ruling planet (conjunction)
        with_relationship = check_planet_with_ruling_planet_relationship(
            chart_data, planet, house_number, chart_type, 'WITH'
        )
        if with_relationship.get('success'):
            relationships.append('WITH_RULING_PLANET')
            result['with_ruling_planet'] = with_relationship
        
        # 2. Check if planet is in the star of ruling planet
        star_relationship = check_planet_in_star_of_house_ruling_planet_relationship(
            chart_data, planet, house_number, chart_type
        )
        if star_relationship.get('success'):
            relationships.append('IN_STAR_OF_RULING_PLANET')
            result['star_relationship'] = star_relationship
        
        # 3. Check if planet is aspecting ruling planet
        aspect_relationship = check_planet_aspecting_house_ruling_planet_relationship(
            chart_data, planet, house_number, chart_type
        )
        if aspect_relationship.get('success'):
            relationships.append('ASPECTING_RULING_PLANET')
            result['aspect_relationship'] = aspect_relationship
        
        # 4. Check position exchange
        exchange_relationship = check_position_exchange(
            chart_data, planet, house_ruling_planet, chart_type
        )
        if exchange_relationship.get('success'):
            relationships.append('POSITION_EXCHANGE')
            result['exchange_relationship'] = exchange_relationship
        
        # Update result
        result['relationships_found'] = relationships
        result['success'] = len(relationships) > 0
        result['relationship_count'] = len(relationships)
        
        # Create summary
        result['relationship_summary'] = {
            'total_relationships': len(relationships),
            'relationship_types': relationships,
            'strongest_relationship': relationships[0] if relationships else None,
            'relationship_strength': 'STRONG' if len(relationships) >= 2 else 'MODERATE' if len(relationships) == 1 else 'NONE'
        }
        
        return result
        
    except Exception as e:
        return {
            'planet': planet,
            'house_number': house_number,
            'success': False,
            'error': str(e),
            'relationships_found': []
        }


def check_house_ruling_planet_relationship(chart_data, house1, house2, chart_type="D1"):
    """
    Check comprehensive relationship between two house ruling planets.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        house1 (int): First house number
        house2 (int): Second house number
        chart_type (str): Chart type
        
    Returns:
        dict: Relationship analysis between house ruling planets
    """
    try:
        result = {
            'house1': house1,
            'house2': house2,
            'chart_type': chart_type,
            'relationships_found': [],
            'success': False
        }
        
        # Get ruling planets for both houses
        ruling_planet1 = get_house_ruling_planet_from_chart(chart_data, house1, chart_type)
        ruling_planet2 = get_house_ruling_planet_from_chart(chart_data, house2, chart_type)
        
        if not ruling_planet1 or not ruling_planet2:
            result['error'] = 'Could not determine ruling planets'
            return result
        
        result['ruling_planet1'] = ruling_planet1
        result['ruling_planet2'] = ruling_planet2
        
        relationships = []
        
        # Check if ruling planets are the same
        if ruling_planet1.upper() == ruling_planet2.upper():
            relationships.append('SAME_RULING_PLANET')
        
        # Check if ruling planets are in conjunction
        conjunction_check = check_planet_conjunction(chart_data, ruling_planet1, ruling_planet2, chart_type)
        if conjunction_check.get('success'):
            relationships.append('RULING_PLANETS_CONJUNCTION')
        
        # Check if ruling planets are aspecting each other
        aspect_check = check_planet_aspecting_relationship(chart_data, ruling_planet1, ruling_planet2, chart_type)
        if aspect_check.get('success'):
            relationships.append('RULING_PLANETS_ASPECTING')
        
        # Check position exchange between ruling planets
        exchange_check = check_position_exchange(chart_data, ruling_planet1, ruling_planet2, chart_type)
        if exchange_check.get('success'):
            relationships.append('RULING_PLANETS_EXCHANGE')
        
        result['relationships_found'] = relationships
        result['success'] = len(relationships) > 0
        result['relationship_count'] = len(relationships)
        
        return result
        
    except Exception as e:
        return {
            'house1': house1,
            'house2': house2,
            'success': False,
            'error': str(e)
        }


def check_planet_to_house_planet_relationship(chart_data, planet, house_number, chart_type="D1"):
    """
    Check comprehensive relationship between a specific planet and planets in a specific house.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        planet (str): Planet name
        house_number (int): House number
        chart_type (str): Chart type
        
    Returns:
        dict: Relationship analysis
    """
    try:
        result = {
            'planet': planet.upper(),
            'house_number': house_number,
            'chart_type': chart_type,
            'relationships_found': [],
            'success': False
        }
        
        # Get planets in the target house
        house_planets = get_planets_in_house(chart_data, house_number, chart_type)
        
        if not house_planets:
            result['note'] = f'No planets found in house {house_number}'
            return result
        
        result['house_planets'] = house_planets
        relationships = []
        
        # Check relationships with each planet in the house
        for house_planet in house_planets:
            # Check conjunction (if the planet is also in this house)
            if check_planet_in_house_placement(chart_data, planet, house_number, chart_type).get('success'):
                relationships.append(f'CONJUNCTION_WITH_{house_planet}')
            
            # Check aspecting relationship
            aspect_check = check_planet_aspecting_relationship(chart_data, planet, house_planet, chart_type)
            if aspect_check.get('success'):
                relationships.append(f'ASPECTING_{house_planet}')
            
            # Check star relationship
            star_check = check_planet_in_star_relationship(chart_data, planet, house_planet, chart_type)
            if star_check.get('success'):
                relationships.append(f'IN_STAR_OF_{house_planet}')
        
        result['relationships_found'] = relationships
        result['success'] = len(relationships) > 0
        result['relationship_count'] = len(relationships)
        
        return result
        
    except Exception as e:
        return {
            'planet': planet,
            'house_number': house_number,
            'success': False,
            'error': str(e)
        }


def check_house_planet_relationship(chart_data, house1, house2, chart_type="D1"):
    """
    Check comprehensive relationship between planets in two houses.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        house1 (int): First house number
        house2 (int): Second house number
        chart_type (str): Chart type
        
    Returns:
        dict: Relationship analysis between house planets
    """
    try:
        result = {
            'house1': house1,
            'house2': house2,
            'chart_type': chart_type,
            'relationships_found': [],
            'success': False
        }
        
        # Get planets in both houses
        house1_planets = get_planets_in_house(chart_data, house1, chart_type)
        house2_planets = get_planets_in_house(chart_data, house2, chart_type)
        
        if not house1_planets or not house2_planets:
            result['note'] = 'One or both houses have no planets'
            return result
        
        result['house1_planets'] = house1_planets
        result['house2_planets'] = house2_planets
        
        relationships = []
        
        # Check relationships between all planet pairs
        for planet1 in house1_planets:
            for planet2 in house2_planets:
                # Check aspecting relationship
                aspect_check = check_planet_aspecting_relationship(chart_data, planet1, planet2, chart_type)
                if aspect_check.get('success'):
                    relationships.append(f'{planet1}_ASPECTING_{planet2}')
                
                # Check star relationship
                star_check = check_planet_in_star_relationship(chart_data, planet1, planet2, chart_type)
                if star_check.get('success'):
                    relationships.append(f'{planet1}_IN_STAR_OF_{planet2}')
                
                # Check reverse star relationship
                reverse_star_check = check_planet_in_star_relationship(chart_data, planet2, planet1, chart_type)
                if reverse_star_check.get('success'):
                    relationships.append(f'{planet2}_IN_STAR_OF_{planet1}')
        
        result['relationships_found'] = relationships
        result['success'] = len(relationships) > 0
        result['relationship_count'] = len(relationships)
        
        return result
        
    except Exception as e:
        return {
            'house1': house1,
            'house2': house2,
            'success': False,
            'error': str(e)
        }


# Helper functions (simplified implementations)

def get_house_ruling_planet_from_chart(chart_data, house_number, chart_type="D1"):
    """Get the ruling planet for a specific house from chart data."""
    try:
        # This is a simplified implementation
        # In production, this would extract the actual ruling planet from chart data
        default_ruling_planets = {
            1: 'MARS', 2: 'VENUS', 3: 'MERCURY', 4: 'MOON',
            5: 'SUN', 6: 'MERCURY', 7: 'VENUS', 8: 'MARS',
            9: 'JUPITER', 10: 'SATURN', 11: 'SATURN', 12: 'JUPITER'
        }
        return default_ruling_planets.get(house_number)
    except:
        return None


def get_planets_in_house(chart_data, house_number, chart_type="D1"):
    """Get all planets present in a specific house."""
    try:
        chart = chart_data.get('chart_data', {}).get(chart_type, {})
        houses = chart.get('houses', [])
        
        for house in houses:
            if house.get('house_number') == house_number:
                planets = house.get('planets', [])
                return [planet.get('planet_name', '').upper() for planet in planets]
        
        return []
    except:
        return []


def check_planet_with_ruling_planet_relationship(chart_data, planet_name, house_num, chart_type, query_subtype):
    """Check if a planet is with a house ruling planet (conjunction)."""
    # Simplified implementation
    return {'success': False, 'note': 'Not fully implemented'}


def check_planet_in_star_of_house_ruling_planet_relationship(chart_data, planet_name, house_num, chart_type):
    """Check if a planet is in the nakshatra/star of a house ruling planet."""
    # Simplified implementation
    return {'success': False, 'note': 'Not fully implemented'}


def check_planet_aspecting_house_ruling_planet_relationship(chart_data, planet_name, house_num, chart_type):
    """Check if a planet is aspecting a house ruling planet."""
    # Simplified implementation
    return {'success': False, 'note': 'Not fully implemented'}


def check_position_exchange(chart_data, planet1, planet2, chart_type):
    """Check if two planets are in position exchange (mutual reception)."""
    # Simplified implementation
    return {'success': False, 'note': 'Not fully implemented'}


def check_planet_conjunction(chart_data, planet1, planet2, chart_type):
    """Check if two planets are in conjunction (same house)."""
    try:
        planet_house_mapping = get_planet_house_mapping(chart_data, chart_type)
        planet1_house = planet_house_mapping.get(planet1.upper())
        planet2_house = planet_house_mapping.get(planet2.upper())
        
        success = planet1_house == planet2_house and planet1_house is not None
        
        return {
            'success': success,
            'planet1_house': planet1_house,
            'planet2_house': planet2_house
        }
    except:
        return {'success': False}


def check_planet_aspecting_relationship(chart_data, source_planet, target_planet, chart_type):
    """Check if source planet is aspecting target planet."""
    # Simplified implementation
    return {'success': False, 'note': 'Not fully implemented'}


def check_planet_in_star_relationship(chart_data, source_planet, target_planet, chart_type):
    """Check if a planet is in another planet's nakshatra/star."""
    # Simplified implementation
    return {'success': False, 'note': 'Not fully implemented'}


def check_planet_in_house_placement(chart_data, planet_name, target_house_num, chart_type):
    """Check if a planet is located in a specific house."""
    try:
        planet_house_mapping = get_planet_house_mapping(chart_data, chart_type)
        planet_house = planet_house_mapping.get(planet_name.upper())
        
        success = planet_house == target_house_num
        
        return {
            'success': success,
            'planet_house': planet_house,
            'target_house': target_house_num
        }
    except:
        return {'success': False}
