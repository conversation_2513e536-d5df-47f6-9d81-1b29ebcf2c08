"""
Condition Parsing & Evaluation

This module handles parsing and evaluation of astrological conditions
and complex queries with logical operators.
"""

import re
from .chart_processing import get_chart_data, get_planet_house_mapping


def parse_condition(condition):
    """
    Parse a single condition. Supports multiple formats:
    - Basic: "JUPITER IN 7"
    - House ruling: "7th_House_Ruling_Planet IN 1"
    - Relationships: "JUPITER WITH 7th_House_Ruling_Planet"
    - Stars: "JUPITER WITH_STARS_OF VENUS"
    - Aspects: "JUPITER ASPECT 7th_House_Ruling_Planet"
    
    Args:
        condition (str): Condition string to parse
        
    Returns:
        dict: Parsed condition with type and parameters
    """
    condition = condition.strip()
    
    # Pattern 1: Planet WITH_STARS_OF Planet
    stars_pattern = r'(\w+)\s+WITH_STARS_OF\s+(\w+)'
    stars_match = re.match(stars_pattern, condition, re.IGNORECASE)
    if stars_match:
        return {
            'type': 'WITH_STARS_OF',
            'planet': stars_match.group(1).upper(),
            'star_planet': stars_match.group(2).upper(),
            'original': condition
        }
    
    # Pattern 2: Planet WITH House_Ruling_Planet
    with_ruling_pattern = r'(\w+)\s+WITH\s+(\d+)(?:st|nd|rd|th)?_House_Ruling_Planet'
    with_ruling_match = re.match(with_ruling_pattern, condition, re.IGNORECASE)
    if with_ruling_match:
        return {
            'type': 'WITH_HOUSE_RULING_PLANET',
            'planet': with_ruling_match.group(1).upper(),
            'house_number': int(with_ruling_match.group(2)),
            'original': condition
        }
    
    # Pattern 3: Planet ASPECT House_Ruling_Planet
    aspect_ruling_pattern = r'(\w+)\s+ASPECT\s+(\d+)(?:st|nd|rd|th)?_House_Ruling_Planet'
    aspect_ruling_match = re.match(aspect_ruling_pattern, condition, re.IGNORECASE)
    if aspect_ruling_match:
        return {
            'type': 'ASPECT_HOUSE_RULING_PLANET',
            'planet': aspect_ruling_match.group(1).upper(),
            'house_number': int(aspect_ruling_match.group(2)),
            'original': condition
        }
    
    # Pattern 4: House_Ruling_Planet IN House
    ruling_in_pattern = r'(\d+)(?:st|nd|rd|th)?_House_Ruling_Planet\s+IN\s+(\d+)'
    ruling_in_match = re.match(ruling_in_pattern, condition, re.IGNORECASE)
    if ruling_in_match:
        return {
            'type': 'HOUSE_RULING_PLANET_IN_HOUSE',
            'ruling_house': int(ruling_in_match.group(1)),
            'target_house': int(ruling_in_match.group(2)),
            'original': condition
        }
    
    # Pattern 5: Basic Planet IN House
    basic_pattern = r'(\w+)\s+IN\s+(\d+)'
    basic_match = re.match(basic_pattern, condition, re.IGNORECASE)
    if basic_match:
        return {
            'type': 'PLANET_IN_HOUSE',
            'planet': basic_match.group(1).upper(),
            'house': int(basic_match.group(2)),
            'original': condition
        }
    
    # Pattern 6: Planet WITH Planet
    with_planet_pattern = r'(\w+)\s+WITH\s+(\w+)'
    with_planet_match = re.match(with_planet_pattern, condition, re.IGNORECASE)
    if with_planet_match:
        return {
            'type': 'PLANET_WITH_PLANET',
            'planet1': with_planet_match.group(1).upper(),
            'planet2': with_planet_match.group(2).upper(),
            'original': condition
        }
    
    # Pattern 7: Planet ASPECT Planet
    aspect_planet_pattern = r'(\w+)\s+ASPECT\s+(\w+)'
    aspect_planet_match = re.match(aspect_planet_pattern, condition, re.IGNORECASE)
    if aspect_planet_match:
        return {
            'type': 'PLANET_ASPECT_PLANET',
            'planet1': aspect_planet_match.group(1).upper(),
            'planet2': aspect_planet_match.group(2).upper(),
            'original': condition
        }
    
    # Default: Unknown condition
    return {
        'type': 'UNKNOWN',
        'original': condition,
        'error': 'Could not parse condition format'
    }


def parse_complex_query(query):
    """
    Parse a complex query with logical operators including OR, AND, and NOT.
    
    Args:
        query (str): Complex query string
        
    Returns:
        list: List of OR groups, each containing AND conditions
    """
    try:
        # Remove extra whitespace and normalize
        query = re.sub(r'\s+', ' ', query.strip())
        
        # Split by OR (case insensitive)
        or_parts = re.split(r'\s+OR\s+', query, flags=re.IGNORECASE)
        
        parsed_query = []
        
        for or_part in or_parts:
            # Split by AND (case insensitive)
            and_parts = re.split(r'\s+AND\s+', or_part.strip(), flags=re.IGNORECASE)
            
            and_conditions = []
            for and_part in and_parts:
                and_part = and_part.strip()
                
                # Handle NOT conditions
                is_negated = False
                if and_part.upper().startswith('NOT '):
                    is_negated = True
                    and_part = and_part[4:].strip()  # Remove 'NOT '
                
                # Parse the condition
                parsed_condition = parse_condition(and_part)
                parsed_condition['negated'] = is_negated
                
                and_conditions.append(parsed_condition)
            
            parsed_query.append({
                'or_group': len(parsed_query) + 1,
                'and_conditions': and_conditions
            })
        
        return parsed_query
        
    except Exception as e:
        return [{
            'or_group': 1,
            'and_conditions': [{
                'type': 'ERROR',
                'error': f'Query parsing failed: {str(e)}',
                'original': query
            }]
        }]


def evaluate_condition(planet, operator, value, condition_type, planet_house_mapping, chart_data=None, chart_type="D1"):
    """
    Evaluate a single condition with enhanced support for ruling planets, relationships, and aspects.
    
    Args:
        planet (str): Planet name
        operator (str): Operator (IN, NOT IN, etc.)
        value: Value to compare against
        condition_type (str): Type of condition
        planet_house_mapping (dict): Planet to house mapping
        chart_data (dict): Chart data
        chart_type (str): Chart type
        
    Returns:
        dict: Evaluation result
    """
    try:
        result = {
            'condition': f"{planet} {operator} {value}",
            'condition_type': condition_type,
            'success': False,
            'details': {},
            'planet_info': {},
            'house_info': {}
        }
        
        # Handle different condition types
        if condition_type == 'PLANET_IN_HOUSE':
            planet_house = planet_house_mapping.get(planet.upper())
            target_house = int(value) if isinstance(value, (str, int)) else value
            
            result['success'] = planet_house == target_house
            result['details'] = {
                'planet_current_house': planet_house,
                'target_house': target_house,
                'match': planet_house == target_house
            }
            
        elif condition_type == 'HOUSE_RULING_PLANET_IN_HOUSE':
            # This would require more complex logic
            result['details'] = {'note': 'House ruling planet evaluation not fully implemented'}
            
        elif condition_type == 'WITH_STARS_OF':
            # This would require nakshatra calculations
            result['details'] = {'note': 'Star relationship evaluation not fully implemented'}
            
        elif condition_type == 'PLANET_WITH_PLANET':
            # Check if both planets are in the same house
            planet1_house = planet_house_mapping.get(planet.upper())
            planet2_house = planet_house_mapping.get(value.upper())
            
            result['success'] = planet1_house == planet2_house and planet1_house is not None
            result['details'] = {
                'planet1_house': planet1_house,
                'planet2_house': planet2_house,
                'conjunction': planet1_house == planet2_house
            }
            
        elif condition_type == 'PLANET_ASPECT_PLANET':
            # This would require aspect calculations
            result['details'] = {'note': 'Aspect evaluation not fully implemented'}
            
        return result
        
    except Exception as e:
        return {
            'condition': f"{planet} {operator} {value}",
            'condition_type': condition_type,
            'success': False,
            'error': str(e),
            'details': {}
        }


def evaluate_parsed_query(parsed_query, planet_house_mapping, chart_data=None, chart_type="D1"):
    """
    Evaluate a parsed query against planet-house mapping.
    
    Args:
        parsed_query (list): Parsed query structure
        planet_house_mapping (dict): Planet to house mapping
        chart_data (dict): Chart data
        chart_type (str): Chart type
        
    Returns:
        dict: Evaluation results
    """
    try:
        results = []
        overall_success = False
        
        for or_group in parsed_query:
            and_conditions = or_group.get('and_conditions', [])
            group_success = True
            group_results = []
            
            for condition in and_conditions:
                condition_type = condition.get('type', 'UNKNOWN')
                is_negated = condition.get('negated', False)
                
                if condition_type == 'PLANET_IN_HOUSE':
                    planet = condition.get('planet', '')
                    house = condition.get('house', 0)
                    
                    evaluation = evaluate_condition(
                        planet, 'IN', house, condition_type,
                        planet_house_mapping, chart_data, chart_type
                    )
                    
                    # Apply negation if needed
                    if is_negated:
                        evaluation['success'] = not evaluation['success']
                        evaluation['negated'] = True
                    
                    group_results.append(evaluation)
                    
                    if not evaluation['success']:
                        group_success = False
                        
                elif condition_type == 'ERROR':
                    group_results.append({
                        'condition': condition.get('original', ''),
                        'success': False,
                        'error': condition.get('error', 'Unknown error')
                    })
                    group_success = False
                    
                else:
                    # Handle other condition types
                    group_results.append({
                        'condition': condition.get('original', ''),
                        'condition_type': condition_type,
                        'success': False,
                        'note': f'Condition type {condition_type} not fully implemented'
                    })
                    group_success = False
            
            results.append({
                'or_group': or_group.get('or_group', len(results) + 1),
                'group_success': group_success,
                'and_conditions': group_results
            })
            
            # OR logic: if any group succeeds, overall succeeds
            if group_success:
                overall_success = True
        
        return {
            'overall_success': overall_success,
            'total_or_groups': len(results),
            'successful_groups': sum(1 for r in results if r['group_success']),
            'results': results
        }
        
    except Exception as e:
        return {
            'overall_success': False,
            'error': str(e),
            'results': []
        }


def evaluate_rule(query, user_profile_id, member_profile_id, chart_type="D1"):
    """
    Evaluate a rule for a specific user and member profile.
    
    Args:
        query (str): Query string to evaluate
        user_profile_id (str): User profile ID
        member_profile_id (str): Member profile ID
        chart_type (str): Chart type
        
    Returns:
        dict: Complete evaluation results
    """
    try:
        # Get chart data
        chart_data = get_chart_data(user_profile_id, member_profile_id)
        
        if 'error' in chart_data:
            return {
                'success': False,
                'error': chart_data['error'],
                'query': query
            }
        
        # Get planet-house mapping
        planet_house_mapping = get_planet_house_mapping(chart_data, chart_type)
        
        # Parse the query
        parsed_query = parse_complex_query(query)
        
        # Evaluate the parsed query
        evaluation_results = evaluate_parsed_query(
            parsed_query, planet_house_mapping, chart_data, chart_type
        )
        
        return {
            'success': evaluation_results['overall_success'],
            'query': query,
            'chart_type': chart_type,
            'user_profile_id': str(user_profile_id),
            'member_profile_id': str(member_profile_id),
            'planet_house_mapping': planet_house_mapping,
            'evaluation_results': evaluation_results,
            'parsed_query': parsed_query
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'query': query
        }
