"""
Dasha Processing Functions

This module handles dasha-related calculations, parsing, and processing
for the rule engine.
"""

import re
from datetime import datetime, timedelta
from dateutil import parser
from .chart_processing import get_chart_data


def parse_dasha_string(dasha_str):
    """
    Parse a dasha string from MongoDB format.
    
    Expected format: "Planet_Name: Start_Date - End_Date"
    Example: "JUPITER: 2023-01-15 - 2024-01-15"
    
    Args:
        dasha_str (str): Dasha string from MongoDB
        
    Returns:
        dict: Parsed dasha information
    """
    try:
        if not dasha_str or ':' not in dasha_str:
            return None
        
        # Split by colon to separate planet and date range
        parts = dasha_str.split(':', 1)
        if len(parts) != 2:
            return None
        
        planet_name = parts[0].strip().upper()
        date_range = parts[1].strip()
        
        # Split date range by dash
        if ' - ' in date_range:
            date_parts = date_range.split(' - ')
            if len(date_parts) == 2:
                start_date = date_parts[0].strip()
                end_date = date_parts[1].strip()
                
                return {
                    'planet_name': planet_name,
                    'start_date': start_date,
                    'end_date': end_date,
                    'original_string': dasha_str
                }
        
        return None
        
    except Exception as e:
        return None


def parse_dasha_string_new_format(dasha_str):
    """
    Parse dasha string in the new format: (planet-planet, start_date, end_date)

    Args:
        dasha_str (str): Dasha string from MongoDB

    Returns:
        dict: Parsed dasha information
    """
    try:
        if not dasha_str or not dasha_str.startswith('(') or not dasha_str.endswith(')'):
            return None

        # Remove parentheses
        content = dasha_str[1:-1]

        # Split by comma
        parts = content.split(', ')
        if len(parts) != 3:
            return None

        # Extract planet name (first part before the dash)
        planet_part = parts[0]
        if '-' in planet_part:
            planet_name = planet_part.split('-')[0].upper()
        else:
            planet_name = planet_part.upper()

        # Extract dates
        start_date_str = parts[1].strip()
        end_date_str = parts[2].strip()

        # Convert date format from "1974-08-05 00:50:17 AM" to "1974-08-05"
        start_date = start_date_str.split(' ')[0]
        end_date = end_date_str.split(' ')[0]

        return {
            'planet_name': planet_name,
            'start_date': start_date,
            'end_date': end_date,
            'original_string': dasha_str
        }

    except Exception as e:
        return None


def get_member_age_from_chart_data(chart_data):
    """
    Calculate member's current age from chart data.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        
    Returns:
        int: Current age in years
    """
    try:
        # Extract birth date from chart data
        birth_date_str = chart_data.get('birth_date')
        if not birth_date_str:
            return None
        
        # Parse birth date
        birth_date = parser.parse(birth_date_str)
        current_date = datetime.now()
        
        # Calculate age
        age = current_date.year - birth_date.year
        
        # Adjust for birthday not yet occurred this year
        if current_date.month < birth_date.month or \
           (current_date.month == birth_date.month and current_date.day < birth_date.day):
            age -= 1
        
        return age
        
    except Exception as e:
        return None


def get_dasha_periods_for_planet(chart_data, planet_name, dasha_type="maha_dasha"):
    """
    Get dasha periods for a specific planet.

    Args:
        chart_data (dict): Chart data from MongoDB
        planet_name (str): Planet name (e.g., 'JUPITER')
        dasha_type (str): Type of dasha ('maha_dasha' or 'bhukti_dasha')

    Returns:
        list: List of dasha periods for the planet
    """
    try:
        dasha_periods = []

        # Navigate to dasha data
        d1_chart = chart_data.get('chart_data', {}).get('D1', {})
        dashas = d1_chart.get('dashas', {})

        # Get the appropriate dasha list
        dasha_list = dashas.get(dasha_type, [])

        for dasha_str in dasha_list:
            # Parse dasha string format: (planet-planet, start_date, end_date)
            parsed_dasha = parse_dasha_string_new_format(dasha_str)

            if parsed_dasha and parsed_dasha['planet_name'].upper() == planet_name.upper():
                dasha_periods.append({
                    'planet_name': planet_name.upper(),
                    'dasha_type': dasha_type,
                    'start_date': parsed_dasha['start_date'],
                    'end_date': parsed_dasha['end_date'],
                    'original_string': dasha_str
                })

        return dasha_periods

    except Exception as e:
        return []


def get_house_ruling_planet_dasha_periods(chart_data, house_number, dasha_type="maha_dasha"):
    """
    Get dasha periods for the ruling planet of a specific house.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        house_number (int): House number (1-12)
        dasha_type (str): Type of dasha
        
    Returns:
        list: List of dasha periods for the house ruling planet
    """
    try:
        # Get the ruling planet for the house
        ruling_planet = get_house_ruling_planet(house_number)
        
        if not ruling_planet:
            return []
        
        # Get dasha periods for the ruling planet
        return get_dasha_periods_for_planet(chart_data, ruling_planet, dasha_type)
        
    except Exception as e:
        return []


def get_planets_with_stars_of_planet(chart_data, star_planet, chart_type="D1"):
    """
    Get all planets that are placed in the nakshatras (stars) ruled by a specific planet.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        star_planet (str): Planet whose stars to check (e.g., 'VENUS')
        chart_type (str): Chart type
        
    Returns:
        list: List of planets in the star planet's nakshatras
    """
    try:
        # This is a simplified implementation
        # In production, this would check actual nakshatra placements
        
        # Get nakshatra lord mapping
        nakshatra_lords = get_nakshatra_lord_mapping()
        
        # Find nakshatras ruled by the star planet
        star_nakshatras = []
        for nakshatra, lord in nakshatra_lords.items():
            if lord.upper() == star_planet.upper():
                star_nakshatras.append(nakshatra)
        
        # Check which planets are in these nakshatras
        planets_in_stars = []
        
        # This would require actual nakshatra calculation from chart data
        # For now, return empty list
        
        return planets_in_stars
        
    except Exception as e:
        return []


def get_planets_in_house_list(chart_data, house_number, chart_type="D1"):
    """
    Get all planets located in a specific house.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        house_number (int): House number (1-12)
        chart_type (str): Chart type
        
    Returns:
        list: List of planet names in the house
    """
    try:
        chart = chart_data.get('chart_data', {}).get(chart_type, {})
        houses = chart.get('houses', [])
        
        for house in houses:
            if house.get('house_number') == house_number:
                planets = house.get('planets', [])
                return [planet.get('planet_name', '').upper() for planet in planets if planet.get('planet_name')]
        
        return []
        
    except Exception as e:
        return []


def get_dasha_periods_for_planets_list(chart_data, planet_list, dasha_type="maha_dasha"):
    """
    Get dasha periods for a list of planets.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        planet_list (list): List of planet names
        dasha_type (str): Type of dasha
        
    Returns:
        list: Combined list of dasha periods for all planets
    """
    try:
        all_dasha_periods = []
        
        for planet in planet_list:
            planet_periods = get_dasha_periods_for_planet(chart_data, planet, dasha_type)
            all_dasha_periods.extend(planet_periods)
        
        # Sort by start date
        all_dasha_periods.sort(key=lambda x: x.get('start_date', ''))
        
        return all_dasha_periods
        
    except Exception as e:
        return []


def check_age_condition(chart_data, min_age, max_age):
    """
    Check if member's current age falls within the specified range.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        min_age (int): Minimum age
        max_age (int): Maximum age
        
    Returns:
        bool: True if age is within range
    """
    try:
        current_age = get_member_age_from_chart_data(chart_data)
        
        if current_age is None:
            return False
        
        return min_age <= current_age <= max_age
        
    except Exception as e:
        return False


def check_dasha_dates_in_prediction_period(dasha_periods, prediction_duration_years=2):
    """
    Check if any dasha periods fall within the prediction duration from current date.
    
    Args:
        dasha_periods (list): List of dasha periods
        prediction_duration_years (int): Prediction duration in years
        
    Returns:
        bool: True if any periods fall within prediction window
    """
    try:
        current_date = datetime.now()
        prediction_end_date = current_date + timedelta(days=prediction_duration_years * 365)
        
        for period in dasha_periods:
            start_date = parser.parse(period.get('start_date', ''))
            end_date = parser.parse(period.get('end_date', ''))
            
            # Check if period overlaps with prediction window
            if start_date <= prediction_end_date and end_date >= current_date:
                return True
        
        return False
        
    except Exception as e:
        return False


def filter_dasha_periods_within_prediction_window(dasha_periods, prediction_duration_years=2):
    """
    Filter dasha periods to only include those that fall within the prediction duration.
    
    Args:
        dasha_periods (list): List of dasha periods
        prediction_duration_years (int): Prediction duration in years
        
    Returns:
        list: Filtered list of dasha periods
    """
    try:
        current_date = datetime.now()
        prediction_end_date = current_date + timedelta(days=prediction_duration_years * 365)
        
        filtered_periods = []
        
        for period in dasha_periods:
            start_date = parser.parse(period.get('start_date', ''))
            end_date = parser.parse(period.get('end_date', ''))
            
            # Check if period overlaps with prediction window
            if start_date <= prediction_end_date and end_date >= current_date:
                filtered_periods.append(period)
        
        return filtered_periods
        
    except Exception as e:
        return []


def parse_and_evaluate_dasha_query(chart_data, query, chart_type="D1", user_profile_id=None, member_profile_id=None):
    """
    Parse and evaluate dasha-based queries with KOCHARAM filter support.

    Args:
        chart_data (dict): Chart data from MongoDB
        query (str): Query string
        chart_type (str): Chart type
        user_profile_id (str): User profile ID
        member_profile_id (str): Member profile ID

    Returns:
        dict: Evaluation results with dasha periods
    """
    try:
        result = {
            'query': query,
            'chart_type': chart_type,
            'success': False,
            'dasha_periods': [],
            'evaluation_results': {}
        }

        # Check if query contains KOCHARAM filter
        has_kocharam = 'KOCHARAM_FILTER' in query

        # Parse different types of dasha queries
        if 'Dasa_Dates' in query or 'Bhukti_Dates' in query or 'dasha' in query.lower():
            dasha_periods = []

            # Determine dasha type
            dasha_type = "maha_dasha"
            if 'Bhukti_Dates' in query:
                dasha_type = "bhukti_dasha"

            # Extract planet or house information from query
            # Check for house ruling planet patterns first (more specific)
            house_match = re.search(r'(\d+)(?:st|nd|rd|th)?_House_Ruling_Planet', query, re.IGNORECASE)
            planet_match = re.search(r'(\w+)\s+(?:Dasa_Dates|Bhukti_Dates)', query, re.IGNORECASE)

            if house_match:
                house_number = int(house_match.group(1))
                dasha_periods = get_house_ruling_planet_dasha_periods(chart_data, house_number, dasha_type)

            elif planet_match and not house_match:
                planet_name = planet_match.group(1).upper()
                dasha_periods = get_dasha_periods_for_planet(chart_data, planet_name, dasha_type)

            # Apply KOCHARAM filter if present
            if has_kocharam and dasha_periods:
                from .kocharam_filter import validate_kocharam_filter, apply_simplified_kocharam_logic

                # Extract KOCHARAM filter part
                kocharam_match = re.search(r'KOCHARAM_FILTER\((.*?)\)', query)
                if kocharam_match:
                    kocharam_condition = kocharam_match.group(1)

                    # Validate and apply KOCHARAM filter
                    validation_result = validate_kocharam_filter(kocharam_condition)
                    if validation_result.get('valid'):
                        kocharam_result = apply_simplified_kocharam_logic(
                            dasha_periods, validation_result, chart_data, user_profile_id, member_profile_id
                        )

                        # Update result with KOCHARAM filtered periods
                        filtered_periods = kocharam_result.get('filtered_dasha_dates', [])
                        result['dasha_periods'] = filtered_periods
                        result['kocharam_analysis'] = kocharam_result.get('kocharam_analysis', {})
                        result['success'] = len(filtered_periods) > 0
                    else:
                        result['error'] = f"Invalid KOCHARAM filter: {validation_result.get('error', 'Unknown error')}"
                        result['dasha_periods'] = dasha_periods
                else:
                    result['error'] = "KOCHARAM_FILTER found but could not extract condition"
                    result['dasha_periods'] = dasha_periods
            else:
                # No KOCHARAM filter, return original dasha periods
                result['dasha_periods'] = dasha_periods
                result['success'] = len(dasha_periods) > 0

        return result

    except Exception as e:
        return {
            'query': query,
            'success': False,
            'error': str(e),
            'dasha_periods': []
        }


# Helper functions

def get_house_ruling_planet(house_number):
    """Get the ruling planet for a specific house (dynamic mapping)."""
    default_ruling_planets = {
        1: 'MARS', 2: 'VENUS', 3: 'MERCURY', 4: 'MOON',
        5: 'SUN', 6: 'MERCURY', 7: 'VENUS', 8: 'MARS',
        9: 'JUPITER', 10: 'SATURN', 11: 'SATURN', 12: 'JUPITER'
    }
    return default_ruling_planets.get(house_number)


def get_nakshatra_lord_mapping():
    """Get nakshatra to lord planet mapping."""
    return {
        'ASHWINI': 'KETU', 'BHARANI': 'VENUS', 'KRITTIKA': 'SUN',
        'ROHINI': 'MOON', 'MRIGASHIRA': 'MARS', 'ARDRA': 'RAHU',
        'PUNARVASU': 'JUPITER', 'PUSHYA': 'SATURN', 'ASHLESHA': 'MERCURY',
        'MAGHA': 'KETU', 'PURVA_PHALGUNI': 'VENUS', 'UTTARA_PHALGUNI': 'SUN',
        'HASTA': 'MOON', 'CHITRA': 'MARS', 'SWATI': 'RAHU',
        'VISHAKHA': 'JUPITER', 'ANURADHA': 'SATURN', 'JYESHTHA': 'MERCURY',
        'MULA': 'KETU', 'PURVA_ASHADHA': 'VENUS', 'UTTARA_ASHADHA': 'SUN',
        'SHRAVANA': 'MOON', 'DHANISHTHA': 'MARS', 'SHATABHISHA': 'RAHU',
        'PURVA_BHADRAPADA': 'JUPITER', 'UTTARA_BHADRAPADA': 'SATURN', 'REVATI': 'MERCURY'
    }
