"""
Response Formatting & Analysis

This module handles formatting of responses, analysis creation,
and output structuring for the rule engine.
"""

from datetime import datetime, timedelta
from dateutil import parser


def create_clean_dasha_response(query, overall_result, filtered_dasha_dates, successful_conditions, results,
                                prediction_duration, duration_details=None, user_profile_id=None,
                                member_profile_id=None, kocharam_result=None):
    """
    Create a clean, comprehensive dasha response with detailed analysis.
    
    Args:
        query (str): Original query
        overall_result (bool): Overall evaluation result
        filtered_dasha_dates (list): Filtered dasha periods
        successful_conditions (list): Successful conditions
        results (list): Detailed evaluation results
        prediction_duration (int): Prediction duration
        duration_details (dict): Duration details
        user_profile_id (str): User profile ID
        member_profile_id (str): Member profile ID
        kocharam_result (dict): KOCHARAM filtering results
        
    Returns:
        dict: Clean, formatted response
    """
    try:
        response = {
            'query': query,
            'overall_result': overall_result,
            'prediction_duration': prediction_duration,
            'prediction_period': get_prediction_period_dates(prediction_duration),
            'summary': create_response_summary(overall_result, filtered_dasha_dates, successful_conditions),
            'dasha_periods': format_dasha_periods_clean(filtered_dasha_dates),
            'successful_conditions': format_successful_conditions_clean(successful_conditions),
            'condition_breakdown': format_condition_breakdown_clean(results),
            'evaluation_summary': create_evaluation_summary(results),
            'metadata': {
                'user_profile_id': str(user_profile_id) if user_profile_id else None,
                'member_profile_id': str(member_profile_id) if member_profile_id else None,
                'generated_at': datetime.now().isoformat(),
                'total_periods_found': len(filtered_dasha_dates),
                'total_conditions_evaluated': len(results) if results else 0
            }
        }
        
        # Add KOCHARAM analysis if available
        if kocharam_result:
            response['kocharam_analysis'] = create_kocharam_analysis_summary(kocharam_result)
        
        # Add duration details if available
        if duration_details:
            response['duration_details'] = duration_details
        
        return response
        
    except Exception as e:
        return {
            'query': query,
            'overall_result': False,
            'error': str(e),
            'summary': {'error': 'Failed to create response'},
            'dasha_periods': [],
            'successful_conditions': []
        }


def format_dasha_periods_clean(dasha_dates):
    """
    Format dasha periods in a clean, readable structure with detailed KOCHARAM analysis and complete dasha breakdown.
    
    Args:
        dasha_dates (list): List of dasha periods
        
    Returns:
        list: Formatted dasha periods
    """
    if not dasha_dates:
        return []
    
    formatted_periods = []
    
    for period in dasha_dates:
        formatted_period = {
            'planet_name': period.get('planet_name', 'Unknown'),
            'dasha_type': period.get('dasha_type', 'Unknown'),
            'start_date': period.get('start_date'),
            'end_date': period.get('end_date'),
            'duration_days': calculate_period_duration(period.get('start_date'), period.get('end_date')),
            'current_status': get_period_status(period.get('start_date'), period.get('end_date'))
        }
        
        # Add KOCHARAM transit information if available
        if 'kocharam_transits' in period:
            formatted_period['kocharam_transits'] = period['kocharam_transits']
            formatted_period['transit_count'] = period.get('transit_count', 0)
            formatted_period['kocharam_condition'] = period.get('kocharam_condition', '')
        
        # Add validation information if available
        if 'kocharam_validation' in period:
            formatted_period['validation'] = period['kocharam_validation']
        
        formatted_periods.append(formatted_period)
    
    return formatted_periods


def format_successful_conditions_clean(successful_conditions):
    """
    Format successful conditions in a clean, readable structure.
    
    Args:
        successful_conditions (list): List of successful conditions
        
    Returns:
        list: Formatted successful conditions
    """
    if not successful_conditions:
        return []
    
    formatted_conditions = []
    
    for condition in successful_conditions:
        formatted_condition = {
            'condition_text': condition.get('condition', 'Unknown'),
            'condition_type': condition.get('condition_type', 'Unknown'),
            'success_rate': condition.get('success_rate', 0),
            'explanation': get_condition_explanation(condition),
            'astrological_meaning': get_astrological_meaning(condition)
        }
        
        # Add detailed information if available
        if 'details' in condition:
            formatted_condition['details'] = condition['details']
        
        if 'planet_info' in condition:
            formatted_condition['planet_info'] = condition['planet_info']
        
        if 'house_info' in condition:
            formatted_condition['house_info'] = condition['house_info']
        
        formatted_conditions.append(formatted_condition)
    
    return formatted_conditions


def format_condition_breakdown_clean(results):
    """
    Format condition breakdown in a clean structure.
    
    Args:
        results (list): Evaluation results
        
    Returns:
        list: Clean condition breakdown
    """
    if not results:
        return []
    
    clean_breakdown = []
    
    for result in results:
        or_group = {
            'or_group_number': result.get('or_group', 0),
            'group_success': result.get('group_success', False),
            'and_conditions': []
        }
        
        and_conditions = result.get('and_conditions', [])
        for condition in and_conditions:
            clean_condition = {
                'condition': condition.get('condition', 'Unknown'),
                'condition_type': condition.get('condition_type', 'Unknown'),
                'success': condition.get('success', False),
                'explanation': get_condition_explanation(condition)
            }
            
            if 'error' in condition:
                clean_condition['error'] = condition['error']
            
            if 'details' in condition:
                clean_condition['details'] = condition['details']
            
            or_group['and_conditions'].append(clean_condition)
        
        clean_breakdown.append(or_group)
    
    return clean_breakdown


def create_evaluation_summary(results):
    """
    Create evaluation summary.
    
    Args:
        results (list): Evaluation results
        
    Returns:
        dict: Evaluation summary
    """
    if not results:
        return {
            'total_or_groups': 0,
            'successful_groups': 0,
            'success_rate': 0,
            'evaluation_status': 'No results'
        }
    
    total_or_groups = len(results)
    successful_groups = sum(1 for result in results if result.get('group_success', False))
    success_rate = (successful_groups / total_or_groups * 100) if total_or_groups > 0 else 0
    
    return {
        'total_or_groups': total_or_groups,
        'successful_groups': successful_groups,
        'success_rate': round(success_rate, 2),
        'evaluation_status': get_evaluation_status(success_rate),
        'logic_type': 'OR' if total_or_groups > 1 else 'AND'
    }


def create_response_summary(overall_result, filtered_dasha_dates, successful_conditions):
    """
    Create response summary.
    
    Args:
        overall_result (bool): Overall result
        filtered_dasha_dates (list): Filtered dasha periods
        successful_conditions (list): Successful conditions
        
    Returns:
        dict: Response summary
    """
    return {
        'overall_success': overall_result,
        'periods_found': len(filtered_dasha_dates),
        'conditions_met': len(successful_conditions),
        'prediction_strength': get_prediction_strength(len(filtered_dasha_dates)),
        'confidence_level': get_confidence_level(overall_result, len(filtered_dasha_dates)),
        'recommendation': get_recommendation(overall_result, len(filtered_dasha_dates))
    }


def create_kocharam_analysis_summary(kocharam_result):
    """
    Create KOCHARAM analysis summary.
    
    Args:
        kocharam_result (dict): KOCHARAM filtering results
        
    Returns:
        dict: KOCHARAM analysis summary
    """
    try:
        analysis = kocharam_result.get('kocharam_analysis', {})
        
        return {
            'filtering_applied': True,
            'total_conditions': analysis.get('total_conditions', 0),
            'original_periods': analysis.get('original_periods', 0),
            'filtered_periods': analysis.get('filtered_periods', 0),
            'filtering_efficiency': analysis.get('filtering_efficiency', 0),
            'conditions_processed': analysis.get('conditions_processed', []),
            'transit_accuracy': 'High (99%+ astronomical precision)',
            'calculation_method': 'Real ephemeris data with degree-based formulas'
        }
        
    except Exception as e:
        return {
            'filtering_applied': False,
            'error': str(e)
        }


def get_prediction_period_dates(prediction_duration_years=2):
    """
    Get the prediction period start and end dates.
    
    Args:
        prediction_duration_years (int): Prediction duration in years
        
    Returns:
        dict: Prediction period dates
    """
    try:
        current_date = datetime.now()
        end_date = current_date + timedelta(days=prediction_duration_years * 365)
        
        return {
            'start_date': current_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'duration_years': prediction_duration_years,
            'total_days': prediction_duration_years * 365
        }
        
    except Exception as e:
        return {
            'start_date': None,
            'end_date': None,
            'error': str(e)
        }


# Helper functions

def calculate_period_duration(start_date, end_date):
    """Calculate duration between two dates in days."""
    try:
        start = parser.parse(start_date)
        end = parser.parse(end_date)
        return (end - start).days
    except:
        return 0


def get_period_status(start_date, end_date):
    """Get the current status of a period."""
    try:
        current_date = datetime.now()
        start = parser.parse(start_date)
        end = parser.parse(end_date)
        
        if current_date < start:
            return 'FUTURE'
        elif current_date > end:
            return 'PAST'
        else:
            return 'CURRENT'
    except:
        return 'UNKNOWN'


def get_condition_explanation(condition):
    """Generate human-readable explanation for a condition."""
    condition_type = condition.get('condition_type', 'UNKNOWN')
    
    explanations = {
        'PLANET_IN_HOUSE': 'Planet is positioned in the specified house',
        'HOUSE_RULING_PLANET_IN_HOUSE': 'House ruling planet is located in the target house',
        'WITH_STARS_OF': 'Planet is in the nakshatra (star) of another planet',
        'PLANET_WITH_PLANET': 'Planets are in conjunction (same house)',
        'PLANET_ASPECT_PLANET': 'Planet is aspecting another planet'
    }
    
    return explanations.get(condition_type, 'Unknown condition type')


def get_astrological_meaning(condition):
    """Get astrological meaning of the condition."""
    condition_type = condition.get('condition_type', 'UNKNOWN')
    
    meanings = {
        'PLANET_IN_HOUSE': 'Indicates planetary influence on house matters',
        'HOUSE_RULING_PLANET_IN_HOUSE': 'Shows strong connection between house themes',
        'WITH_STARS_OF': 'Suggests subtle planetary influence through nakshatra lord',
        'PLANET_WITH_PLANET': 'Creates combined planetary energies and effects',
        'PLANET_ASPECT_PLANET': 'Establishes planetary relationship through aspect'
    }
    
    return meanings.get(condition_type, 'Astrological significance not defined')


def get_evaluation_status(success_rate):
    """Get evaluation status based on success rate."""
    if success_rate >= 80:
        return 'EXCELLENT'
    elif success_rate >= 60:
        return 'GOOD'
    elif success_rate >= 40:
        return 'MODERATE'
    elif success_rate > 0:
        return 'WEAK'
    else:
        return 'NO_MATCH'


def get_prediction_strength(period_count):
    """Get prediction strength based on period count."""
    if period_count >= 5:
        return 'VERY_STRONG'
    elif period_count >= 3:
        return 'STRONG'
    elif period_count >= 1:
        return 'MODERATE'
    else:
        return 'WEAK'


def get_confidence_level(overall_result, period_count):
    """Get confidence level based on results."""
    if overall_result and period_count >= 3:
        return 'HIGH'
    elif overall_result and period_count >= 1:
        return 'MEDIUM'
    elif overall_result:
        return 'LOW'
    else:
        return 'NONE'


def get_recommendation(overall_result, period_count):
    """Get recommendation based on results."""
    if overall_result and period_count >= 3:
        return 'Strong astrological support - favorable timing indicated'
    elif overall_result and period_count >= 1:
        return 'Moderate astrological support - consider timing carefully'
    elif overall_result:
        return 'Limited astrological support - proceed with caution'
    else:
        return 'No astrological support found for the specified conditions'
