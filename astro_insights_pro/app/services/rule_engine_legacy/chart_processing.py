"""
Chart Data Processing & House Systems

This module handles chart data processing, house system mappings,
and chart-related utility functions for the rule engine.
"""

from bson import ObjectId
from ...extensions import mongo


def get_default_house_ruling_planets():
    """
    Get default house ruling planets mapping.
    
    Returns:
        dict: House number to ruling planet mapping
    """
    return {
        1: 'MARS',     # Aries
        2: 'VENUS',    # <PERSON>rus
        3: 'MERCURY',  # <PERSON>
        4: 'MOON',     # <PERSON>
        5: 'SUN',      # <PERSON>
        6: 'MERCURY',  # Virgo
        7: 'VENUS',    # Libra
        8: 'MARS',     # <PERSON><PERSON><PERSON>
        9: 'JUPITER',  # Sagittarius
        10: 'SATURN',  # Capricorn
        11: 'SATURN',  # Aquarius
        12: 'JUPITER'  # Pis<PERSON>
    }


def get_standard_planet_names():
    """
    Get standard planet names mapping.
    
    Returns:
        dict: Planet name variations to standard names
    """
    return {
        'SUN': 'SUN', 'SURYA': 'SUN', 'RAVI': 'SUN',
        'MOON': 'MOON', 'CHANDRA': 'MOON', 'SOM<PERSON>': 'MOON',
        'MARS': 'MARS', 'MANG<PERSON>': 'MARS', 'ANGARAKA': 'MARS',
        'MERCURY': 'MERCURY', 'BUD<PERSON>': 'MERCURY', 'BUDHAN': 'MERCURY',
        'JUPITER': 'JUPITER', 'GURU': 'JUPITER', 'BRIHASPATI': 'JUPITER',
        'VENUS': 'VENUS', 'SHUKRA': 'VENUS', 'SUKRA': 'VENUS',
        'SATURN': 'SATURN', 'SHANI': 'SATURN', 'SANI': 'SATURN',
        'RAHU': 'RAHU', 'NORTH_NODE': 'RAHU',
        'KETU': 'KETU', 'SOUTH_NODE': 'KETU'
    }


def get_dynamic_house_name_ruling_planets():
    """
    Get dynamic house name to ruling planet mapping.
    
    Returns:
        dict: House name to ruling planet mapping
    """
    return {
        'MESHAM': 'MARS', 'ARIES': 'MARS',
        'RISHABAM': 'VENUS', 'TAURUS': 'VENUS',
        'MIDUNAM': 'MERCURY', 'GEMINI': 'MERCURY',
        'KADAGAM': 'MOON', 'CANCER': 'MOON',
        'SIMMAM': 'SUN', 'LEO': 'SUN',
        'KANNI': 'MERCURY', 'VIRGO': 'MERCURY',
        'THULAM': 'VENUS', 'LIBRA': 'VENUS',
        'VIRICHIGAM': 'MARS', 'SCORPIO': 'MARS',
        'DHANUSU': 'JUPITER', 'SAGITTARIUS': 'JUPITER',
        'MAGARAM': 'SATURN', 'CAPRICORN': 'SATURN',
        'KUMBAM': 'SATURN', 'AQUARIUS': 'SATURN',
        'MEENAM': 'JUPITER', 'PISCES': 'JUPITER'
    }


def get_sign_from_longitude(longitude):
    """
    Get sign name from longitude degree.
    
    Args:
        longitude (float): Longitude in degrees (0-360)
        
    Returns:
        str: Sign name
    """
    signs = [
        'MESHAM', 'RISHABAM', 'MIDUNAM', 'KADAGAM', 'SIMMAM', 'KANNI',
        'THULAM', 'VIRICHIGAM', 'DHANUSU', 'MAGARAM', 'KUMBAM', 'MEENAM'
    ]
    
    sign_index = int(longitude // 30) % 12
    return signs[sign_index]


def get_house_name_from_number(house_number, house_to_sign_map):
    """
    Get house name from house number using the user's house system mapping.
    
    Args:
        house_number (int): House number (1-12)
        house_to_sign_map (dict): User's house system mapping
        
    Returns:
        str: House name or default
    """
    return house_to_sign_map.get(house_number, f'House_{house_number}')


def get_full_d1_chart_details(chart_data):
    """
    Extract full D1 chart details from chart data (excluding dasha information).
    
    Args:
        chart_data (dict): Chart data from MongoDB
        
    Returns:
        dict: Complete D1 chart details
    """
    try:
        d1_chart = chart_data.get('chart_data', {}).get('D1', {})
        
        if not d1_chart:
            return {
                'error': 'D1 chart data not found',
                'chart_info': {},
                'houses': [],
                'lagna': {},
                'planets_precise': {}
            }
        
        # Extract chart info
        chart_info = d1_chart.get('chart_info', {
            'name': 'Rasi Chart (D1)',
            'description': 'Main birth chart showing planetary positions at birth',
            'divisional_factor': 1
        })
        
        # Extract houses (excluding dasha data)
        houses = d1_chart.get('houses', [])
        
        # Extract lagna information
        lagna = d1_chart.get('lagna', {})
        
        # Extract precise planetary positions
        planets_precise = d1_chart.get('planets_precise', {})
        
        # Create comprehensive D1 chart details
        d1_details = {
            'chart_info': chart_info,
            'houses': houses,
            'lagna': lagna,
            'planets_precise': planets_precise,
            'total_houses': len(houses),
            'chart_type': 'D1 - Rasi Chart',
            'description': 'Complete D1 chart details including all houses, planetary positions, degrees, nakshatras, and padas'
        }
        
        return d1_details
        
    except Exception as e:
        return {
            'error': f'Error extracting D1 chart details: {str(e)}',
            'chart_info': {},
            'houses': [],
            'lagna': {},
            'planets_precise': {}
        }


def get_user_house_system(chart_data):
    """
    Get user's house system mapping from chart data.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        
    Returns:
        dict: House number to house name mapping
    """
    try:
        d1_chart = chart_data.get('chart_data', {}).get('D1', {})
        houses = d1_chart.get('houses', [])
        
        house_system = {}
        for house in houses:
            house_number = house.get('house_number')
            house_name = house.get('house_name')
            if house_number and house_name:
                house_system[house_number] = house_name
                
        return house_system
    except Exception as e:
        return {}


def get_chart_data(user_profile_id, member_profile_id):
    """
    Get chart data for a specific user and member profile.
    
    Args:
        user_profile_id (str): User profile ID
        member_profile_id (str): Member profile ID
        
    Returns:
        dict: Chart data from MongoDB
    """
    try:
        # Convert string IDs to ObjectId if needed
        if isinstance(user_profile_id, str):
            user_profile_id = ObjectId(user_profile_id)
        if isinstance(member_profile_id, str):
            member_profile_id = ObjectId(member_profile_id)
            
        # Query MongoDB for chart data
        chart_data = mongo.db.user_member_astro_profile_data.find_one({
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id
        })
        
        if not chart_data:
            return {
                'error': f'Chart data not found for user {user_profile_id}, member {member_profile_id}',
                'chart_data': {}
            }
            
        return chart_data
        
    except Exception as e:
        return {
            'error': f'Error retrieving chart data: {str(e)}',
            'chart_data': {}
        }


def get_planet_house_mapping(chart_data, chart_type="D1"):
    """
    Extract planet to house mapping from chart data.
    
    Args:
        chart_data (dict): Chart data from MongoDB
        chart_type (str): Chart type (default: "D1")
        
    Returns:
        dict: Planet name to house number mapping
    """
    try:
        chart = chart_data.get('chart_data', {}).get(chart_type, {})
        houses = chart.get('houses', [])
        
        planet_house_mapping = {}
        
        for house in houses:
            house_number = house.get('house_number')
            planets = house.get('planets', [])
            
            for planet in planets:
                planet_name = planet.get('planet_name', '').upper()
                if planet_name and house_number:
                    planet_house_mapping[planet_name] = house_number
                    
        return planet_house_mapping
        
    except Exception as e:
        return {}


def normalize_planet_name(planet_name):
    """
    Normalize planet names to standard uppercase format.
    
    Args:
        planet_name (str): Planet name in any format
        
    Returns:
        str: Standardized planet name
    """
    if not planet_name:
        return ''
        
    # Convert to uppercase and strip whitespace
    normalized = planet_name.upper().strip()
    
    # Apply standard name mapping
    standard_names = get_standard_planet_names()
    return standard_names.get(normalized, normalized)


def map_planets_by_signs(planets_precise, lagna_sign):
    """
    Map planets to houses based on their signs and lagna sign.
    
    Args:
        planets_precise (dict): Precise planetary positions
        lagna_sign (str): Lagna sign name
        
    Returns:
        dict: Planet to house mapping
    """
    try:
        signs = [
            'MESHAM', 'RISHABAM', 'MIDUNAM', 'KADAGAM', 'SIMMAM', 'KANNI',
            'THULAM', 'VIRICHIGAM', 'DHANUSU', 'MAGARAM', 'KUMBAM', 'MEENAM'
        ]
        
        # Find lagna sign index
        lagna_index = 0
        for i, sign in enumerate(signs):
            if sign.upper() == lagna_sign.upper():
                lagna_index = i
                break
                
        planet_house_mapping = {}
        
        for planet_name, planet_data in planets_precise.items():
            planet_sign = planet_data.get('sign', '').upper()
            
            # Find planet sign index
            planet_sign_index = 0
            for i, sign in enumerate(signs):
                if sign.upper() == planet_sign:
                    planet_sign_index = i
                    break
                    
            # Calculate house number (1-based)
            house_number = ((planet_sign_index - lagna_index) % 12) + 1
            planet_house_mapping[planet_name.upper()] = house_number
            
        return planet_house_mapping
        
    except Exception as e:
        return {}


def debug_chart_structure(chart_data, chart_type="D1"):
    """
    Debug function to analyze chart data structure.
    
    Args:
        chart_data (dict): Chart data
        chart_type (str): Chart type
        
    Returns:
        dict: Debug information
    """
    try:
        debug_info = {
            'chart_type': chart_type,
            'has_chart_data': 'chart_data' in chart_data,
            'available_charts': list(chart_data.get('chart_data', {}).keys()),
            'structure_analysis': {}
        }
        
        if chart_type in chart_data.get('chart_data', {}):
            chart = chart_data['chart_data'][chart_type]
            debug_info['structure_analysis'] = {
                'has_houses': 'houses' in chart,
                'house_count': len(chart.get('houses', [])),
                'has_lagna': 'lagna' in chart,
                'has_planets_precise': 'planets_precise' in chart,
                'planet_count': len(chart.get('planets_precise', {}))
            }
            
        return debug_info
        
    except Exception as e:
        return {'error': str(e)}
