"""
<PERSON>ript to load default astrological data from Excel and store it in MongoDB.
This script should be run once to initialize the database with default values.
"""

import os
import pandas as pd
from bson import ObjectId
from datetime import datetime
from fortune_lens.app.extensions import mongo

# Path to the Excel file
EXCEL_FILE_PATH = '/Users/<USER>/PycharmProjects/fortune_lens/FortuneLens/FL Medicine Use Case v1.xlsx'

def read_excel_sheets(file_name=EXCEL_FILE_PATH):
    """
    Read data from Excel sheets.
    
    Args:
        file_name (str): Path to the Excel file
        
    Returns:
        dict: Dictionary containing DataFrames from Excel sheets
    """
    try:
        house_name = pd.read_excel(file_name, sheet_name="Master Lookup", usecols='C:D', skiprows=5, nrows=13)

        planets_exalt_debilitate = pd.read_excel(
            file_name,
            sheet_name="Master Lookup",
            usecols='F:H',
            skiprows=6,
            nrows=10
        )

        star = pd.read_excel(
            file_name,
            sheet_name="Master Lookup",
            usecols='B:D',
            skiprows=19,
            nrows=27
        )

        planets_friends_neutral_enemies = pd.read_excel(
            file_name,
            sheet_name="Master Lookup",
            usecols='F:I',
            skiprows=20,
            nrows=9
        )

        planets_aspects = pd.read_excel(
            file_name,
            sheet_name="Master Lookup",
            usecols='F:G',
            skiprows=32,
            nrows=7
        )

        return {
            "house_name": house_name,
            "planets_exalt_debilitate": planets_exalt_debilitate,
            "star": star,
            "planets_friends_neutral_enemies": planets_friends_neutral_enemies,
            "planets_aspects": planets_aspects
        }
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return {}

def store_in_mongodb():
    """
    Store the data from Excel in MongoDB collections.
    """
    # Read data from Excel
    dataframes = read_excel_sheets()
    
    if not dataframes:
        print("Failed to read data from Excel.")
        return
    
    # Store house_name data
    house_name_data = dataframes["house_name"].to_dict('records')
    store_collection_data("astro_house_names", house_name_data)
    
    # Store planets_exalt_debilitate data
    planets_exalt_debilitate_data = dataframes["planets_exalt_debilitate"].to_dict('records')
    store_collection_data("astro_planets_exalt_debilitate", planets_exalt_debilitate_data)
    
    # Store star data
    star_data = dataframes["star"].to_dict('records')
    store_collection_data("astro_stars", star_data)
    
    # Store planets_friends_neutral_enemies data
    planets_friends_neutral_enemies_data = dataframes["planets_friends_neutral_enemies"].to_dict('records')
    store_collection_data("astro_planets_relationships", planets_friends_neutral_enemies_data)
    
    # Store planets_aspects data
    planets_aspects_data = dataframes["planets_aspects"].to_dict('records')
    store_collection_data("astro_planets_aspects", planets_aspects_data)
    
    print("All data stored in MongoDB successfully.")

def store_collection_data(collection_name, data):
    """
    Store data in a MongoDB collection.
    
    Args:
        collection_name (str): Name of the collection
        data (list): List of dictionaries to store
    """
    # Check if collection exists
    if collection_name in mongo.db.list_collection_names():
        # Drop the collection if it exists
        mongo.db[collection_name].drop()
    
    # Add created_at and updated_at fields
    now = datetime.utcnow()
    for item in data:
        item["created_at"] = now
        item["updated_at"] = now
    
    # Insert data
    if data:
        mongo.db[collection_name].insert_many(data)
        print(f"Stored {len(data)} records in {collection_name} collection.")
    else:
        print(f"No data to store in {collection_name} collection.")

if __name__ == "__main__":
    store_in_mongodb()
