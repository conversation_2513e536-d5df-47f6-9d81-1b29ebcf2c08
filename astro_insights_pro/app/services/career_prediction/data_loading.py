"""
Data loading module for the medical profession prediction service.
This module contains functions to load data from Excel files.
"""

import pandas as pd
from pandas import DataFrame
import os

# Path to the Excel file
EXCEL_FILE_PATH = '/Users/<USER>/PycharmProjects/fortune_lens/FortuneLens/FL Medicine Use Case v1.xlsx'

def read_excel_sheets(file_name=EXCEL_FILE_PATH):
    """
    Read data from Excel sheets.

    Args:
        file_name (str): Path to the Excel file

    Returns:
        dict: Dictionary containing DataFrames from Excel sheets
    """
    try:
        house_name = pd.read_excel(file_name, sheet_name="Master Lookup", usecols='C:D', skiprows=5, nrows=13)

        Planets_exalt_debilitate: DataFrame = pd.read_excel(
            file_name,
            sheet_name="Master Lookup",
            usecols='F:H',
            skiprows=6,
            nrows=10
        )

        star: DataFrame = pd.read_excel(
            file_name,
            sheet_name="Master Lookup",
            usecols='B:D',
            skiprows=19,
            nrows=27
        )

        planets_friends_neutral_enemies: DataFrame = pd.read_excel(
            file_name,
            sheet_name="Master Lookup",
            usecols='F:I',
            skiprows=20,
            nrows=9
        )

        planets_aspects: DataFrame = pd.read_excel(
            file_name,
            sheet_name="Master Lookup",
            usecols='F:G',
            skiprows=32,
            nrows=7
        )

        return {
            "house_name": house_name,
            "Planets_exalt_debilitate": Planets_exalt_debilitate,
            "star": star,
            "planets_friends_neutral_enemies": planets_friends_neutral_enemies,
            "planets_aspects": planets_aspects
        }
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return {}

# Load the dataframes
dataframes = read_excel_sheets()
