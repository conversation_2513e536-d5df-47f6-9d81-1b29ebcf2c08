"""
New Sequence Generator Service

This service provides sequential IDs without using the sequences collection.
It uses in-memory counters that start from the maximum existing values in the database.
"""

from bson import ObjectId
from ..extensions import mongo
from ..config import BaseConfiguration

class NewSequenceService:
    """Service for generating sequential IDs without using the sequences collection"""
    
    # Initialize counters with None (will be set on first use)
    _user_profile_id_counter = None
    _member_profile_id_counter = None
    
    @classmethod
    def _initialize_counters(cls):
        """Initialize counters from the maximum values in the database"""
        if cls._user_profile_id_counter is None:
            # Find maximum user_profile_id
            max_user = mongo.db[BaseConfiguration.MONGO_USER_COLLECTION].find_one(
                sort=[("user_profile_id", -1)]
            )
            cls._user_profile_id_counter = max_user.get('user_profile_id', 0) if max_user else 0
        
        if cls._member_profile_id_counter is None:
            # Find maximum member_profile_id
            max_member = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one(
                sort=[("member_profile_id", -1)]
            )
            cls._member_profile_id_counter = max_member.get('member_profile_id', 0) if max_member else 0
    
    @classmethod
    def get_next_user_profile_id(cls):
        """
        Get the next user profile ID
        
        Returns:
            int: Next user profile ID
        """
        cls._initialize_counters()
        cls._user_profile_id_counter += 1
        return cls._user_profile_id_counter
    
    @classmethod
    def get_next_member_profile_id(cls):
        """
        Get the next member profile ID
        
        Returns:
            int: Next member profile ID
        """
        cls._initialize_counters()
        cls._member_profile_id_counter += 1
        return cls._member_profile_id_counter
    
    @staticmethod
    def get_new_object_id():
        """
        Get a new ObjectId
        
        Returns:
            ObjectId: New ObjectId
        """
        return ObjectId()
