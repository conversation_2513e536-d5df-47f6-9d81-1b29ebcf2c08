"""
Marriage Date Prediction API

This module provides API endpoints for predicting potential marriage dates
based on astrological factors.
"""

from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
import json
from bson import ObjectId

from . import api_bp
from ..services.marriage_matching.date_prediction import predict_marriage_dates
from ..errors.exceptions import ValidationError, ResourceNotFoundError
from ..extensions import mongo
from ..config import BaseConfiguration


@api_bp.route('/marriage-date-prediction', methods=['POST'])
@jwt_required()
def predict_marriage_date():
    """
    Predict potential marriage dates for a member
    ---
    tags:
      - Marriage Date Prediction
    security:
      - bearerAuth: []
    parameters:
      - in: body
        name: body
        schema:
          type: object
          required:
            - member_id
          properties:
            member_id:
              type: string
              description: ID of the member profile
            start_age:
              type: integer
              description: Starting age for prediction (default 21)
            end_age:
              type: integer
              description: Ending age for prediction (default 40)
            marriage_date:
              type: string
              description: Optional marriage date for validation (format YYYY-MM-DD)
            print_output:
              type: boolean
              description: Whether to print detailed output (default false)
    responses:
      200:
        description: Marriage date prediction results
      400:
        description: Validation error
      401:
        description: Unauthorized
      404:
        description: Member profile not found
    """
    data = request.get_json() or {}

    # Validate input
    if 'member_id' not in data:
        raise ValidationError({'member_id': 'Member ID is required'})

    # Get optional parameters
    start_age = data.get('start_age', 21)
    end_age = data.get('end_age', 40)
    marriage_date = data.get('marriage_date')
    print_output = data.get('print_output', False)

    # Predict marriage dates
    result = predict_marriage_dates(
        data['member_id'],
        start_age=start_age,
        end_age=end_age,
        marriage_date=marriage_date,
        print_output=print_output
    )

    if not result['success']:
        raise ResourceNotFoundError(result['message'])

    return jsonify(result)


@api_bp.route('/marriage-date-prediction/<member_id>', methods=['GET'])
@jwt_required()
def get_marriage_date_prediction(member_id):
    """
    Get marriage date prediction for a member
    ---
    tags:
      - Marriage Date Prediction
    security:
      - bearerAuth: []
    parameters:
      - in: path
        name: member_id
        required: true
        schema:
          type: string
        description: ID of the member profile
      - in: query
        name: start_age
        schema:
          type: integer
        description: Starting age for prediction (default 21)
      - in: query
        name: end_age
        schema:
          type: integer
        description: Ending age for prediction (default 40)
      - in: query
        name: marriage_date
        schema:
          type: string
        description: Optional marriage date for validation (format YYYY-MM-DD)
      - in: query
        name: print_output
        schema:
          type: boolean
        description: Whether to print detailed output (default false)
    responses:
      200:
        description: Marriage date prediction results
      401:
        description: Unauthorized
      404:
        description: Member profile not found
    """
    # Get optional parameters from query string
    start_age = request.args.get('start_age', 21, type=int)
    end_age = request.args.get('end_age', 40, type=int)
    marriage_date = request.args.get('marriage_date')
    print_output = request.args.get('print_output', 'false').lower() == 'true'

    # Predict marriage dates
    result = predict_marriage_dates(
        member_id,
        start_age=start_age,
        end_age=end_age,
        marriage_date=marriage_date,
        print_output=print_output
    )

    if not result['success']:
        raise ResourceNotFoundError(result['message'])

    return jsonify(result)


@api_bp.route('/marriage-date-prediction/user/<user_profile_id>/<member_profile_id>', methods=['GET'])
@jwt_required()
def get_marriage_date_prediction_by_user(user_profile_id, member_profile_id):
    """
    Get marriage date prediction for a member of a user
    ---
    tags:
      - Marriage Date Prediction
    security:
      - bearerAuth: []
    parameters:
      - in: path
        name: user_profile_id
        required: true
        schema:
          type: string
        description: ID of the user profile (can be ObjectId or user_profile_id)
      - in: path
        name: member_profile_id
        required: true
        schema:
          type: string
        description: member_profile_id of the member
      - in: query
        name: start_age
        schema:
          type: integer
        description: Starting age for prediction (default 21)
      - in: query
        name: end_age
        schema:
          type: integer
        description: Ending age for prediction (default 40)
      - in: query
        name: marriage_date
        schema:
          type: string
        description: Optional marriage date for validation (format YYYY-MM-DD)
      - in: query
        name: print_output
        schema:
          type: boolean
        description: Whether to print detailed output (default false)
    responses:
      200:
        description: Marriage date prediction results
      401:
        description: Unauthorized
      404:
        description: Member profile not found
    """
    # Get optional parameters from query string
    start_age = request.args.get('start_age', 21, type=int)
    end_age = request.args.get('end_age', 40, type=int)
    marriage_date = request.args.get('marriage_date')
    print_output = request.args.get('print_output', 'false').lower() == 'true'

    # Get user profile
    try:
        if ObjectId.is_valid(user_profile_id):
            user_profile = mongo.db[BaseConfiguration.MONGO_USER_COLLECTION].find_one({"_id": ObjectId(user_profile_id)})
        else:
            user_profile = mongo.db[BaseConfiguration.MONGO_USER_COLLECTION].find_one({"user_profile_id": int(user_profile_id)})
    except:
        user_profile = None

    if not user_profile:
        raise ResourceNotFoundError(f"User profile with ID {user_profile_id} not found")

    # Get member profile
    try:
        member_profile = mongo.db[BaseConfiguration.MONGO_MEMBER_PROFILE_COLLECTION].find_one({
            "user_profile_id": user_profile.get("user_profile_id"),
            "member_profile_id": int(member_profile_id)
        })
    except:
        member_profile = None

    if not member_profile:
        raise ResourceNotFoundError(f"Member profile with ID {member_profile_id} not found for user {user_profile_id}")

    # Predict marriage dates
    result = predict_marriage_dates(
        str(member_profile["_id"]),
        start_age=start_age,
        end_age=end_age,
        marriage_date=marriage_date,
        print_output=print_output
    )

    if not result['success']:
        raise ResourceNotFoundError(result['message'])

    return jsonify(result)
