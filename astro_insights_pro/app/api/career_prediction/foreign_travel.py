"""
Foreign Travel Prediction API

This module provides API endpoints for foreign travel prediction based on astrological factors.
"""

from flask import request, jsonify
from bson import ObjectId
import json
from . import career_prediction_bp
from ...services.career_prediction.foreign_travel import predict_foreign_travel


@career_prediction_bp.route('/foreign-travel', methods=['POST'])
def foreign_travel():
    """
    Predict potential foreign travel periods based on astrological factors.

    Request JSON:
    {
        "user_profile_id": "user_profile_id",
        "member_profile_id": "member_profile_id",
        "foreign_travel_date": "YYYY-MM-DD" (optional),
        "print_output": true/false (optional)
    }

    Response JSON:
    {
        "success": true/false,
        "user_profile_id": "user_profile_id",
        "member_profile_id": "member_profile_id",
        "foreign_travel_date": "YYYY-MM-DD",
        "chart_path": "path/to/chart.png",
        "rule_results": {
            "Rule 3.1": true/false,
            "Rule 3.1_count": 0,
            "Rule 3.2": true/false,
            "Rule 3.2_count": 0,
            "Rule 3.3": true/false,
            "Rule 3.3_count": 0
        },
        "overall_percentage": 0.0,
        "foreign_travel_potential": "Low/Medium/High",
        "detailed_results": {...} (if print_output=true)
    }
    """
    try:
        data = request.get_json()

        # Validate input
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400

        # Extract parameters
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        foreign_travel_date = data.get('foreign_travel_date')
        print_output = data.get('print_output', False)

        # Validate required parameters
        if not user_profile_id or not member_profile_id:
            return jsonify({
                'success': False,
                'message': 'User profile ID and member profile ID are required'
            }), 400

        # Special handling for user_profile_id 1 and self member (equivalent to user_id 100001 in Excel)
        if user_profile_id == '1' and member_profile_id == '1':
            print("Processing special case for user_profile_id 1 and self member")
            # We'll use MongoDB data instead of Excel data
            use_excel_data = False
        else:
            use_excel_data = False

        # Call the prediction function with user_profile_id and member_profile_id
        result = predict_foreign_travel(
            user_profile_id=user_profile_id,
            member_profile_id=member_profile_id,
            foreign_travel_date=foreign_travel_date,
            print_output=print_output,
            use_excel_data=use_excel_data
        )

        if not result.get('success', False):
            return jsonify(result), 404

        return jsonify(result), 200

    except Exception as e:
        import traceback
        print(f"Error in foreign travel API: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Error in foreign travel API: {str(e)}'
        }), 500
