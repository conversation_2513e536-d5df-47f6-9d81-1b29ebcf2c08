# API Directory Structure

This directory contains all the API endpoints for the Fortune Lens application. It is organized into subdirectories based on functionality:

## Directory Structure

```
api/
├── __init__.py                 # Main API blueprint registration
├── auth/                       # Authentication endpoints
│   ├── __init__.py             # Auth blueprint initialization
│   └── routes.py               # Authentication routes (register, login, etc.)
├── profiles/                   # User and member profile endpoints
│   ├── __init__.py             # Profiles blueprint initialization
│   └── routes.py               # Profile management routes
└── astrology/                  # Astrological features
    ├── __init__.py             # Astrology blueprint initialization
    ├── charts.py               # Chart generation endpoints
    ├── marriage_matching.py    # Marriage compatibility endpoints
    └── panchanga.py            # Panchanga calculation endpoints
```

## Blueprint Registration

All blueprints are registered in the main `__init__.py` file with appropriate URL prefixes:

- `/api/auth` - Authentication endpoints
- `/api/profiles` - User and member profile endpoints
- `/api/astrology` - Astrological feature endpoints

## API Endpoints

For a complete list of all API endpoints, see the [API_ENDPOINTS.md](../../API_ENDPOINTS.md) file in the project root directory.
