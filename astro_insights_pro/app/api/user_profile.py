"""
User Profile API

This module handles user registration, authentication, and profile management.
"""

from flask import request, jsonify
from flask_jwt_extended import (
    create_access_token,
    create_refresh_token,
    jwt_required,
    get_jwt_identity
)
from bson import ObjectId

from . import api_bp
from ..services.auth_service import AuthService
from ..services.user_service import UserService
from ..services.otp_service import OTPService
from ..schemas.auth_schemas import validate_login_input, validate_register_input
from ..errors.exceptions import ValidationError, AuthenticationError, ResourceNotFoundError, AuthorizationError
from ..constants import ApiRoute, HttpMethod, ResponseMessage, OtpConfig, Field, StatusCode
from ..extensions import mongo


# ===== Authentication Endpoints =====

@api_bp.route(ApiRoute.AUTH_REGISTER, methods=[HttpMethod.POST])
def register():
    """
    Register a new user
    ---
    tags:
      - User Profile
    parameters:
      - in: body
        name: body
        schema:
          type: object
          required:
            - email
            - password
            - name
          properties:
            email:
              type: string
            password:
              type: string
            name:
              type: string
            mobile:
              type: string
            otp:
              type: string
              description: OTP for email verification
    responses:
      201:
        description: User registered successfully
      202:
        description: Registration initiated, OTP sent
      400:
        description: Validation error
    """
    data = request.get_json() or {}

    # Validate input
    errors = validate_register_input(data)
    if errors:
        raise ValidationError(errors)

    # Check if user already exists
    if UserService.get_user_by_email(data['email']):
        raise ValidationError({Field.EMAIL: ResponseMessage.EMAIL_ALREADY_REGISTERED})

    # Check if mobile number is already registered
    if 'mobile' in data and data['mobile'] and UserService.get_user_by_mobile(data['mobile']):
        raise ValidationError({Field.MOBILE: ResponseMessage.MOBILE_ALREADY_REGISTERED})

    # If OTP is provided, verify it and complete registration
    if 'otp' in data and data['otp']:
        # Verify OTP for email
        if not OTPService.verify_otp(
            data['otp'],
            email=data['email'],
            otp_type=OtpConfig.TYPE_REGISTRATION
        ):
            raise ValidationError({Field.OTP: ResponseMessage.INVALID_OTP})

        # OTP verified, create user
        user = UserService.create_user(
            email=data['email'],
            password=data['password'],
            name=data['name'],
            mobile=data.get('mobile')
        )

        # Generate tokens
        # Convert ID to string for JWT
        user_id_str = str(user['_id'])
        access_token = create_access_token(identity=user_id_str)
        refresh_token = create_refresh_token(identity=user_id_str)

        return jsonify({
            'message': ResponseMessage.USER_REGISTERED,
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': {
                'id': str(user['_id']),
                'user_profile_id': str(user.get(Field.USER_PROFILE_ID, user['_id'])),
                'email': user['email'],
                'name': user['name'],
                'mobile': user.get('mobile')
            }
        }), StatusCode.CREATED
    else:
        # No OTP provided, send OTP to email
        try:
            # Generate and send OTP
            otp_data = OTPService.save_otp(email=data['email'], otp_type=OtpConfig.TYPE_REGISTRATION)
            OTPService.send_email_otp(data['email'], otp_data[Field.OTP])

            return jsonify({
                'message': ResponseMessage.OTP_SENT,
                'email': data['email'],
                'expires_at': otp_data[Field.EXPIRES_AT],
                'registration_data': {
                    'email': data['email'],
                    'name': data['name'],
                    'mobile': data.get('mobile')
                    # Don't include password in response
                }
            }), StatusCode.ACCEPTED
        except Exception as e:
            return jsonify({
                'error': str(e),
                'status': 'error'
            }), StatusCode.INTERNAL_SERVER_ERROR


@api_bp.route(ApiRoute.AUTH_LOGIN, methods=[HttpMethod.POST])
def login():
    """
    User login
    ---
    tags:
      - User Profile
    parameters:
      - in: body
        name: body
        schema:
          type: object
          required:
            - email
            - password
          properties:
            email:
              type: string
            password:
              type: string
    responses:
      200:
        description: Login successful
      400:
        description: Validation error
      401:
        description: Authentication failed
    """
    data = request.get_json() or {}

    # Validate input
    errors = validate_login_input(data)
    if errors:
        raise ValidationError(errors)

    # Authenticate user
    user = AuthService.authenticate_user(data['email'], data['password'])
    if not user:
        raise AuthenticationError(ResponseMessage.INVALID_CREDENTIALS)

    # Generate tokens
    # Convert ID to string for JWT
    user_id_str = str(user['_id'])
    access_token = create_access_token(identity=user_id_str)
    refresh_token = create_refresh_token(identity=user_id_str)

    return jsonify({
        'message': ResponseMessage.LOGIN_SUCCESS,
        'access_token': access_token,
        'refresh_token': refresh_token,
        'user': {
            'id': str(user['_id']),
            'email': user['email'],
            'name': user['name'],
            'mobile': user.get('mobile')
        }
    })


@api_bp.route(ApiRoute.AUTH_REFRESH, methods=[HttpMethod.POST])
@jwt_required(refresh=True)
def refresh():
    """
    Refresh access token
    ---
    tags:
      - User Profile
    security:
      - bearerAuth: []
    responses:
      200:
        description: Token refreshed successfully
      401:
        description: Invalid token
    """
    current_user = get_jwt_identity()
    access_token = create_access_token(identity=current_user)

    return jsonify({
        'access_token': access_token
    })


@api_bp.route(ApiRoute.AUTH_ME, methods=[HttpMethod.GET])
@jwt_required()
def get_user_info():
    """
    Get current user info
    ---
    tags:
      - User Profile
    security:
      - bearerAuth: []
    responses:
      200:
        description: User info retrieved successfully
      401:
        description: Invalid token
    """
    current_user_id = get_jwt_identity()
    user = UserService.get_user_by_id(current_user_id)

    if not user:
        raise AuthenticationError(ResponseMessage.USER_NOT_FOUND)

    return jsonify({
        'user': {
            'id': str(user['_id']),
            'user_profile_id': str(user.get(Field.USER_PROFILE_ID, user['_id'])),
            'email': user['email'],
            'name': user['name'],
            'mobile': user.get('mobile')
        }
    })


# ===== User Management Endpoints =====

@api_bp.route('/users', methods=['GET'])
@jwt_required()
def get_users():
    """
    Get all users (admin only)
    ---
    tags:
      - User Profile
    security:
      - bearerAuth: []
    parameters:
      - in: query
        name: page
        schema:
          type: integer
          default: 1
      - in: query
        name: per_page
        schema:
          type: integer
          default: 10
    responses:
      200:
        description: List of users
      401:
        description: Unauthorized
    """
    # Get query parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)

    # Get users
    users, total = UserService.get_users(page, per_page)

    # Calculate pagination info
    total_pages = (total + per_page - 1) // per_page

    return jsonify({
        'users': users,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': total_pages
        }
    })


@api_bp.route('/users/<user_id>', methods=['GET'])
@jwt_required()
def get_user(user_id):
    """
    Get user by ID
    ---
    tags:
      - User Profile
    security:
      - bearerAuth: []
    parameters:
      - in: path
        name: user_id
        required: true
        schema:
          type: string
    responses:
      200:
        description: User details
      404:
        description: User not found
    """
    user = UserService.get_user_by_id(user_id)

    if not user:
        raise ResourceNotFoundError('User not found')

    # Remove password from response
    if 'password' in user:
        del user['password']

    return jsonify({
        'user': user
    })


@api_bp.route('/users/<user_id>', methods=['PUT'])
@jwt_required()
def update_user(user_id):
    """
    Update user
    ---
    tags:
      - User Profile
    security:
      - bearerAuth: []
    parameters:
      - in: path
        name: user_id
        required: true
        schema:
          type: string
      - in: body
        name: body
        schema:
          type: object
          properties:
            name:
              type: string
            email:
              type: string
            mobile:
              type: string
    responses:
      200:
        description: User updated successfully
      400:
        description: Validation error
      401:
        description: Unauthorized
      404:
        description: User not found
    """
    # Check if user exists
    user = UserService.get_user_by_id(user_id)
    if not user:
        raise ResourceNotFoundError('User not found')

    # Check if current user is updating their own profile
    current_user_id = get_jwt_identity()
    if str(user['_id']) != current_user_id:
        raise AuthorizationError()

    # Update user
    data = request.get_json()
    updated_user = UserService.update_user(user_id, data)

    return jsonify({
        'message': 'User updated successfully',
        'user': updated_user
    })


@api_bp.route('/users/<user_id>', methods=['DELETE'])
@jwt_required()
def delete_user(user_id):
    """
    Delete user
    ---
    tags:
      - User Profile
    security:
      - bearerAuth: []
    parameters:
      - in: path
        name: user_id
        required: true
        schema:
          type: string
    responses:
      200:
        description: User deleted successfully
      401:
        description: Unauthorized
      403:
        description: Forbidden
      404:
        description: User not found
    """
    # Check if user exists
    user = UserService.get_user_by_id(user_id)
    if not user:
        raise ResourceNotFoundError('User not found')

    # Check if current user is deleting their own profile
    current_user_id = get_jwt_identity()
    if str(user['_id']) != current_user_id:
        raise AuthorizationError()

    # Delete user
    success = UserService.delete_user(user_id)

    if not success:
        return jsonify({
            'error': 'Failed to delete user'
        }), 500

    return jsonify({
        'message': 'User deleted successfully'
    })
