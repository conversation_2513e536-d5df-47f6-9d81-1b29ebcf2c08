"""
Member Profiles API
"""

from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId
import re

from . import api_bp
from ..services.member_profile_service import MemberProfileService
from ..schemas.member_profile_schemas import validate_member_profile_input
from ..errors.exceptions import ValidationError, ResourceNotFoundError, AuthorizationError
from ..constants import Field


@api_bp.route('/member-profiles', methods=['POST'])
@jwt_required()
def create_member_profile():
    """
    Create member profile
    ---
    tags:
      - Member Profiles
    security:
      - bearerAuth: []
    parameters:
      - in: body
        name: body
        schema:
          type: object
          required:
            - name
            - relation
          properties:
            name:
              type: string
            relation:
              type: string
              description: Relationship to the user (e.g., self, spouse, child, parent)
            birth_date:
              type: string
              format: date
            birth_time:
              type: string
              format: time
            birth_place:
              type: string
            latitude:
              type: number
            longitude:
              type: number
            gender:
              type: string
              enum: [male, female, other]
    responses:
      201:
        description: Profile created successfully
      400:
        description: Validation error
      401:
        description: Unauthorized
    """
    data = request.get_json() or {}

    # Validate input
    errors = validate_member_profile_input(data)
    if errors:
        raise ValidationError(errors)

    # Get current user ID
    user_id = get_jwt_identity()

    # Create profile
    profile = MemberProfileService.create_member_profile(user_id, data)

    return jsonify({
        'message': 'Member profile created successfully',
        'profile': profile
    }), 201


@api_bp.route('/member-profiles/<profile_id>', methods=['GET'])
@jwt_required()
def get_member_profile(profile_id):
    """
    Get member profile by ID
    ---
    tags:
      - Member Profiles
    security:
      - bearerAuth: []
    parameters:
      - in: path
        name: profile_id
        required: true
        schema:
          type: string
    responses:
      200:
        description: Profile details
      404:
        description: Profile not found
    """
    # Try to convert profile_id to int if it's a digit string
    if re.match(r'^\d+$', profile_id):
        profile_id = int(profile_id)

    profile = MemberProfileService.get_member_profile_by_id(profile_id)

    if not profile:
        raise ResourceNotFoundError('Member profile')

    # Check if current user owns the profile
    current_user_id = get_jwt_identity()
    if str(profile['user_profile_id']) != current_user_id:
        raise AuthorizationError()

    # Ensure member_profile_id is included in the response
    if Field.MEMBER_PROFILE_ID not in profile:
        profile[Field.MEMBER_PROFILE_ID] = profile['_id']

    return jsonify({
        'profile': profile
    })


@api_bp.route('/member-profiles', methods=['GET'])
@jwt_required()
def get_my_member_profiles():
    """
    Get current user's member profiles
    ---
    tags:
      - Member Profiles
    security:
      - bearerAuth: []
    responses:
      200:
        description: List of user's member profiles
      401:
        description: Unauthorized
    """
    user_id = get_jwt_identity()
    profiles = MemberProfileService.get_member_profiles_by_user_id(user_id)

    # Ensure member_profile_id is included in all profiles
    for profile in profiles:
        if Field.MEMBER_PROFILE_ID not in profile:
            profile[Field.MEMBER_PROFILE_ID] = profile['_id']

    return jsonify({
        'profiles': profiles
    })


@api_bp.route('/member-profiles/<profile_id>', methods=['PUT'])
@jwt_required()
def update_member_profile(profile_id):
    """
    Update member profile
    ---
    tags:
      - Member Profiles
    security:
      - bearerAuth: []
    parameters:
      - in: path
        name: profile_id
        required: true
        schema:
          type: string
      - in: body
        name: body
        schema:
          type: object
          properties:
            name:
              type: string
            relation:
              type: string
            birth_date:
              type: string
              format: date
            birth_time:
              type: string
              format: time
            birth_place:
              type: string
            latitude:
              type: number
            longitude:
              type: number
            gender:
              type: string
              enum: [male, female, other]
    responses:
      200:
        description: Profile updated successfully
      400:
        description: Validation error
      401:
        description: Unauthorized
      403:
        description: Forbidden
      404:
        description: Profile not found
    """
    # Try to convert profile_id to int if it's a digit string
    if re.match(r'^\d+$', profile_id):
        profile_id = int(profile_id)

    # Check if profile exists
    profile = MemberProfileService.get_member_profile_by_id(profile_id)
    if not profile:
        raise ResourceNotFoundError('Member profile')

    # Check if current user owns the profile
    current_user_id = get_jwt_identity()
    if str(profile['user_id']) != current_user_id:
        raise AuthorizationError()

    # Validate input
    data = request.get_json() or {}
    errors = validate_member_profile_input(data)
    if errors:
        raise ValidationError(errors)

    # Update profile
    updated_profile = MemberProfileService.update_member_profile(profile_id, data)

    return jsonify({
        'message': 'Member profile updated successfully',
        'profile': updated_profile
    })


@api_bp.route('/member-profiles/<profile_id>', methods=['DELETE'])
@jwt_required()
def delete_member_profile(profile_id):
    """
    Delete member profile
    ---
    tags:
      - Member Profiles
    security:
      - bearerAuth: []
    parameters:
      - in: path
        name: profile_id
        required: true
        schema:
          type: string
    responses:
      200:
        description: Profile deleted successfully
      401:
        description: Unauthorized
      403:
        description: Forbidden
      404:
        description: Profile not found
    """
    # Try to convert profile_id to int if it's a digit string
    if re.match(r'^\d+$', profile_id):
        profile_id = int(profile_id)

    # Check if profile exists
    profile = MemberProfileService.get_member_profile_by_id(profile_id)
    if not profile:
        raise ResourceNotFoundError('Member profile')

    # Check if current user owns the profile
    current_user_id = get_jwt_identity()
    if str(profile['user_id']) != current_user_id:
        raise AuthorizationError()

    # Delete profile
    MemberProfileService.delete_member_profile(profile_id)

    return jsonify({
        'message': 'Member profile deleted successfully'
    })


@api_bp.route('/member-profiles/with-charts', methods=['POST'])
@jwt_required()
def create_member_profile_with_charts():
    """
    Create member profile with automatic chart generation
    ---
    tags:
      - Member Profiles
    security:
      - bearerAuth: []
    parameters:
      - in: body
        name: body
        schema:
          type: object
          required:
            - member_name
            - member_gender
            - member_relation
            - user_birthdate
            - user_birthtime
            - user_birthplace
            - user_state
            - user_country
          properties:
            member_name:
              type: string
              description: Name of the member
            member_gender:
              type: string
              enum: [male, female, other]
              description: Gender of the member
            member_relation:
              type: string
              description: Relationship to the user (e.g., self, spouse, child, parent)
            user_birthdate:
              type: string
              description: Birth date in format DD-MM-YYYY
              example: 01-01-1990
            user_birthtime:
              type: string
              description: Birth time in format HH:MM:SS AM/PM
              example: 10:30:00 AM
            user_birthplace:
              type: string
              description: Birth place
              example: Chennai
            user_state:
              type: string
              description: State
              example: Tamil Nadu
            user_country:
              type: string
              description: Country
              example: India
    responses:
      201:
        description: Profile created successfully with charts
      400:
        description: Validation error
      401:
        description: Unauthorized
    """
    data = request.get_json() or {}

    # Get current user ID
    user_id = get_jwt_identity()

    # Create profile with automatic chart generation
    result = MemberProfileService.create_member_profile_with_charts(user_id, data)

    if not result['success']:
        raise ValidationError({'error': result['message']})

    # Ensure member_profile_id is included in the response
    member_profile = result['member_profile']
    if Field.MEMBER_PROFILE_ID not in member_profile:
        member_profile[Field.MEMBER_PROFILE_ID] = member_profile['_id']

    return jsonify({
        'message': result['message'],
        'profile': member_profile,
        'profile_id': result.get('profile_id', str(member_profile['_id'])),
        'member_profile_id': str(member_profile.get(Field.MEMBER_PROFILE_ID, member_profile['_id']))
    }), 201


@api_bp.route('/member-profiles/<profile_id>/charts', methods=['GET'])
@jwt_required()
def get_member_profile_with_charts(profile_id):
    """
    Get member profile with charts
    ---
    tags:
      - Member Profiles
    security:
      - bearerAuth: []
    parameters:
      - in: path
        name: profile_id
        required: true
        schema:
          type: string
    responses:
      200:
        description: Profile details with charts
      404:
        description: Profile not found
    """
    # Get current user ID
    user_id = get_jwt_identity()

    # Get profile with charts
    result = MemberProfileService.get_member_profile_with_charts(user_id, profile_id)

    if not result['success']:
        raise ResourceNotFoundError(result['message'])

    # Ensure member_profile_id is included in the response data
    data = result['data']
    if 'profile' in data and Field.MEMBER_PROFILE_ID not in data['profile']:
        data['profile'][Field.MEMBER_PROFILE_ID] = data['profile']['_id']

    return jsonify({
        'message': result['message'],
        'data': data
    })


@api_bp.route('/member-profiles/self', methods=['GET'])
@jwt_required()
def get_self_profile():
    """
    Get or create user's own profile as a member profile
    ---
    tags:
      - Member Profiles
    security:
      - bearerAuth: []
    responses:
      200:
        description: User's self profile
      401:
        description: Unauthorized
    """
    user_id = get_jwt_identity()
    profile = MemberProfileService.get_user_as_member_profile(user_id)

    # Ensure member_profile_id is included in the response
    if Field.MEMBER_PROFILE_ID not in profile:
        profile[Field.MEMBER_PROFILE_ID] = profile['_id']

    return jsonify({
        'profile': profile
    })