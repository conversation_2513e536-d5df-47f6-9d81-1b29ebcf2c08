"""
Rule Engine API

Professional astrological rule engine for evaluating complex planetary relationships,
house placements, and dasha-based marriage predictions.

This module provides a unified API endpoint that intelligently detects and processes
various astrological query formats with comprehensive error handling and validation.
"""

import re
from typing import Dict, Any, Optional, List
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId

from . import api_bp
from ..services.rule_engine import (
    process_rule_engine_request, validate_api_request_data,
    get_success_rating
)
from ..errors.exceptions import ValidationError


@api_bp.route('/rule-engine/', methods=['GET', 'POST'])
# @jwt_required()  # Temporarily disabled for testing
def unified_rule_engine_api():
    """
    Unified Rule Engine API
    
    Intelligently detects and processes various astrological query formats:
    
    - Basic rule evaluation with logical operators
    - House to house planet relationships  
    - Planet to house ruling planet relationships
    - Dasha-based marriage predictions with KOCHARAM filtering
    - Complex logical combinations with OR, AND, NOT operators
    
    Returns comprehensive analysis with detailed scoring and explanations.
    """
    try:
        # Handle GET request for API documentation
        if request.method == 'GET':
            return _get_api_documentation()

        # POST request processing
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No JSON data provided',
                'error_code': 'NO_DATA'
            }), 400

        # Process request using modular rule engine
        result = process_rule_engine_request(data)

        if result.get('success', False):
            return jsonify(result)
        else:
            return jsonify(result), 400

    except ValidationError as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'error_code': 'VALIDATION_ERROR'
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Internal server error: {str(e)}',
            'error_code': 'INTERNAL_ERROR'
        }), 500


def _get_api_documentation() -> Dict[str, Any]:
    """Return comprehensive API documentation."""
    return jsonify({
        "success": True,
        "message": "Professional Astrological Rule Engine API",
        "version": "3.0",
        "architecture": "Modular design with core, astrology, dasha, and utils modules",
        "supported_query_formats": {
            "basic_relationships": {
                "format": "Planet IS RELATED TO #th_House_Planet",
                "examples": ["Ketu IS RELATED TO 10th_House_Planet"]
            },
            "house_ruling_planets": {
                "format": "#th_House_Ruling_Planet IS RELATED TO #th_House_Ruling_Planet", 
                "examples": ["6th_House_Ruling_Planet IS RELATED TO 10th_House_Ruling_Planet"]
            },
            "planet_placements": {
                "format": "Planet in #th_House",
                "examples": ["Mars in 1st_House"]
            },
            "dasha_predictions": {
                "format": "Planet Bhukti_Dates AND Member_Age >= X AND <= Y AND PREDICTION_DURATION = Z",
                "examples": ["VENUS Bhukti_Dates AND Member_Age >= 25 AND <= 35 AND PREDICTION_DURATION = 10"]
            },
            "logical_combinations": {
                "format": "Query1 OR/AND/NOT Query2",
                "examples": ["Mars in 1st_House OR Jupiter in 10th_House"]
            }
        },
        "planets": ["Sun", "Moon", "Mars", "Mercury", "Jupiter", "Venus", "Saturn", "Rahu", "Ketu"],
        "houses": list(range(1, 13)),
        "operators": ["IS RELATED TO", "in", "WITH", "OR", "AND", "NOT"]
    })


# The validation and routing logic has been moved to the modular rule engine
# All request processing is now handled by process_rule_engine_request()

# End of API file - all other functions have been moved to the modular rule engine
