"""
Charts API

This module provides API endpoints for generating and accessing astrological charts.
"""

from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

from . import api_bp
from ..services.chart_service import generate_chart, generate_divisional_chart, get_available_chart_types
from ..services.chart_constants import ChartType, CHART_TYPE_NAMES, AVAILABLE_CHART_FORMATS
from ..errors.exceptions import ValidationError


@api_bp.route('/charts/types', methods=['GET'])
def get_chart_types():
    """
    Get available chart types
    ---
    tags:
      - Charts
    responses:
      200:
        description: List of available chart types
    """
    return jsonify({
        'success': True,
        'chart_types': get_available_chart_types(),
        'chart_formats': AVAILABLE_CHART_FORMATS
    })


@api_bp.route('/charts/generate', methods=['POST'])
@jwt_required()
def generate_user_chart():
    """
    Generate all 23 astrological divisional charts for a user
    ---
    tags:
      - Charts
    security:
      - bearerAuth: []
    parameters:
      - in: body
        name: body
        schema:
          type: object
          required:
            - user_birthdate
            - user_birthtime
            - user_birthplace
            - user_state
            - user_country
          properties:
            user_birthdate:
              type: string
              description: Birth date in format DD-MM-YYYY
              example: 01-01-1990
            user_birthtime:
              type: string
              description: Birth time in format HH:MM:SS AM/PM
              example: 10:30:00 AM
            user_birthplace:
              type: string
              description: Birth place
              example: Chennai
            user_state:
              type: string
              description: State
              example: Tamil Nadu
            user_country:
              type: string
              description: Country
              example: India
    responses:
      200:
        description: All 23 divisional chart data
      400:
        description: Validation error
      401:
        description: Unauthorized
    """
    data = request.get_json() or {}

    # Validate input
    required_fields = ['user_birthdate', 'user_birthtime', 'user_birthplace', 'user_state', 'user_country']
    for field in required_fields:
        if field not in data:
            raise ValidationError({field: f'{field} is required'})

    # Generate all 23 divisional charts
    chart_data = generate_chart(data)

    if 'error' in chart_data:
        return jsonify({
            'success': False,
            'message': chart_data['error']
        }), 400

    # Get chart names for reference
    chart_names = get_available_chart_types()

    return jsonify({
        'success': True,
        'chart_data': chart_data,
        'chart_names': chart_names
    })


@api_bp.route('/charts/divisional', methods=['POST'])
@jwt_required()
def generate_divisional_chart_api():
    """
    Generate a specific divisional chart with detailed structure
    ---
    tags:
      - Charts
    security:
      - bearerAuth: []
    parameters:
      - in: body
        name: body
        schema:
          type: object
          required:
            - user_birthdate
            - user_birthtime
            - user_birthplace
            - user_state
            - user_country
            - chart_type
          properties:
            user_birthdate:
              type: string
              description: Birth date in format DD-MM-YYYY
              example: 01-01-1990
            user_birthtime:
              type: string
              description: Birth time in format HH:MM:SS AM/PM
              example: 10:30:00 AM
            user_birthplace:
              type: string
              description: Birth place
              example: Chennai
            user_state:
              type: string
              description: State
              example: Tamil Nadu
            user_country:
              type: string
              description: Country
              example: India
            chart_type:
              type: integer
              description: Divisional chart factor (1=D1, 9=D9, etc.)
              example: 9
    responses:
      200:
        description: Detailed chart data with structured format
      400:
        description: Validation error
      401:
        description: Unauthorized
    """
    data = request.get_json() or {}

    # Validate input
    required_fields = ['user_birthdate', 'user_birthtime', 'user_birthplace', 'user_state', 'user_country', 'chart_type']
    for field in required_fields:
        if field not in data:
            raise ValidationError({field: f'{field} is required'})

    chart_type = data['chart_type']

    # Validate chart type
    valid_factors = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 16, 20, 24, 27, 30, 40, 45, 60, 81, 108, 144]
    if chart_type not in valid_factors:
        raise ValidationError({'chart_type': f'Invalid chart type: {chart_type}. Must be one of {valid_factors}'})

    # Generate chart
    chart_data = generate_divisional_chart(data, divisional_chart_factor=chart_type)

    if 'error' in chart_data:
        return jsonify({
            'success': False,
            'message': chart_data['error']
        }), 400

    return jsonify({
        'success': True,
        'chart_data': chart_data
    })
