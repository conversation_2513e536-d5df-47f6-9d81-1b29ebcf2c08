"""
Schemas Package

This package contains all the schema validation modules for the Fortune Lens application.
It is organized into subdirectories based on functionality:
- auth: Authentication-related schemas
- profiles: User and member profile schemas
- astrology: Astrological data schemas
"""

# Import schemas from existing files
from .auth_schemas import validate_login_input, validate_register_input
from .user_schemas import validate_update_user_input
from .profile_schemas import validate_profile_input
from .member_profile_schemas import validate_member_profile_input
