"""
Member Profile Schemas
"""

import re
from datetime import datetime


def validate_member_profile_input(data):
    """
    Validate member profile input
    
    Args:
        data (dict): Input data
        
    Returns:
        dict: Validation errors, empty if no errors
    """
    errors = {}
    
    # Check required fields
    if 'name' not in data or not data['name']:
        errors['name'] = 'Name is required'
    
    if 'relation' not in data or not data['relation']:
        errors['relation'] = 'Relation is required'
    
    # Validate birth date format if provided
    if 'birth_date' in data and data['birth_date']:
        try:
            # Try to parse date
            if isinstance(data['birth_date'], str):
                datetime.strptime(data['birth_date'], '%Y-%m-%d')
        except ValueError:
            errors['birth_date'] = 'Invalid date format. Use YYYY-MM-DD'
    
    # Validate birth time format if provided
    if 'birth_time' in data and data['birth_time']:
        time_regex = r'^([01]\d|2[0-3]):([0-5]\d)(:([0-5]\d))?$'
        if not re.match(time_regex, data['birth_time']):
            errors['birth_time'] = 'Invalid time format. Use HH:MM or HH:MM:SS'
    
    # Validate latitude and longitude if provided
    if 'latitude' in data and data['latitude'] is not None:
        try:
            lat = float(data['latitude'])
            if lat < -90 or lat > 90:
                errors['latitude'] = 'Latitude must be between -90 and 90'
        except (ValueError, TypeError):
            errors['latitude'] = 'Latitude must be a number'
    
    if 'longitude' in data and data['longitude'] is not None:
        try:
            lon = float(data['longitude'])
            if lon < -180 or lon > 180:
                errors['longitude'] = 'Longitude must be between -180 and 180'
        except (ValueError, TypeError):
            errors['longitude'] = 'Longitude must be a number'
    
    # Validate gender if provided
    if 'gender' in data and data['gender']:
        if data['gender'] not in ['male', 'female', 'other']:
            errors['gender'] = 'Gender must be one of: male, female, other'
    
    return errors
