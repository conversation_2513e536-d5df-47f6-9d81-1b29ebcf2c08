"""
User Schemas
"""

import re


def validate_update_user_input(data):
    """
    Validate update user input

    Args:
        data (dict): Input data

    Returns:
        dict: Validation errors, empty if no errors
    """
    errors = {}

    # Validate email format
    if 'email' in data and data['email']:
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, data['email']):
            errors['email'] = 'Invalid email format'

    # Validate name
    if 'name' in data and not data['name']:
        errors['name'] = 'Name cannot be empty'

    # Validate mobile number format if provided
    if 'mobile' in data and data['mobile']:
        mobile_regex = r'^[0-9]{10}$'
        if not re.match(mobile_regex, data['mobile']):
            errors['mobile'] = 'Invalid mobile number format. Must be exactly 10 digits'

    return errors
