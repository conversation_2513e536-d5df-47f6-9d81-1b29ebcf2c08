# Schemas Directory Structure

This directory contains all the schema validation modules for the Fortune Lens application. It is organized into subdirectories based on functionality:

## Directory Structure

```
schemas/
├── __init__.py                 # Schema package initialization
├── auth/                       # Authentication schemas
│   ├── __init__.py             # Auth schemas initialization
│   └── auth_schemas.py         # Login and registration validation
├── profiles/                   # User and member profile schemas
│   ├── __init__.py             # Profiles schemas initialization
│   ├── user_schemas.py         # User validation
│   ├── profile_schemas.py      # Profile validation
│   └── member_profile_schemas.py # Member profile validation
└── astrology/                  # Astrological schemas
    └── __init__.py             # Astrology schemas initialization
```

## Schema Validation

Each schema module contains validation functions that check the input data for API endpoints. These functions return a dictionary of validation errors, which is empty if the data is valid.

## Backward Compatibility

The main `__init__.py` file imports all validation functions from the subdirectories to maintain backward compatibility with existing code.
