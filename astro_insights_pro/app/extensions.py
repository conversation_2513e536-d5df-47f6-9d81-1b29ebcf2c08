"""
Flask Extensions

This module initializes all Flask extensions used in the application.
Extensions are initialized without arguments here and later initialized
with the Flask application instance in the create_app function.
"""

from flask_pymongo import PyMongo
from flask_jwt_extended import JWTManager
from flask_bcrypt import Bcrypt

# MongoDB extension for database operations
mongo = PyMongo()

# JWT extension for authentication and token management
jwt = JWTManager()

# Bcrypt extension for password hashing
bcrypt = Bcrypt()
