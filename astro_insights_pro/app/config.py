"""
Astro Insights Pro - Application Configuration

Professional configuration management for the Astro Insights Pro platform.
This module defines configuration classes for different environments
(development, testing, production) with proper security and scalability settings.

Author: Astro Insights Pro Team
Version: 1.0.0
License: MIT
"""

import os
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from .constants import Collection

# Get the base directory
BASE_DIR = Path(__file__).parent.parent


class BaseConfiguration:
    """
    Base configuration class with common settings for all environments.

    This class contains the fundamental configuration settings that are
    shared across all deployment environments (development, testing, production).
    """

    # Application Metadata
    APP_NAME = os.getenv('APP_NAME', 'Astro Insights Pro')
    APP_VERSION = os.getenv('APP_VERSION', '1.0.0')

    # Flask Core Settings
    SECRET_KEY = os.getenv('SECRET_KEY', 'astro-insights-pro-secret-key-change-in-production')
    DEBUG = False
    TESTING = False

    # Server Configuration
    HOST = os.getenv('HOST', '0.0.0.0')
    PORT = int(os.getenv('PORT', 5003))

    # Database Configuration
    MONGODB_URI = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/fortune_lens')
    MONGODB_DATABASE = os.getenv('MONGODB_DATABASE', 'fortune_lens')

    # MongoDB Collections (Professional Naming)
    COLLECTION_USER_PROFILES = Collection.USER_PROFILE
    COLLECTION_MEMBER_PROFILES = Collection.MEMBER_PROFILE
    COLLECTION_ASTROLOGICAL_DATA = Collection.USER_MEMBER_ASTRO_PROFILE_DATA

    # Legacy MongoDB Settings (for backward compatibility)
    MONGO_URI = MONGODB_URI
    MONGO_USER_COLLECTION = Collection.USER_PROFILE
    MONGO_MEMBER_PROFILE_COLLECTION = Collection.MEMBER_PROFILE
    MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION = Collection.USER_MEMBER_ASTRO_PROFILE_DATA

    # Email settings (using Gmail SMTP)
    MAIL_SERVER = os.getenv('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_PORT = int(os.getenv('MAIL_PORT', 587))
    MAIL_USE_TLS = os.getenv('MAIL_USE_TLS', 'True') == 'True'
    MAIL_USERNAME = os.getenv('MAIL_USERNAME', '<EMAIL>')
    MAIL_PASSWORD = os.getenv('MAIL_PASSWORD', 'your-app-password')
    MAIL_DEFAULT_SENDER = os.getenv('MAIL_DEFAULT_SENDER', 'Fortune Lens <<EMAIL>>')

    # Twilio settings for SMS
    TWILIO_ACCOUNT_SID = os.getenv('TWILIO_ACCOUNT_SID', 'your-account-sid')
    TWILIO_AUTH_TOKEN = os.getenv('TWILIO_AUTH_TOKEN', 'your-auth-token')
    TWILIO_PHONE_NUMBER = os.getenv('TWILIO_PHONE_NUMBER', 'your-twilio-phone-number')

    # JWT settings
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'jwt_secret_key')  # Should be overridden in production
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(days=30)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)

    # Logging configuration
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = 'logs/app.log'

    # API settings
    API_TITLE = 'Fortune Lens API'
    API_VERSION = 'v1'
    API_DESCRIPTION = 'API for Fortune Lens application'


class DevelopmentConfig(BaseConfiguration):
    """Development configuration with debugging enabled"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'


class TestingConfig(BaseConfiguration):
    """Testing configuration with test database and debugging enabled"""
    DEBUG = True
    TESTING = True
    MONGO_URI = os.getenv('TEST_MONGO_URI', 'mongodb://localhost:27017/fortune_lens_test')
    LOG_LEVEL = 'DEBUG'


class ProductionConfig(BaseConfiguration):
    """Production configuration with secure settings and minimal logging"""
    # In production, these must be set as environment variables
    SECRET_KEY = os.getenv('SECRET_KEY')
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY')

    # Disable debug mode in production
    DEBUG = False

    # Only log errors in production
    LOG_LEVEL = 'ERROR'


# Configuration dictionary mapping environment names to config classes
config_by_name = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig
}
