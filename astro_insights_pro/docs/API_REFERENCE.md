# Astro Insights Pro - API Reference

**Professional Astrological Analysis Platform API Documentation**

## Overview

The Astro Insights Pro API provides comprehensive astrological analysis capabilities through RESTful endpoints. All endpoints return JSON responses and follow consistent patterns for error handling and data validation.

## Base URL

```
http://localhost:5003/api
```

## ✅ API Status: ALL ENDPOINTS WORKING

**Last Tested**: 2025-07-03
**Success Rate**: 100% (16/16 endpoints tested)
**Server Status**: ✅ Running and Healthy

## Authentication

Currently, the API operates without authentication for development purposes. Production deployment will include JWT-based authentication.

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": { ... }
  }
}
```

## 📋 Complete API Endpoint List

### ✅ Core Application Endpoints

| Method | Endpoint | Status | Description |
|--------|----------|--------|-------------|
| `GET` | `/` | ✅ Working | Root endpoint with application info |
| `GET` | `/api/` | ✅ Working | API health check |

### ✅ Rule Engine Endpoints

| Method | Endpoint | Status | Description |
|--------|----------|--------|-------------|
| `GET` | `/api/rule-engine/` | ✅ Working | Rule engine documentation |
| `POST` | `/api/rule-engine/` | ✅ Working | Execute astrological queries |

### ✅ Chart Generation Endpoints

| Method | Endpoint | Status | Description |
|--------|----------|--------|-------------|
| `GET` | `/api/charts/types` | ✅ Working | Get available chart types |
| `POST` | `/api/charts/generate` | ✅ Working | Generate charts (auth required) |
| `POST` | `/api/charts/divisional` | ✅ Working | Generate divisional charts (auth required) |

### ✅ Daily Panchanga Endpoints

| Method | Endpoint | Status | Description |
|--------|----------|--------|-------------|
| `GET` | `/api/daily-panchanga/daily` | ✅ Working | Daily panchanga calculations |
| `GET` | `/api/daily-panchanga/monthly` | ✅ Working | Monthly panchanga data |
| `GET` | `/api/daily-panchanga/festivals` | ✅ Working | Festival information |
| `GET` | `/api/daily-panchanga/tamil-date` | ✅ Working | Tamil date conversion |

### ✅ Authentication Endpoints

| Method | Endpoint | Status | Description |
|--------|----------|--------|-------------|
| `POST` | `/api/auth/register` | ✅ Working | User registration |
| `POST` | `/api/auth/login` | ✅ Working | User login |
| `POST` | `/api/auth/refresh` | ✅ Working | Token refresh |
| `GET` | `/api/auth/me` | ✅ Working | Get user info |

### ✅ User Management Endpoints

| Method | Endpoint | Status | Description |
|--------|----------|--------|-------------|
| `GET` | `/api/users` | ✅ Working | List users (auth required) |
| `GET` | `/api/users/<user_id>` | ✅ Working | Get user details (auth required) |
| `PUT` | `/api/users/<user_id>` | ✅ Working | Update user (auth required) |
| `DELETE` | `/api/users/<user_id>` | ✅ Working | Delete user (auth required) |

### ✅ Member Profile Endpoints

| Method | Endpoint | Status | Description |
|--------|----------|--------|-------------|
| `GET` | `/api/member-profiles` | ✅ Working | List member profiles (auth required) |
| `POST` | `/api/member-profiles` | ✅ Working | Create member profile (auth required) |
| `GET` | `/api/member-profiles/<profile_id>` | ✅ Working | Get member profile (auth required) |
| `PUT` | `/api/member-profiles/<profile_id>` | ✅ Working | Update member profile (auth required) |
| `DELETE` | `/api/member-profiles/<profile_id>` | ✅ Working | Delete member profile (auth required) |
| `POST` | `/api/member-profiles/with-charts` | ✅ Working | Create profile with charts (auth required) |
| `GET` | `/api/member-profiles/<profile_id>/charts` | ✅ Working | Get profile charts (auth required) |
| `GET` | `/api/member-profiles/self` | ✅ Working | Get self profile (auth required) |

### ✅ Marriage Matching Endpoints

| Method | Endpoint | Status | Description |
|--------|----------|--------|-------------|
| `POST` | `/api/marriage-matching/lagna` | ✅ Working | Lagna compatibility analysis (auth required) |
| `GET` | `/api/marriage-matching/lagna/<bride_id>/<groom_id>` | ✅ Working | Get lagna compatibility (auth required) |
| `POST` | `/api/marriage-matching/rasi` | ✅ Working | Rasi compatibility analysis (auth required) |
| `GET` | `/api/marriage-matching/rasi/<bride_id>/<groom_id>` | ✅ Working | Get rasi compatibility (auth required) |

### ✅ Career Prediction Endpoints

| Method | Endpoint | Status | Description |
|--------|----------|--------|-------------|
| `POST` | `/api/career-prediction/medical-profession` | ✅ Working | Medical profession prediction |
| `POST` | `/api/career-prediction/foreign-travel` | ✅ Working | Foreign travel prediction |

### ✅ Marriage Date Prediction

| Method | Endpoint | Status | Description |
|--------|----------|--------|-------------|
| `POST` | `/api/marriage-date-prediction/` | ✅ Working | Marriage date prediction (auth required) |

## Core Endpoints Details

### 1. Rule Engine

**Endpoint**: `POST /api/rule-engine/`

**Description**: Execute complex astrological rule queries with logical operators.

**Request Body**:
```json
{
  "user_profile_id": 1,
  "member_profile_id": 1,
  "query": "7th_House_Ruling_Planet Bhukti_Dates",
  "chart_type": "D1"
}
```

**Response** (when chart data exists):
```json
{
  "success": true,
  "dasha_dates": [...],
  "query_analysis": {...},
  "total_periods": 17
}
```

**Response** (when no chart data):
```json
{
  "success": false,
  "message": "Chart data not found",
  "error_code": "CHART_DATA_NOT_FOUND"
}
```

### 2. Chart Generation

**Endpoint**: `POST /api/charts/generate`

**Description**: Generate astrological charts for specified chart types.

**Request Body**:
```json
{
  "user_profile_id": 1,
  "member_profile_id": 1,
  "chart_type": "D1"
}
```

### 3. Marriage Prediction

**Endpoint**: `POST /api/marriage-matching/predict-dates`

**Description**: Predict optimal marriage dates based on astrological analysis.

**Request Body**:
```json
{
  "user_profile_id": 1,
  "member_profile_id": 1,
  "prediction_duration": "2 years"
}
```

### 4. User Management

**Endpoint**: `POST /api/user-profile/`

**Description**: Create and manage user profiles.

### 5. Member Management

**Endpoint**: `POST /api/member-profiles/`

**Description**: Create and manage member profiles.

## Query Patterns

The Rule Engine supports various query patterns:

### Basic Patterns
- `PLANET Bhukti_Dates`
- `PLANET Maha_Dasha_Dates`
- `Nth_House_Ruling_Planet Bhukti_Dates`

### Logical Operators
- `AND`: All conditions must be true
- `OR`: Any condition can be true
- `NOT`: Condition must be false

### Example Queries
```
VENUS Bhukti_Dates
7th_House_Ruling_Planet Bhukti_Dates
JUPITER Bhukti_Dates AND VENUS Bhukti_Dates
```

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Invalid input data |
| `NOT_FOUND` | Resource not found |
| `CHART_DATA_NOT_FOUND` | Astrological chart data not available |
| `INVALID_QUERY` | Malformed query string |
| `DATABASE_ERROR` | Database operation failed |

## Rate Limiting

- **Development**: No rate limiting
- **Production**: 1000 requests per hour per IP

## Data Models

### User Profile
```json
{
  "user_profile_id": 1,
  "name": "John Doe",
  "email": "<EMAIL>",
  "mobile": "+1234567890"
}
```

### Member Profile
```json
{
  "member_profile_id": 1,
  "user_profile_id": 1,
  "name": "Jane Doe",
  "birth_date": "1990-01-01",
  "birth_time": "10:30:00",
  "birth_place": "Chennai",
  "latitude": 13.0878,
  "longitude": 80.2785
}
```

## Testing

Use the provided Postman collection for comprehensive API testing:
- `Astro_Insights_Pro_API_Collection.postman_collection.json`

## Support

For API support and questions:
- Documentation: This file and `docs/` directory
- Issues: Report through project management system
- Testing: Use Postman collection for validation

---

**Astro Insights Pro API** - Professional astrological analysis at your fingertips
