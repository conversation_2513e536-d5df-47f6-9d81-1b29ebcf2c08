# Astro Insights Pro - Rule Engine API Documentation

**Professional Astrological Rule Engine**

The Rule Engine API allows you to evaluate complex astrological rules and conditions against a user's chart data. This document explains how to use the API endpoints and provides examples.

## API Endpoints

### 1. Evaluate Rule

**Endpoint:** `/api/rule-engine/evaluate`

**Method:** `POST`

**Authentication:** JWT token required

**Description:** Evaluates a complex astrological rule against a user's chart data.

**Request Body:**

```json
{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "Moon IN House1 OR Moon IN House3 OR Moon IN House6 OR Moon IN House10 OR Moon IN House11",
    "chart_type": "d1"
}
```

**Parameters:**

- `user_profile_id` (required): The ID of the user profile
- `member_profile_id` (required): The ID of the member profile
- `query` (required): The rule query string
- `chart_type` (optional): The chart type to evaluate against (default: "d1")

**Response:**

```json
{
    "success": true,
    "query": "Moon IN House1 OR Moon IN House3 OR Moon IN House6 OR Moon IN House10 OR Moon IN House11",
    "result": true,
    "planet_positions": {
        "SUN": 2,
        "MOON": 3,
        "MARS": 6,
        "MERCURY": 9,
        "JUPITER": 5,
        "VENUS": 7,
        "SATURN": 10,
        "RAHU": 1,
        "KETU": 7
    }
}
```

### 2. Get Rule Suggestions

**Endpoint:** `/api/rule-engine/suggestions`

**Method:** `GET`

**Authentication:** JWT token required

**Description:** Returns suggestions for building rule queries, including available planets, operators, and logical operators.

**Response:**

```json
{
    "success": true,
    "planets": ["SUN", "MOON", "MARS", "MERCURY", "JUPITER", "VENUS", "SATURN", "RAHU", "KETU"],
    "operators": ["IN", "NOT IN"],
    "logical_operators": ["AND", "OR"],
    "houses": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    "example_queries": [
        "Moon IN House1 OR Moon IN House3 OR Moon IN House6 OR Moon IN House10 OR Moon IN House11",
        "Sun IN House5 AND Jupiter IN House9",
        "Mars IN House10 OR Saturn IN House10",
        "Jupiter IN House1 AND Venus IN House7",
        "Rahu NOT IN House8 AND Ketu NOT IN House2"
    ]
}
```

## Query Syntax

The rule engine supports a specific syntax for defining astrological conditions:

### Basic Condition

A basic condition has the format: `Planet Operator House`

- **Planet**: The name of the planet (e.g., Sun, Moon, Mars)
- **Operator**: The comparison operator (IN, NOT IN)
- **House**: The house number, prefixed with "House" (e.g., House1, House10)

Example: `Moon IN House1`

### Logical Operators

You can combine multiple conditions using logical operators:

- **OR**: Evaluates to true if any of the conditions are true
- **AND**: Evaluates to true only if all conditions are true

Examples:
- `Moon IN House1 OR Moon IN House3`
- `Sun IN House5 AND Jupiter IN House9`

### Complex Queries

You can create complex queries by combining multiple conditions with logical operators:

Example: `Moon IN House1 AND Sun IN House5 OR Mars IN House10`

This query will evaluate to true if:
- Moon is in House1 AND Sun is in House5, OR
- Mars is in House10

## Error Handling

The API returns appropriate error messages for various scenarios:

- **400 Bad Request**: Missing or invalid parameters
- **404 Not Found**: Chart data not found for the specified user and member profile
- **500 Internal Server Error**: Unexpected errors during processing

Example error response:

```json
{
    "success": false,
    "message": "Chart data not found for user_profile_id: 1, member_profile_id: 1"
}
```
