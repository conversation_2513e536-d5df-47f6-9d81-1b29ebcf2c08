# Dasha Based Marriage Prediction API - Postman Collection

## Overview

This Postman collection contains comprehensive test cases for the **Dasha Based Marriage Date Prediction API** in the Fortune Lens application. The collection includes all supported query patterns with planetary names in dasha_dates responses.

## 🚀 Quick Setup

### 1. Import Collection
1. Open Postman
2. Click **Import** button
3. Select `Dasha_Based_Marriage_Prediction_API.json`
4. Collection will be imported with all test cases

### 2. Set Environment Variables
- **base_url**: `http://localhost:5003` (default)
- Update the base_url if your server runs on a different port

### 3. Start Fortune Lens Server
```bash
cd fortune_lens
python run.py
```

## 📋 Collection Structure

### 1. **Simple Planet Dasha Queries**
Test individual planet dasha periods:
- VENUS Dasha Dates with Age Range
- JUPITER Dasha Dates with Age Range  
- MARS Dasha Dates with Prediction Duration

### 2. **House Ruling Planet Dasha Queries**
Test house ruling planet dasha periods:
- 2nd House Ruling Planet (Family/Wealth)
- 7th House Ruling Planet (Marriage)
- 5th House Ruling Planet (Love/Romance)
- 11th House Ruling Planet (Gains/Fulfillment)

### 3. **Planets in Houses Dasha Queries**
Test dasha periods of planets located in specific houses:
- Planets in 7th House (Marriage House)
- Planets in 10th House (Career/Status)
- Planets in 2nd House (Family/Wealth)

### 4. **Planets with Stars Dasha Queries**
Test dasha periods of planets in specific nakshatras:
- Planets with VENUS Stars
- Planets with 2nd House Ruling Planet Stars
- Planets with 7th House Ruling Planet Stars

### 5. **Complex OR Condition Queries**
Test complex logical combinations:
- Original Complex Query (Marriage Houses OR VENUS)
- Multiple Planets OR Condition
- Marriage Houses OR Love Houses

### 6. **Complex Stars and Houses Queries**
Advanced combinations:
- Stars of Marriage Houses OR VENUS
- Planets in Marriage Houses
- Comprehensive Marriage Analysis

### 7. **Age and Duration Variations**
Different age ranges and prediction durations:
- Young Age Marriage Prediction (18-25)
- Middle Age Marriage Prediction (30-45)
- Extended Duration Analysis (10 years)
- Minimum Age Only

## 🎯 Key Query Patterns

### Basic Patterns
```
PLANET Dasa_Dates AND Member_Age >= X AND <= Y
#th House Ruling Planet Dasa_Dates AND Member_Age >= X AND <= Y
```

### Complex Patterns
```
(A OR B OR C) AND Member_Age >= X AND <= Y AND PREDICTION_DURATION = Z
Dasa_Dates of PLANETS IN #th House AND Member_Age >= X AND <= Y
Dasa_Dates of PLANETS WITH STARS_OF PLANET AND Member_Age >= X
```

## 📊 Expected Response Structure

### Successful Response
```json
{
  "success": true,
  "overall_result": true,
  "prediction_duration": 2,
  "prediction_period": {
    "start_date": "2025-06-21 07:19:03",
    "end_date": "2027-06-21 07:19:03",
    "duration_years": 2
  },
  "dasha_dates": [
    {
      "planet_name": "JUPITER",
      "start_date": "2015-08-05 13:06:01",
      "end_date": "2031-08-05 15:32:38",
      "duration_days": 5844
    }
  ],
  "successful_conditions": [...],
  "evaluation_breakdown": [...]
}
```

### Key Response Fields
- **overall_result**: Boolean indicating if marriage conditions are met
- **dasha_dates**: Array of dasha periods with planet names
- **planet_name**: Name of the planet for each dasha period
- **prediction_period**: Current prediction timeframe
- **successful_conditions**: Details of conditions that returned true
- **evaluation_breakdown**: Step-by-step evaluation details

## 🧪 Test Data Information

### Current Test Profile
- **User Profile ID**: "1"
- **Member Profile ID**: "1"
- **Current Age**: 47 years (born 1978)
- **Chart Type**: D1 (Main birth chart)

### Active Dasha Periods
- **JUPITER Dasha**: 2015-2031 (Currently Active)
- **VENUS Dasha**: 2074-2094 (Future)

### House Information
- **7th House**: Ruled by JUPITER in MEENAM (Pisces)
- **2nd House**: Ruled by VENUS in THULAM (Libra)
- **10th House**: Contains JUPITER and VENUS

## 🎯 Recommended Test Sequence

### 1. Start with Simple Queries
- Test individual planet dasha queries
- Verify planet names appear in responses
- Check age calculations

### 2. Test House Ruling Planets
- 7th House Ruling Planet (should return true)
- 2nd House Ruling Planet (should return false - future dasha)

### 3. Test Complex OR Conditions
- Original complex query with realistic age range
- Multiple planet combinations

### 4. Test Advanced Patterns
- Planets in houses
- Nakshatra (stars) based queries
- Extended duration predictions

## 🔧 Troubleshooting

### Common Issues

1. **Server Not Running**
   - Error: Connection refused
   - Solution: Start server with `python run.py`

2. **Wrong Port**
   - Error: 403 Forbidden or timeout
   - Solution: Check if server is on port 5003, update base_url

3. **Invalid User/Member ID**
   - Error: Chart data not found
   - Solution: Ensure test data exists in MongoDB

4. **Age Calculation Issues**
   - Error: Age returns null
   - Solution: Verify birth_date exists in chart data

### Expected Results for Test Data

- **Age 47**: All age conditions >= 40 should return true
- **JUPITER Dasha Active**: 7th house queries should return true
- **VENUS Dasha Future**: VENUS direct queries should return false
- **Complex OR**: Should return true if any condition matches

## 📈 Performance Notes

- Simple queries: ~100-200ms response time
- Complex OR queries: ~200-500ms response time
- Queries with multiple planets: ~300-800ms response time

## 🔄 Updates and Maintenance

### Adding New Test Cases
1. Copy existing request structure
2. Modify query string
3. Update description
4. Test with current data

### Modifying Age Ranges
- Use realistic ranges based on test data age (47)
- Age >= 40 conditions will return true
- Age <= 50 conditions will return true
- Age <= 35 conditions will return false

## 📞 Support

For issues with the API or Postman collection:
1. Check server logs for errors
2. Verify MongoDB data exists
3. Test with simple queries first
4. Check age calculations match expected values

---

**Happy Testing! 🎉**

This collection provides comprehensive coverage of all dasha-based marriage prediction functionality with planetary names in responses.
