{"info": {"_postman_id": "dasha-marriage-prediction-api", "name": "Dasha Based Marriage Prediction API", "description": "Complete collection of dasha-based marriage date prediction queries for Fortune Lens application. This collection includes all supported query patterns with planetary names in dasha_dates responses.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Simple Planet Dasha Queries", "item": [{"name": "VENUS Dasha Dates with Age Range", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"VENUS Dasa_Dates AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test VENUS planet dasha periods with age range 40-50 years"}}, {"name": "JUPITER Dasha Dates with Age Range", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"JUPITER Dasa_Dates AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test JUPITER planet dasha periods with age range 40-50 years"}}, {"name": "MARS Dasha Dates with Prediction Duration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"MARS Dasa_Dates AND Member_Age >= 40 AND PREDICTION_DURATION = 5\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test MARS planet dasha with 5-year prediction duration"}}], "description": "Simple planet dasha queries for individual planets"}, {"name": "Simple Planet Bhukti Queries", "item": [{"name": "VENUS Bhukti Dates with Age Range", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"VENUS Bhukti_Dates AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test VENUS planet bhukti periods with age range 40-50 years"}}, {"name": "JUPITER Bhukti Dates with Age Range", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"JUPITER Bhukti_Dates AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test JUPITER planet bhukti periods with age range 40-50 years"}}, {"name": "MARS Bhukti Dates with Prediction Duration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"MARS Bhukti_Dates AND Member_Age >= 40 AND PREDICTION_DURATION = 5\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test MARS planet bhukti with 5-year prediction duration"}}], "description": "Simple planet bhukti queries for individual planets"}, {"name": "House Ruling Planet Dasha Queries", "item": [{"name": "2nd House Ruling Planet Dasha", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"2nd House Ruling Planet Dasa_Dates AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test 2nd house (family/wealth) ruling planet dasha periods"}}, {"name": "7th House Ruling Planet Dasha", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"7th House Ruling Planet Dasa_Dates AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test 7th house (marriage) ruling planet dasha periods"}}, {"name": "5th House Ruling Planet Dasha", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"5th House Ruling Planet Dasa_Dates AND Member_Age >= 21 AND <= 35\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test 5th house (love/romance) ruling planet dasha periods"}}, {"name": "11th House Ruling Planet Dasha", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"11th House Ruling Planet Dasa_Dates AND Member_Age >= 25 AND <= 40\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test 11th house (gains/fulfillment) ruling planet dasha periods"}}], "description": "House ruling planet dasha queries for marriage-related houses"}, {"name": "House Ruling Planet Bhukti Queries", "item": [{"name": "2nd House Ruling Planet Bhukti", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"2nd House Ruling Planet Bhukti_Dates AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test 2nd house (family/wealth) ruling planet bhukti periods"}}, {"name": "7th House Ruling Planet Bhukti", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"7th House Ruling Planet Bhukti_Dates AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test 7th house (marriage) ruling planet bhukti periods"}}, {"name": "5th House Ruling Planet Bhukti", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"5th House Ruling Planet Bhukti_Dates AND Member_Age >= 21 AND <= 35\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test 5th house (love/romance) ruling planet bhukti periods"}}], "description": "House ruling planet bhu<PERSON>i queries for marriage-related houses"}, {"name": "Planets in Houses Dasha Queries", "item": [{"name": "Planets in 7th House Dasha", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"Dasa_Dates of PLANETS IN 7th House AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test dasha periods of all planets located in 7th house (marriage house)"}}, {"name": "Planets in 10th House Dasha", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"Dasa_Dates of PLANETS IN 10th House AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test dasha periods of all planets located in 10th house (career/status)"}}, {"name": "Planets in 2nd House Dasha", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"Dasa_Dates of PLANETS IN 2nd House AND Member_Age >= 30 AND <= 45\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test dasha periods of all planets located in 2nd house (family/wealth)"}}], "description": "Dasha queries for planets located in specific houses"}, {"name": "Planets with Stars <PERSON>a <PERSON>ries", "item": [{"name": "Planets with VENUS Stars Dasha", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"Dasa_Dates of PLANETS WITH STARS_OF VENUS AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test dasha periods of planets placed in nakshatras ruled by VENUS"}}, {"name": "Planets with 2nd House Ruling Planet Stars", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"Dasa_Dates of PLANETS WITH STARS_OF 2nd House Ruling Planet AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test dasha periods of planets in nakshatras ruled by 2nd house ruling planet"}}, {"name": "Planets with 7th House Ruling Planet Stars", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"Dasa_Dates of PLANETS WITH STARS_OF 7th House Ruling Planet AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test dasha periods of planets in nakshatras ruled by 7th house ruling planet"}}], "description": "<PERSON>a queries for planets placed in specific nakshatra (star) rulers"}, {"name": "Complex OR Condition Queries", "item": [{"name": "Original Complex Query - Marriage Houses OR VENUS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"(2nd House Ruling Planet Dasa_Dates OR 7th House Ruling Planet Dasa_Dates OR VENUS Dasa_Dates) AND Member_Age >= 21 AND <= 35 AND PREDICTION_DURATION = 2\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Original complex query: Marriage-related house ruling planets OR VENUS with age 21-35"}}, {"name": "Complex Query - Realistic Age Range", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"(2nd House Ruling Planet Dasa_Dates OR 7th House Ruling Planet Dasa_Dates OR VENUS Dasa_Dates) AND Member_Age >= 21 AND <= 50 AND PREDICTION_DURATION = 2\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Complex query with realistic age range 21-50 for current test data"}}, {"name": "Multiple Planets OR Condition", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"(VENUS Dasa_Dates OR JUPITER Dasa_Dates OR MARS Dasa_Dates) AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Multiple planet dasha periods with OR logic"}}, {"name": "Marriage Houses OR Love Houses", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"(7th House Ruling Planet Dasa_Dates OR 5th House Ruling Planet Dasa_Dates OR 11th House Ruling Planet Dasa_Dates) AND Member_Age >= 25 AND <= 45\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Marriage (7th), love (5th), and fulfillment (11th) house ruling planets"}}], "description": "Complex queries with OR conditions combining multiple dasha periods"}, {"name": "Mixed Dasha and Bhukti Queries", "item": [{"name": "7th House Dasha OR Bhukti", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"(7th House Ruling Planet Dasa_Dates OR 7th House Ruling Planet Bhukti_Dates) AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test 7th house ruling planet in both maha dasha and bhukti periods"}}, {"name": "VENUS Dasha OR Bhukti", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"(VENUS Dasa_Dates OR VENUS Bhukti_Dates) AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test VENUS in both maha dasha and bhukti periods"}}, {"name": "Complex Mixed Query - Marriage Houses", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"(7th House Ruling Planet Dasa_Dates OR 7th House Ruling Planet Bhukti_Dates OR VENUS Bhukti_Dates) AND Member_Age >= 40 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Complex query mixing maha dasha and bhukti periods for marriage prediction"}}, {"name": "Comprehensive Marriage Analysis - All Periods", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"(2nd House Ruling Planet Dasa_Dates OR 2nd House Ruling Planet Bhukti_Dates OR 7th House Ruling Planet Dasa_Dates OR 7th House Ruling Planet Bhukti_Dates OR VENUS Dasa_Dates OR VENUS Bhukti_Dates) AND Member_Age >= 40 AND <= 50 AND PREDICTION_DURATION = 5\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Comprehensive analysis including all maha dasha and bhukti periods for marriage houses and VENUS"}}], "description": "Complex queries combining both maha dasha and bhukti periods"}, {"name": "Complex Stars and Houses Queries", "item": [{"name": "Stars of Marriage Houses OR VENUS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"(Dasa_Dates of PLANETS WITH STARS_OF 2nd House Ruling Planet OR Dasa_Dates of PLANETS WITH STARS_OF 7th House Ruling Planet OR Dasa_Dates of PLANETS WITH STARS_OF VENUS) AND Member_Age >= 21 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Planets in nakshatras of marriage house rulers OR VENUS stars"}}, {"name": "Planets in Marriage Houses", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"(Dasa_Dates of PLANETS IN 2nd House OR Dasa_Dates of PLANETS IN 7th House) AND Member_Age >= 21 AND <= 50\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Planets located in 2nd house OR 7th house dasha periods"}}, {"name": "Comprehensive Marriage Analysis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"(7th House Ruling Planet Dasa_Dates OR Dasa_Dates of PLANETS IN 7th House OR Dasa_Dates of PLANETS WITH STARS_OF 7th House Ruling Planet) AND Member_Age >= 30 AND <= 50 AND PREDICTION_DURATION = 5\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Comprehensive 7th house analysis: ruling planet, planets in house, and nakshatra connections"}}], "description": "Complex queries combining stars, houses, and multiple conditions"}, {"name": "Age and Duration Variations", "item": [{"name": "Young Age Marriage Prediction", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"(7th House Ruling Planet Dasa_Dates OR VENUS Dasa_Dates) AND Member_Age >= 18 AND <= 25 AND PREDICTION_DURATION = 3\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Marriage prediction for young age group (18-25) with 3-year duration"}}, {"name": "Middle Age Marriage Prediction", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"(2nd House Ruling Planet Dasa_Dates OR 7th House Ruling Planet Dasa_Dates) AND Member_Age >= 30 AND <= 45 AND PREDICTION_DURATION = 5\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Marriage prediction for middle age group (30-45) with 5-year duration"}}, {"name": "Extended Duration Analysis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"7th House Ruling Planet Dasa_Dates AND Member_Age >= 40 AND PREDICTION_DURATION = 10\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Extended 10-year prediction duration for comprehensive analysis"}}, {"name": "Minimum Age Only", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"1\",\n  \"member_profile_id\": \"1\",\n  \"query\": \"VENUS Dasa_Dates AND Member_Age >= 21\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}, "description": "Test with minimum age only (no maximum age limit)"}}], "description": "Queries with different age ranges and prediction durations"}], "variable": [{"key": "base_url", "value": "http://localhost:5003", "description": "Base URL for Fortune Lens API server"}]}