"""
Check MongoDB Data

This script inspects the MongoDB database to see what data is actually available
for the rule engine to work with.

Version: 2.0
Author: Fortune Lens Team
Last Updated: 2025-07-31
"""

import sys
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens')

from astro_insights_pro.app.extensions import mongo
from astro_insights_pro.app import create_app
import json
from pprint import pprint

def check_mongodb_collections():
    """Check what collections exist in MongoDB"""
    
    print("🔍 Checking MongoDB Collections")
    print("=" * 40)
    
    try:
        # Get all collection names
        collections = mongo.db.list_collection_names()
        
        print(f"📊 Found {len(collections)} collections:")
        for i, collection in enumerate(collections, 1):
            count = mongo.db[collection].count_documents({})
            print(f"   {i}. {collection}: {count} documents")
        
        return collections
        
    except Exception as e:
        print(f"❌ Error checking collections: {e}")
        return []


def check_user_member_astro_data():
    """Check user_member_astro_profile_data collection"""
    
    print("\n🎯 Checking user_member_astro_profile_data Collection")
    print("=" * 55)
    
    try:
        collection = mongo.db.user_member_astro_profile_data
        
        # Get total count
        total_count = collection.count_documents({})
        print(f"📊 Total documents: {total_count}")
        
        if total_count == 0:
            print("⚠️ No documents found in user_member_astro_profile_data")
            return
        
        # Get sample document
        sample_doc = collection.find_one()
        
        if sample_doc:
            print(f"\n📋 Sample Document Structure:")
            print(f"   user_profile_id: {sample_doc.get('user_profile_id', 'Not found')}")
            print(f"   member_profile_id: {sample_doc.get('member_profile_id', 'Not found')}")
            
            # Check chart_data structure
            chart_data = sample_doc.get('chart_data', {})
            if chart_data:
                print(f"   chart_data keys: {list(chart_data.keys())}")
                
                # Check D1 chart data
                d1_data = chart_data.get('d1', {})
                if d1_data:
                    print(f"   d1 chart keys: {list(d1_data.keys())}")
                    
                    # Check dashas
                    if 'dashas' in d1_data:
                        dashas = d1_data['dashas']
                        print(f"   dashas type: {type(dashas)}")
                        if isinstance(dashas, list):
                            print(f"   dashas count: {len(dashas)}")
                            if dashas:
                                print(f"   sample dasha: {dashas[0] if dashas else 'None'}")
                        elif isinstance(dashas, dict):
                            print(f"   dashas keys: {list(dashas.keys())}")
                    
                    # Check bhukti_dasha field
                    if 'bhukti_dasha' in d1_data:
                        bhukti_dasha = d1_data['bhukti_dasha']
                        print(f"   bhukti_dasha type: {type(bhukti_dasha)}")
                        if isinstance(bhukti_dasha, list):
                            print(f"   bhukti_dasha count: {len(bhukti_dasha)}")
                        elif isinstance(bhukti_dasha, dict):
                            print(f"   bhukti_dasha keys: {list(bhukti_dasha.keys())}")
            
            # Get all unique user_profile_ids
            unique_users = collection.distinct("user_profile_id")
            print(f"\n👥 Unique user_profile_ids: {unique_users[:10]}{'...' if len(unique_users) > 10 else ''}")
            
            # Get all unique member_profile_ids
            unique_members = collection.distinct("member_profile_id")
            print(f"👤 Unique member_profile_ids: {unique_members}")
        
    except Exception as e:
        print(f"❌ Error checking user_member_astro_profile_data: {e}")


def check_specific_user_data(user_id, member_id):
    """Check data for a specific user and member"""
    
    print(f"\n🔍 Checking Data for User {user_id}, Member {member_id}")
    print("=" * 50)
    
    try:
        collection = mongo.db.user_member_astro_profile_data
        
        # Find specific document
        doc = collection.find_one({
            "user_profile_id": user_id,
            "member_profile_id": member_id
        })
        
        if doc:
            print(f"✅ Found document for user {user_id}, member {member_id}")
            
            # Check chart_data
            chart_data = doc.get('chart_data', {})
            if chart_data:
                print(f"📊 Chart data available: {list(chart_data.keys())}")
                
                # Check D1 data specifically
                d1_data = chart_data.get('d1', {})
                if d1_data:
                    print(f"📈 D1 chart data keys: {list(d1_data.keys())}")
                    
                    # Check for dasha data
                    dashas = d1_data.get('dashas', [])
                    bhukti_dasha = d1_data.get('bhukti_dasha', [])
                    
                    print(f"🎯 Dasha Information:")
                    print(f"   dashas: {type(dashas)} with {len(dashas) if isinstance(dashas, (list, dict)) else 'unknown'} items")
                    print(f"   bhukti_dasha: {type(bhukti_dasha)} with {len(bhukti_dasha) if isinstance(bhukti_dasha, (list, dict)) else 'unknown'} items")
                    
                    # Show sample dasha data
                    if isinstance(dashas, list) and dashas:
                        print(f"   Sample dasha entry: {dashas[0]}")
                    elif isinstance(dashas, dict) and dashas:
                        first_key = list(dashas.keys())[0]
                        print(f"   Sample dasha entry ({first_key}): {dashas[first_key]}")
                    
                    if isinstance(bhukti_dasha, list) and bhukti_dasha:
                        print(f"   Sample bhukti entry: {bhukti_dasha[0]}")
                    elif isinstance(bhukti_dasha, dict) and bhukti_dasha:
                        first_key = list(bhukti_dasha.keys())[0]
                        print(f"   Sample bhukti entry ({first_key}): {bhukti_dasha[first_key]}")
                
                else:
                    print("⚠️ No D1 chart data found")
            else:
                print("⚠️ No chart_data found")
        else:
            print(f"❌ No document found for user {user_id}, member {member_id}")
    
    except Exception as e:
        print(f"❌ Error checking specific user data: {e}")


def find_users_with_dasha_data():
    """Find users that actually have dasha data"""
    
    print(f"\n🔎 Finding Users with Dasha Data")
    print("=" * 40)
    
    try:
        collection = mongo.db.user_member_astro_profile_data
        
        # Find documents with dasha data
        pipeline = [
            {
                "$match": {
                    "$or": [
                        {"chart_data.d1.dashas": {"$exists": True, "$ne": []}},
                        {"chart_data.d1.bhukti_dasha": {"$exists": True, "$ne": []}}
                    ]
                }
            },
            {
                "$project": {
                    "user_profile_id": 1,
                    "member_profile_id": 1,
                    "has_dashas": {"$ifNull": ["$chart_data.d1.dashas", []]},
                    "has_bhukti": {"$ifNull": ["$chart_data.d1.bhukti_dasha", []]}
                }
            },
            {"$limit": 10}
        ]
        
        users_with_data = list(collection.aggregate(pipeline))
        
        if users_with_data:
            print(f"✅ Found {len(users_with_data)} users with dasha data:")
            for user in users_with_data:
                user_id = user.get('user_profile_id')
                member_id = user.get('member_profile_id')
                has_dashas = len(user.get('has_dashas', [])) if isinstance(user.get('has_dashas'), list) else 'dict'
                has_bhukti = len(user.get('has_bhukti', [])) if isinstance(user.get('has_bhukti'), list) else 'dict'
                
                print(f"   👤 User {user_id}, Member {member_id}: dashas={has_dashas}, bhukti={has_bhukti}")
            
            # Return first user for detailed testing
            return users_with_data[0] if users_with_data else None
        else:
            print("❌ No users found with dasha data")
            return None
    
    except Exception as e:
        print(f"❌ Error finding users with dasha data: {e}")
        return None


if __name__ == "__main__":
    print("🔍 MongoDB Data Inspection")
    print("=" * 50)
    
    # Create app context
    app = create_app()
    
    with app.app_context():
        # Check collections
        collections = check_mongodb_collections()
        
        # Check main astro data collection
        if 'user_member_astro_profile_data' in collections:
            check_user_member_astro_data()
            
            # Check specific user (the one we've been testing)
            check_specific_user_data(100001, 1)
            
            # Find users with actual dasha data
            user_with_data = find_users_with_dasha_data()
            
            if user_with_data:
                print(f"\n🎯 Testing with User who has Data:")
                check_specific_user_data(
                    user_with_data.get('user_profile_id'),
                    user_with_data.get('member_profile_id')
                )
        else:
            print("❌ user_member_astro_profile_data collection not found")
    
    print(f"\n🏁 MongoDB Inspection Complete")
    print(f"   Use the user IDs found above to test the dynamic API")
    print(f"   with real data from MongoDB.")
