# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Project specific
logs/
data/
charts/*.png
outputs/

# Test and demo files
test_*.py
*_test.py
*_demo.py
*_verification.py
*_load_data.py
create_test_*.py
verify_*.py
validate_*.py

# Documentation files (excessive)
*_COMPLETE.md
*_GUIDE.md
*_SUMMARY.md
*_DOCUMENTATION.md
*_ANALYSIS.md
*_IMPLEMENTATION.md
*_TESTING.md
*_FIXES.md
*_RESULTS.md
*_EXPLANATION.md
*_CORRECTIONS.md
*_FUNCTIONS.md
better_*.md
improved_*.md

# Postman collections (excessive)
*_Postman.json
*Postman*.json
*.postman_collection.json
!Fortune_Lens_Complete_API_Collection.postman_collection.json

# Result files
verification_*.json
pattern_*.json
*_results.json

# OS specific
.DS_Store
Thumbs.db
