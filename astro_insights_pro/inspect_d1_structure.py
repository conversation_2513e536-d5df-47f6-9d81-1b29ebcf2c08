"""
Inspect D1 Chart Structure

This script inspects the actual D1 chart data structure in MongoDB
to understand how dasha data is stored.

Version: 2.0
Author: Fortune Lens Team
Last Updated: 2025-07-31
"""

import sys
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens')

from astro_insights_pro.app.extensions import mongo
from astro_insights_pro.app import create_app
import json
from pprint import pprint

def inspect_d1_structure():
    """Inspect the D1 chart data structure"""
    
    print("🔍 Inspecting D1 Chart Data Structure")
    print("=" * 45)
    
    try:
        collection = mongo.db.user_member_astro_profile_data
        
        # Get a sample document
        doc = collection.find_one({
            "user_profile_id": 100001,
            "member_profile_id": 1
        })
        
        if doc:
            print(f"✅ Found document for user 100001, member 1")
            
            chart_data = doc.get('chart_data', {})
            print(f"📊 Chart data keys: {list(chart_data.keys())}")
            
            # Check D1 data (uppercase)
            d1_data = chart_data.get('D1', {})
            if d1_data:
                print(f"\n📈 D1 Chart Data Structure:")
                print(f"   D1 keys: {list(d1_data.keys())}")
                
                # Print the entire D1 structure (first few levels)
                print(f"\n📋 Complete D1 Structure:")
                for key, value in d1_data.items():
                    if isinstance(value, dict):
                        print(f"   {key}: dict with keys {list(value.keys())}")
                        
                        # If it's houses, show sample house
                        if key == 'houses' and value:
                            first_house = list(value.keys())[0]
                            print(f"      Sample house ({first_house}): {value[first_house]}")
                        
                        # If it's dashas, show structure
                        elif key == 'dashas' and value:
                            print(f"      Dashas structure: {type(value)} with {len(value)} items")
                            if isinstance(value, dict):
                                first_dasha_key = list(value.keys())[0]
                                print(f"      Sample dasha key: {first_dasha_key}")
                                print(f"      Sample dasha value: {value[first_dasha_key]}")
                            elif isinstance(value, list):
                                print(f"      Sample dasha item: {value[0] if value else 'Empty'}")
                        
                        # Show any other dict structures
                        elif isinstance(value, dict) and value:
                            sample_key = list(value.keys())[0]
                            print(f"      Sample {key} ({sample_key}): {value[sample_key]}")
                    
                    elif isinstance(value, list):
                        print(f"   {key}: list with {len(value)} items")
                        if value:
                            print(f"      Sample item: {value[0]}")
                    else:
                        print(f"   {key}: {type(value)} = {value}")
                
                # Look specifically for dasha-related fields
                print(f"\n🎯 Looking for Dasha Data:")
                dasha_fields = ['dashas', 'dasha', 'bhukti_dasha', 'maha_dasha', 'periods', 'timeline']
                for field in dasha_fields:
                    if field in d1_data:
                        data = d1_data[field]
                        print(f"   ✅ Found {field}: {type(data)}")
                        if isinstance(data, (list, dict)) and data:
                            if isinstance(data, list):
                                print(f"      Length: {len(data)}")
                                print(f"      Sample: {data[0] if data else 'Empty'}")
                            else:
                                print(f"      Keys: {list(data.keys())}")
                                first_key = list(data.keys())[0]
                                print(f"      Sample ({first_key}): {data[first_key]}")
                    else:
                        print(f"   ❌ No {field} field")
            else:
                print("❌ No D1 data found")
        else:
            print("❌ No document found")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


def search_for_dasha_data():
    """Search for any dasha-related data in the collection"""
    
    print(f"\n🔎 Searching for Dasha Data Patterns")
    print("=" * 40)
    
    try:
        collection = mongo.db.user_member_astro_profile_data
        
        # Search for documents with various dasha field patterns
        search_patterns = [
            {"chart_data.D1.dashas": {"$exists": True}},
            {"chart_data.D1.dasha": {"$exists": True}},
            {"chart_data.D1.bhukti_dasha": {"$exists": True}},
            {"chart_data.D1.maha_dasha": {"$exists": True}},
            {"chart_data.D1.periods": {"$exists": True}},
            {"chart_data.D1.timeline": {"$exists": True}}
        ]
        
        for i, pattern in enumerate(search_patterns, 1):
            count = collection.count_documents(pattern)
            field_name = list(pattern.keys())[0].split('.')[-1]
            print(f"   {i}. Documents with '{field_name}': {count}")
            
            if count > 0:
                # Get a sample document
                sample = collection.find_one(pattern)
                if sample:
                    field_path = list(pattern.keys())[0].split('.')
                    data = sample
                    for part in field_path:
                        data = data.get(part, {})
                    
                    print(f"      Sample {field_name} data: {type(data)}")
                    if isinstance(data, (list, dict)) and data:
                        if isinstance(data, list):
                            print(f"      Length: {len(data)}, First item: {data[0] if data else 'Empty'}")
                        else:
                            print(f"      Keys: {list(data.keys())[:5]}{'...' if len(data.keys()) > 5 else ''}")
    
    except Exception as e:
        print(f"❌ Error searching for dasha data: {e}")


def check_raw_document():
    """Check the raw document structure"""
    
    print(f"\n📄 Raw Document Structure")
    print("=" * 30)
    
    try:
        collection = mongo.db.user_member_astro_profile_data
        
        # Get raw document
        doc = collection.find_one({
            "user_profile_id": 100001,
            "member_profile_id": 1
        })
        
        if doc:
            print(f"📋 Top-level keys: {list(doc.keys())}")
            
            # Show chart_data structure
            chart_data = doc.get('chart_data', {})
            if chart_data:
                print(f"📊 chart_data keys: {list(chart_data.keys())}")
                
                # Show D1 structure in detail
                d1 = chart_data.get('D1', {})
                if d1:
                    print(f"📈 D1 structure (showing first 3 levels):")
                    print(json.dumps(d1, indent=2, default=str)[:2000] + "..." if len(str(d1)) > 2000 else json.dumps(d1, indent=2, default=str))
    
    except Exception as e:
        print(f"❌ Error checking raw document: {e}")


if __name__ == "__main__":
    print("🔍 D1 Chart Structure Inspection")
    print("=" * 50)
    
    # Create app context
    app = create_app()
    
    with app.app_context():
        inspect_d1_structure()
        search_for_dasha_data()
        check_raw_document()
    
    print(f"\n🏁 D1 Structure Inspection Complete")
    print(f"   This will help us understand how to extract dasha data correctly.")
