"""
Debug API Call

This script debugs the API call to see exactly what's happening
in the data fetching and processing pipeline.

Version: 2.0
Author: Fortune Lens Team
Last Updated: 2025-07-31
"""

import sys
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens')

from astro_insights_pro.app.extensions import mongo
from astro_insights_pro.app import create_app
from astro_insights_pro.app.services.rule_engine.api.main_rule_engine import _fetch_chart_data_from_mongodb
import json

def debug_chart_data_fetching():
    """Debug the chart data fetching process"""
    
    print("🔍 Debugging Chart Data Fetching")
    print("=" * 40)
    
    try:
        # Test the _fetch_chart_data_from_mongodb function directly
        chart_data = _fetch_chart_data_from_mongodb(100001, 1, "D1")
        
        if chart_data:
            print(f"✅ Chart data fetched successfully")
            print(f"   Keys: {list(chart_data.keys())}")
            
            # Check chart_data structure
            if 'chart_data' in chart_data:
                chart_info = chart_data['chart_data']
                print(f"   chart_data keys: {list(chart_info.keys())}")
                
                # Check D1 data
                if 'D1' in chart_info:
                    d1_data = chart_info['D1']
                    print(f"   D1 keys: {list(d1_data.keys())}")
                    
                    # Check dashas
                    if 'dashas' in d1_data:
                        dashas = d1_data['dashas']
                        print(f"   dashas: {type(dashas)} with {len(dashas)} items")
                        
                        if isinstance(dashas, list) and dashas:
                            print(f"   Sample dasha: {dashas[0]}")
                        elif isinstance(dashas, dict):
                            print(f"   Dasha keys: {list(dashas.keys())}")
                    else:
                        print(f"   ❌ No 'dashas' key in D1 data")
                else:
                    print(f"   ❌ No 'D1' key in chart_data")
            else:
                print(f"   ❌ No 'chart_data' key")
        else:
            print(f"❌ No chart data returned")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


def debug_dasha_processor():
    """Debug the dasha processor logic"""
    
    print(f"\n🔍 Debugging Dasha Processor")
    print("=" * 35)
    
    try:
        from astro_insights_pro.app.services.rule_engine.processors.dasha_processor import DashaProcessor
        
        processor = DashaProcessor()
        
        # Get chart data
        chart_data = _fetch_chart_data_from_mongodb(100001, 1, "D1")
        
        if chart_data:
            # Test planet bhukti periods extraction
            venus_periods = processor._get_planet_bhukti_periods("VENUS", chart_data)
            print(f"✅ VENUS bhukti periods: {len(venus_periods)}")
            
            if venus_periods:
                print(f"   Sample period: {venus_periods[0]}")
            
            # Test planet dasa periods extraction
            venus_dasa = processor._get_planet_dasa_periods("VENUS", chart_data)
            print(f"✅ VENUS dasa periods: {len(venus_dasa)}")
            
            if venus_dasa:
                print(f"   Sample dasa: {venus_dasa[0]}")
            
            # Test combined periods
            combined = venus_periods + venus_dasa
            print(f"✅ Combined VENUS periods: {len(combined)}")
            
            # Test deduplication
            deduplicated = processor._remove_duplicate_dasha_periods(combined)
            print(f"✅ After deduplication: {len(deduplicated)}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


def debug_full_api_flow():
    """Debug the full API flow"""
    
    print(f"\n🔍 Debugging Full API Flow")
    print("=" * 35)
    
    try:
        from astro_insights_pro.app.services.rule_engine.api.main_rule_engine import process_rule_engine_request
        
        # Test data
        test_data = {
            'user_profile_id': 100001,
            'member_profile_id': 1,
            'query': 'VENUS Bhukti_Dates',
            'chart_type': 'D1'
        }
        
        print(f"📋 Testing with data: {test_data}")
        
        # Call the main function
        result = process_rule_engine_request(test_data)
        
        print(f"✅ API function returned")
        print(f"   Result type: {type(result)}")
        print(f"   Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        if isinstance(result, dict):
            if 'dasha_periods' in result:
                periods = result['dasha_periods']
                print(f"   Dasha periods: {len(periods)}")
                
                if periods:
                    print(f"   Sample period: {periods[0]}")
            
            if 'success' in result:
                print(f"   Success: {result['success']}")
            
            if 'error' in result:
                print(f"   Error: {result['error']}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🔍 API Debug Suite")
    print("=" * 50)
    
    # Create app context
    app = create_app()
    
    with app.app_context():
        debug_chart_data_fetching()
        debug_dasha_processor()
        debug_full_api_flow()
    
    print(f"\n🏁 Debug Complete")
    print(f"   This should help identify where the issue is in the pipeline.")
