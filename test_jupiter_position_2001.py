#!/usr/bin/env python3
"""
Test Jupiter position and aspect calculation for 2001-05-26 00:00:00
"""

import sys
import os
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

from app.services.chart_service import generate_chart

def test_jupiter_position_multiple_dates():
    """Test Jupiter position for multiple dates to verify KOCHARAM calculation"""

    # Test dates around the predicted transit
    test_dates = [
        ('2001-05-26', 'Age start range date'),
        ('2002-07-01', 'July 2002 - suspected correct date'),
        ('2002-09-27', 'KOCHARAM predicted date'),
        ('2003-01-01', 'January 2003 - mid transit')
    ]

    for date_str, description in test_dates:
        print(f"\n=== TESTING JUPITER POSITION ON {date_str} ({description}) ===")

        # Birth data for the specific date we want to check
        # Convert YYYY-MM-DD to DD-MM-YYYY
        year, month, day = date_str.split('-')
        birth_data = {
            'user_birthdate': f'{day}-{month}-{year}',  # DD-MM-YYYY format
            'user_birthtime': '00:00:00',    # HH:MM:SS format
            'user_birthplace': 'Cuddalore',
            'user_state': 'Tamil Nadu',
            'user_country': 'India'
        }

        test_single_date(birth_data, date_str, description)

def test_single_date(birth_data, date_str, description):
    """Test Jupiter position for a single date"""
    
    try:
        # Generate chart for this specific date
        chart_data = generate_chart(birth_data, chart_types=['D1'])

        print(f"📊 Chart data keys: {list(chart_data.keys())}")

        if 'error' in chart_data:
            print(f"❌ Error generating chart: {chart_data['error']}")
            return

        # Extract D1 chart data
        d1_chart = chart_data.get('D1', {})
        print(f"📊 D1 chart keys: {list(d1_chart.keys())}")

        planets_precise = d1_chart.get('planets_precise', {})
        print(f"📊 Planets available: {list(planets_precise.keys())}")

        if 'jupiter' not in planets_precise:
            print("❌ Jupiter data not found in chart")
            return

        jupiter_data = planets_precise['jupiter']

        print(f"🪐 JUPITER POSITION ON {date_str}:")
        print(f"   Sign: {jupiter_data.get('sign', 'N/A')}")
        print(f"   Degree: {jupiter_data.get('degree', 'N/A'):.2f}°")

        # Calculate longitude from sign and degree
        sign = jupiter_data.get('sign', 'Unknown')
        degree = jupiter_data.get('degree', 0)

        # Map sign to longitude base
        sign_to_longitude = {
            'Mesham': 0, 'Rishabam': 30, 'Midunam': 60, 'Kadagam': 90,
            'Simmam': 120, 'Kanni': 150, 'Thulam': 180, 'Virichigam': 210,
            'Dhanusu': 240, 'Magaram': 270, 'Kumbam': 300, 'Meenam': 330
        }

        longitude_base = sign_to_longitude.get(sign, 0)
        longitude = longitude_base + degree

        print(f"   Calculated Longitude: {longitude:.2f}° (from {sign} + {degree:.2f}°)")

        # Calculate aspect angles
        
        print(f"\n🎯 JUPITER ASPECT CALCULATIONS:")
        print(f"   Current Position: {longitude:.2f}°")
        
        # Jupiter aspects: 5th, 7th, 9th houses from its position
        # Formula: Aspect angle = (Current position + (aspect_number - 1) * 30°) % 360°
        aspect_5th = (longitude + 4*30) % 360  # 5th aspect = +120°
        aspect_7th = (longitude + 6*30) % 360  # 7th aspect = +180°
        aspect_9th = (longitude + 8*30) % 360  # 9th aspect = +240°
        
        print(f"   5th Aspect Angle: {aspect_5th:.2f}°")
        print(f"   7th Aspect Angle: {aspect_7th:.2f}°")
        print(f"   9th Aspect Angle: {aspect_9th:.2f}°")
        
        # Check which houses these aspects fall into
        # User's 7th house is Meenam (330°-360°)
        target_house_start = 330
        target_house_end = 360
        
        print(f"\n🏠 TARGET HOUSE (7th House - Meenam): {target_house_start}°-{target_house_end}°")
        
        aspects = [
            ('5th', aspect_5th),
            ('7th', aspect_7th), 
            ('9th', aspect_9th)
        ]
        
        aspecting_7th_house = False
        for aspect_name, aspect_angle in aspects:
            in_target = target_house_start <= aspect_angle <= target_house_end
            status = '✅ ASPECTING 7th HOUSE' if in_target else '❌ NOT ASPECTING 7th HOUSE'
            print(f"   {aspect_name} Aspect ({aspect_angle:.2f}°): {status}")
            if in_target:
                aspecting_7th_house = True
        
        print(f"\n🔍 OVERALL RESULT FOR {date_str}:")
        if aspecting_7th_house:
            print(f"✅ Jupiter IS aspecting 7th house on {date_str}")
            print("✅ This is a favorable period!")
        else:
            print(f"❌ Jupiter is NOT aspecting 7th house on {date_str}")

        # Additional verification: Check what sign Jupiter is in
        jupiter_sign = jupiter_data.get('sign', 'Unknown')
        print(f"📍 Jupiter is in {jupiter_sign} sign")
        
        # Calculate correct transit ranges with degree validation
        print(f"\n🧮 CORRECTED ASPECT CALCULATION WITH DEGREE RANGES:")
        print(f"   For Jupiter to aspect 7th house (Meenam: 330°-360°):")

        # Reverse calculation: which positions would aspect 7th house
        for aspect_num, aspect_name in [(5, '5th'), (7, '7th'), (9, '9th')]:
            # Calculate the house where Jupiter should be to aspect 7th house
            required_house_start = (330 - (aspect_num-1)*30) % 360
            required_house_end = (360 - (aspect_num-1)*30) % 360

            # Apply degree range validation: 0-5° for start, 25-30° for end
            # Transit START: Jupiter enters house at 0-5° range
            transit_start_min = required_house_start + 0  # House start + 0°
            transit_start_max = required_house_start + 5  # House start + 5°

            # Transit END: Jupiter exits house at 25-30° range
            transit_end_min = required_house_start + 25  # House start + 25°
            transit_end_max = required_house_start + 30  # House start + 30°

            # Handle wrap-around for houses crossing 0°
            if required_house_end < required_house_start:
                print(f"   {aspect_name} aspect: Jupiter house range {required_house_start:.0f}°-360° OR 0°-{required_house_end:.0f}°")
                print(f"     Transit START range: {transit_start_min:.0f}°-{transit_start_max:.0f}° OR 0°-5°")
                print(f"     Transit END range: {transit_end_min:.0f}°-{transit_end_max:.0f}° OR 25°-30°")
            else:
                print(f"   {aspect_name} aspect: Jupiter house range {required_house_start:.0f}°-{required_house_end:.0f}°")
                print(f"     Transit START range: {transit_start_min:.0f}°-{transit_start_max:.0f}°")
                print(f"     Transit END range: {transit_end_min:.0f}°-{transit_end_max:.0f}°")

            # Check current position against corrected ranges
            current_in_start_range = False
            current_in_end_range = False
            current_in_house = False

            if required_house_end < required_house_start:
                # House crosses 0° boundary
                current_in_house = longitude >= required_house_start or longitude <= required_house_end
                current_in_start_range = (transit_start_min <= longitude <= transit_start_max) or (0 <= longitude <= 5)
                current_in_end_range = (transit_end_min <= longitude <= transit_end_max) or (25 <= longitude <= 30)
            else:
                # Normal house range
                current_in_house = required_house_start <= longitude <= required_house_end
                current_in_start_range = transit_start_min <= longitude <= transit_start_max
                current_in_end_range = transit_end_min <= longitude <= transit_end_max

            # Determine transit status
            if current_in_start_range:
                print(f"     ✅ Current position {longitude:.2f}° is in TRANSIT START range")
            elif current_in_end_range:
                print(f"     ✅ Current position {longitude:.2f}° is in TRANSIT END range")
            elif current_in_house:
                print(f"     ✅ Current position {longitude:.2f}° is in ACTIVE TRANSIT (mid-range)")
            else:
                print(f"     ❌ Current position {longitude:.2f}° is NOT in transit range")

            # Calculate degree within house for current position
            if current_in_house:
                if required_house_end < required_house_start and longitude <= required_house_end:
                    # Position is in the 0°-required_house_end part
                    degree_in_house = longitude
                else:
                    # Normal calculation
                    degree_in_house = longitude - required_house_start
                print(f"     📍 Degree within house: {degree_in_house:.1f}°")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_jupiter_position_multiple_dates()
