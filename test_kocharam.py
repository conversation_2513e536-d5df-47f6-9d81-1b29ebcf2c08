#!/usr/bin/env python3
"""
Test script to verify KOCHARAM filter calculations using API simulation
"""

import requests
import json

def test_kocharam_api():
    """Test KOCHARAM calculation using API endpoint"""

    # API endpoint
    base_url = "http://localhost:5003"
    endpoint = f"{base_url}/api/rule-engine/"

    # Test data
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "VENUS Bhukti_Dates AND KOCHARAM_FILTER(VENUS in 7th_House)",
        "chart_type": "D1"
    }

    print("🔍 Testing KOCHARAM Filter via API")
    print("=" * 50)
    print(f"Endpoint: {endpoint}")
    print(f"Request Data: {json.dumps(test_data, indent=2)}")
    print()

    try:
        # Make API request
        print("📡 Making API request...")
        response = requests.post(endpoint, json=test_data, timeout=30)

        print(f"Status Code: {response.status_code}")
        print()

        if response.status_code == 200:
            result = response.json()
            print("✅ API Response:")
            print(json.dumps(result, indent=2))

            # Check for KOCHARAM specific results
            if 'result' in result and 'kocharam_result' in result['result']:
                kocharam_result = result['result']['kocharam_result']
                print("\n🎯 KOCHARAM Analysis:")
                print(f"Filter Applied: {kocharam_result.get('filter_applied', 'Unknown')}")
                print(f"Periods Analyzed: {kocharam_result.get('periods_analyzed', 0)}")
                print(f"Periods Passed: {kocharam_result.get('periods_passed', 0)}")
                print(f"Success Rate: {kocharam_result.get('success_rate', 0)}%")

                if 'detailed_analysis' in kocharam_result:
                    print(f"Detailed Analysis Items: {len(kocharam_result['detailed_analysis'])}")

                    # Show first analysis item
                    if kocharam_result['detailed_analysis']:
                        first_analysis = kocharam_result['detailed_analysis'][0]
                        print("\n📊 First Analysis Item:")
                        print(json.dumps(first_analysis, indent=2))

        else:
            print("❌ API Error:")
            print(response.text)

    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure the server is running on localhost:5003")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_simple_kocharam():
    """Test simple KOCHARAM condition without full dasha query"""

    base_url = "http://localhost:5003"
    endpoint = f"{base_url}/api/rule-engine/"

    # Simple test data
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "VENUS in 7th_House",
        "chart_type": "D1"
    }

    print("\n🔍 Testing Simple KOCHARAM Condition")
    print("=" * 50)
    print(f"Query: {test_data['query']}")

    try:
        response = requests.post(endpoint, json=test_data, timeout=30)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ Simple Query Response:")
            print(json.dumps(result, indent=2))
        else:
            print("❌ Error Response:")
            print(response.text)

    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 Starting KOCHARAM Filter Tests")
    print("Make sure the server is running: python run.py")
    print()

    # Test 1: Full KOCHARAM with dasha dates
    test_kocharam_api()

    # Test 2: Simple KOCHARAM condition
    test_simple_kocharam()

    print("\n🎯 All tests completed!")
