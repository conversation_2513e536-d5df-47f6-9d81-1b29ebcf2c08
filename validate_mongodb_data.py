#!/usr/bin/env python3
"""
MongoDB Data Validation Script for Fortune Lens
Validates data integrity and chart generation after Excel upload
"""

import pymongo
from pymongo import MongoClient
import pandas as pd
from datetime import datetime
import json

class MongoDBValidator:
    def __init__(self, mongodb_uri='mongodb://localhost:27017/', db_name='fortune_lens'):
        """Initialize validator with MongoDB connection"""
        self.client = MongoClient(mongodb_uri)
        self.db = self.client[db_name]
        self.collections = {
            'user_profile': 'user_profile',
            'member_profile': 'member_profile',
            'astro_data': 'user_member_astro_profile_data'
        }
    
    def validate_collection_counts(self):
        """Validate document counts across collections"""
        print("=== COLLECTION COUNTS VALIDATION ===")
        
        counts = {}
        for name, collection in self.collections.items():
            count = self.db[collection].count_documents({})
            counts[name] = count
            print(f"{collection}: {count} documents")
        
        # Check relationships
        user_count = counts['user_profile']
        member_count = counts['member_profile']
        astro_count = counts['astro_data']
        
        print(f"\nRelationship Analysis:")
        print(f"Users: {user_count}")
        print(f"Members: {member_count}")
        print(f"Astro profiles: {astro_count}")
        print(f"Average members per user: {member_count/user_count if user_count > 0 else 0:.2f}")
        print(f"Member-Astro ratio: {astro_count/member_count if member_count > 0 else 0:.2f}")
        
        return counts
    
    def validate_user_profiles(self):
        """Validate user profile data integrity"""
        print("\n=== USER PROFILES VALIDATION ===")
        
        users = list(self.db[self.collections['user_profile']].find())
        
        issues = []
        for user in users:
            user_id = user.get('user_profile_id')
            
            # Check required fields
            required_fields = ['user_profile_id', 'email', 'name', 'unique_key']
            for field in required_fields:
                if field not in user or not user[field]:
                    issues.append(f"User {user_id}: Missing {field}")
            
            # Check email format
            email = user.get('email', '')
            if email and '@' not in email:
                issues.append(f"User {user_id}: Invalid email format")
        
        print(f"Validated {len(users)} user profiles")
        if issues:
            print(f"Issues found: {len(issues)}")
            for issue in issues[:10]:  # Show first 10 issues
                print(f"  - {issue}")
        else:
            print("✓ All user profiles are valid")
        
        return len(issues) == 0
    
    def validate_member_profiles(self):
        """Validate member profile data integrity"""
        print("\n=== MEMBER PROFILES VALIDATION ===")
        
        members = list(self.db[self.collections['member_profile']].find())
        
        issues = []
        user_member_map = {}
        
        for member in members:
            user_id = member.get('user_profile_id')
            member_id = member.get('member_profile_id')
            
            # Track user-member relationships
            if user_id not in user_member_map:
                user_member_map[user_id] = []
            user_member_map[user_id].append(member_id)
            
            # Check required fields
            required_fields = ['user_profile_id', 'member_profile_id', 'name', 'birth_date', 'birth_time', 'birth_place']
            for field in required_fields:
                if field not in member or not member[field]:
                    issues.append(f"User {user_id}, Member {member_id}: Missing {field}")
            
            # Check coordinates
            lat = member.get('latitude')
            lon = member.get('longitude')
            if lat is None or lon is None:
                issues.append(f"User {user_id}, Member {member_id}: Missing coordinates")
            elif not (-90 <= lat <= 90) or not (-180 <= lon <= 180):
                issues.append(f"User {user_id}, Member {member_id}: Invalid coordinates")
        
        print(f"Validated {len(members)} member profiles")
        
        # Check user-member relationships
        print(f"\nUser-Member Relationships:")
        for user_id, member_ids in sorted(user_member_map.items()):
            print(f"  User {user_id}: Members {sorted(member_ids)}")
        
        if issues:
            print(f"\nIssues found: {len(issues)}")
            for issue in issues[:10]:
                print(f"  - {issue}")
        else:
            print("✓ All member profiles are valid")
        
        return len(issues) == 0, user_member_map
    
    def validate_astro_data(self):
        """Validate astrological data and chart generation"""
        print("\n=== ASTROLOGICAL DATA VALIDATION ===")
        
        astro_profiles = list(self.db[self.collections['astro_data']].find())
        
        issues = []
        chart_stats = {
            'total_profiles': len(astro_profiles),
            'with_chart_data': 0,
            'with_d1_chart': 0,
            'with_dashas': 0,
            'chart_types': set()
        }
        
        for astro in astro_profiles:
            user_id = astro.get('user_profile_id')
            member_id = astro.get('member_profile_id')
            
            # Check chart data existence
            chart_data = astro.get('chart_data', {})
            if not chart_data:
                issues.append(f"User {user_id}, Member {member_id}: No chart data")
                continue
            
            chart_stats['with_chart_data'] += 1
            
            # Check D1 chart (most important)
            d1_chart = chart_data.get('D1', {})
            if not d1_chart:
                issues.append(f"User {user_id}, Member {member_id}: No D1 chart")
                continue
            
            chart_stats['with_d1_chart'] += 1
            
            # Check D1 chart structure
            required_d1_fields = ['chart_info', 'houses', 'planets_precise', 'lagna']
            for field in required_d1_fields:
                if field not in d1_chart:
                    issues.append(f"User {user_id}, Member {member_id}: D1 missing {field}")
            
            # Check dashas in D1
            dashas = d1_chart.get('dashas', {})
            if dashas:
                chart_stats['with_dashas'] += 1
                
                # Validate dasha structure
                if 'maha_dasha' not in dashas or 'bhukti_dasha' not in dashas:
                    issues.append(f"User {user_id}, Member {member_id}: Incomplete dasha data")
            
            # Count chart types
            for chart_type in chart_data.keys():
                chart_stats['chart_types'].add(chart_type)
            
            # Check houses data
            houses = d1_chart.get('houses', [])
            if len(houses) != 12:
                issues.append(f"User {user_id}, Member {member_id}: D1 should have 12 houses, found {len(houses)}")
        
        print(f"Chart Statistics:")
        print(f"  Total astro profiles: {chart_stats['total_profiles']}")
        print(f"  With chart data: {chart_stats['with_chart_data']}")
        print(f"  With D1 chart: {chart_stats['with_d1_chart']}")
        print(f"  With dashas: {chart_stats['with_dashas']}")
        print(f"  Chart types found: {sorted(chart_stats['chart_types'])}")
        
        if issues:
            print(f"\nIssues found: {len(issues)}")
            for issue in issues[:10]:
                print(f"  - {issue}")
        else:
            print("✓ All astrological data is valid")
        
        return len(issues) == 0, chart_stats
    
    def validate_data_consistency(self, user_member_map):
        """Validate consistency between collections"""
        print("\n=== DATA CONSISTENCY VALIDATION ===")
        
        issues = []
        
        # Check if every user has corresponding members and astro data
        for user_id in user_member_map:
            member_ids = user_member_map[user_id]
            
            # Check astro data for each member
            for member_id in member_ids:
                astro_exists = self.db[self.collections['astro_data']].find_one({
                    'user_profile_id': user_id,
                    'member_profile_id': member_id
                })
                
                if not astro_exists:
                    issues.append(f"User {user_id}, Member {member_id}: Missing astro data")
        
        # Check for orphaned astro data
        astro_profiles = self.db[self.collections['astro_data']].find({}, {'user_profile_id': 1, 'member_profile_id': 1})
        for astro in astro_profiles:
            user_id = astro['user_profile_id']
            member_id = astro['member_profile_id']
            
            # Check if corresponding member profile exists
            member_exists = self.db[self.collections['member_profile']].find_one({
                'user_profile_id': user_id,
                'member_profile_id': member_id
            })
            
            if not member_exists:
                issues.append(f"Orphaned astro data: User {user_id}, Member {member_id}")
        
        if issues:
            print(f"Consistency issues found: {len(issues)}")
            for issue in issues[:10]:
                print(f"  - {issue}")
        else:
            print("✓ Data consistency is good")
        
        return len(issues) == 0
    
    def generate_validation_report(self):
        """Generate comprehensive validation report"""
        print("=" * 60)
        print("FORTUNE LENS MONGODB VALIDATION REPORT")
        print("=" * 60)
        print(f"Validation Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Run all validations
        counts = self.validate_collection_counts()
        user_valid = self.validate_user_profiles()
        member_valid, user_member_map = self.validate_member_profiles()
        astro_valid, chart_stats = self.validate_astro_data()
        consistency_valid = self.validate_data_consistency(user_member_map)
        
        # Overall summary
        print("\n" + "=" * 60)
        print("VALIDATION SUMMARY")
        print("=" * 60)
        
        all_valid = all([user_valid, member_valid, astro_valid, consistency_valid])
        
        print(f"✓ User Profiles: {'PASS' if user_valid else 'FAIL'}")
        print(f"✓ Member Profiles: {'PASS' if member_valid else 'FAIL'}")
        print(f"✓ Astrological Data: {'PASS' if astro_valid else 'FAIL'}")
        print(f"✓ Data Consistency: {'PASS' if consistency_valid else 'FAIL'}")
        print(f"\nOVERALL STATUS: {'PASS' if all_valid else 'FAIL'}")
        
        if all_valid:
            print("\n🎉 Database validation completed successfully!")
            print("All data is properly structured and chart generation is working.")
        else:
            print("\n⚠️  Database validation found issues.")
            print("Please review the issues above and fix them.")
        
        return all_valid
    
    def close(self):
        """Close MongoDB connection"""
        self.client.close()

def main():
    """Main function to run validation"""
    validator = MongoDBValidator()
    
    try:
        validator.generate_validation_report()
    finally:
        validator.close()

if __name__ == "__main__":
    main()
