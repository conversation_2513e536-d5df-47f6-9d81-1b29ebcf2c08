#!/usr/bin/env python3
"""
Test longitude-based sign validation specifically
"""

def longitude_to_sign_validation(longitude, target_sign):
    """Validate sign based on longitude ranges - Complete zodiac coverage"""
    sign_ranges = {
        "Mesham": (0, 30),      # Aries: 0-30°
        "Rishabam": (30, 60),   # Taurus: 30-60°
        "Midunam": (60, 90),    # Gemini: 60-90°
        "Kadagam": (90, 120),   # Cancer: 90-120°
        "Simham": (120, 150),   # Leo: 120-150°
        "Simmam": (120, 150),   # <PERSON> (alternate): 120-150°
        "Kanni": (150, 180),    # Virgo: 150-180°
        "Thulam": (180, 210),   # Libra: 180-210°
        "Virichigam": (210, 240), # Scorpio: 210-240°
        "Dhanusu": (240, 270),  # Sagittarius: 240-270°
        "MAGARAM": (270, 300),  # Capricorn: 270-300°
        "Kumbam": (300, 330),   # Aquarius: 300-330°
        "Meenam": (330, 360)    # Pisces: 330-360°
    }
    
    if target_sign in sign_ranges:
        start_deg, end_deg = sign_ranges[target_sign]
        return start_deg <= longitude <= end_deg
    return False

def test_longitude_validation():
    print("🔍 Testing Longitude-Based Sign Validation")
    print("=" * 50)
    
    # Test cases from our KOCHARAM results
    test_cases = [
        # (longitude, chart_sign, target_sign, expected_result)
        (327.27, "Kumbam", "Meenam", False),  # Should be False - 327° is in Kumbam (300-330°)
        (344.51, "Meenam", "Meenam", True),   # Should be True - 344° is in Meenam (330-360°)
        (358.23, "Meenam", "Meenam", True),   # Should be True - 358° is in Meenam
        (334.04, "Meenam", "Meenam", True),   # Should be True - 334° is in Meenam
        (329.62, "Kumbam", "Meenam", False),  # Should be False - 329° is in Kumbam
        (14.36, "Mesham", "Meenam", False),   # Should be False - 14° is in Mesham
        (335.07, "Meenam", "Meenam", True),   # Should be True - 335° is in Meenam
    ]
    
    print("Testing longitude validation logic:")
    print()
    
    for longitude, chart_sign, target_sign, expected in test_cases:
        result = longitude_to_sign_validation(longitude, target_sign)
        status = "✅" if result == expected else "❌"
        
        print(f"{status} {longitude:.2f}° (Chart: {chart_sign}) → Target: {target_sign}")
        print(f"   Longitude validation: {result} (Expected: {expected})")
        
        if result != expected:
            print(f"   🔍 Issue: Longitude {longitude}° should {'be' if expected else 'NOT be'} in {target_sign}")
        print()
    
    # Test the specific Meenam range
    print("🎯 Meenam Range Analysis (330-360°):")
    meenam_tests = [325, 327.27, 329, 330, 331, 335, 344.51, 350, 358.23, 359, 360, 361]
    
    for longitude in meenam_tests:
        in_meenam = longitude_to_sign_validation(longitude, "Meenam")
        status = "✅" if in_meenam else "❌"
        print(f"   {status} {longitude}° → In Meenam: {in_meenam}")

if __name__ == "__main__":
    test_longitude_validation()
