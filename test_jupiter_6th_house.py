#!/usr/bin/env python3
"""
Test KOCHARAM filter with Jupiter in 6th House
"""

import sys
import os
import json
from datetime import datetime

# Add the project path
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

try:
    # Import Flask app factory and create app context
    from app import create_app
    from app.services.rule_engine.main_rule_engine import process_rule_engine_request
    
    # Create Flask app with development config
    app = create_app('development')
    
    # Test data - Jupiter in 6th House with Dasa_Dates
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Dasa_Dates)AND KOCHARAM_FILTER(JUPITER in 6th_House)",
        "chart_type": "D1"
    }
    
    print("🔍 Testing KOCHARAM Filter: Jupiter in 6th House")
    print("=" * 60)
    print(f"Query: {test_data['query']}")
    print()
    
    with app.app_context():
        result = process_rule_engine_request(test_data)
    
    if result.get('success', False) and 'result' in result:
        periods = result['result']
        
        print(f"📊 Total periods returned: {len(periods)}")
        print()
        
        # Analyze first few periods
        for i, period in enumerate(periods[:3]):  # First 3 periods
            print(f"🔍 Period {i+1}:")
            print(f"   Dasha: {period.get('start_date', 'N/A')} to {period.get('end_date', 'N/A')}")
            print(f"   Planet: {period.get('planet_name', 'N/A')}")
            print(f"   Type: {period.get('period_type', 'N/A')}")
            print(f"   Duration: {period.get('duration', {}).get('formatted', 'N/A')}")
            
            if 'kocharam_filter' in period:
                kf = period['kocharam_filter']
                
                print(f"   🎯 KOCHARAM Details:")
                print(f"     Condition: {kf.get('condition', 'N/A')}")
                print(f"     Planet: {kf.get('planet', 'N/A')}")
                print(f"     Target House: {kf.get('target_house_name', 'N/A')}")
                print(f"     Method: {kf.get('calculation_method', 'N/A')}")
                
                # Check predicted dates
                pred_start = kf.get('predicted_start_date', 'N/A')
                pred_end = kf.get('predicted_end_date', 'N/A')
                pred_start_timing = kf.get('predicted_start_timing', 'N/A')
                pred_end_timing = kf.get('predicted_end_timing', 'N/A')
                
                print(f"     Predicted Start: {pred_start}")
                print(f"     Predicted End: {pred_end}")
                print(f"     Start Timing: {pred_start_timing}")
                print(f"     End Timing: {pred_end_timing}")
                
                # Check longitudes
                start_long = kf.get('predicted_start_longitude', 'N/A')
                end_long = kf.get('predicted_end_longitude', 'N/A')
                print(f"     Start Longitude: {start_long}°")
                print(f"     End Longitude: {end_long}°")
                
                # Check validations
                start_sign = kf.get('start_sign_validation', 'N/A')
                end_sign = kf.get('end_sign_validation', 'N/A')
                start_degree = kf.get('start_degree_range', 'N/A')
                end_degree = kf.get('end_degree_range', 'N/A')
                overall = kf.get('validation', 'N/A')
                
                print(f"     Validations:")
                print(f"       Start Sign: {start_sign}")
                print(f"       End Sign: {end_sign}")
                print(f"       Start Degree: {start_degree}")
                print(f"       End Degree: {end_degree}")
                print(f"       Overall: {overall}")
                
                # Check dasha transit flags
                start_dasha_found = kf.get('start_dasha_transit_found', 'N/A')
                end_dasha_found = kf.get('end_dasha_transit_found', 'N/A')
                predicted_date = kf.get('predicted_date', 'N/A')
                
                print(f"     Dasha Transit:")
                print(f"       Start Found: {start_dasha_found}")
                print(f"       End Found: {end_dasha_found}")
                print(f"       Predicted Date: {predicted_date}")
                
                # Check all_transit_periods
                all_transits = kf.get('all_transit_periods', [])
                print(f"     Transit Periods: {len(all_transits)}")
                
                if all_transits:
                    for j, transit in enumerate(all_transits):
                        print(f"       Transit {j+1}:")
                        print(f"         Date: {transit.get('date', 'N/A')}")
                        print(f"         Timing: {transit.get('timing', 'N/A')}")
                        print(f"         Longitude: {transit.get('longitude', 'N/A')}°")
                        print(f"         Sign: {transit.get('sign', 'N/A')}")
                        print(f"         Degree Range: {transit.get('degree_range', 'N/A')}")
                        print(f"         Within Dasha: {transit.get('within_dasha', 'N/A')}")
                        print(f"         Verified: {transit.get('verified', 'N/A')}")
                
                if overall:
                    print(f"   ✅ VALID: All validations passed")
                else:
                    print(f"   ⚠️ INVALID: Some validations failed")
            else:
                print(f"   ❌ No KOCHARAM filter data")
            
            print("-" * 50)
        
        # Summary
        valid_periods = sum(1 for p in periods if p.get('kocharam_filter', {}).get('validation', False))
        dasha_transit_periods = sum(1 for p in periods 
                                  if p.get('kocharam_filter', {}).get('start_dasha_transit_found', False) or 
                                     p.get('kocharam_filter', {}).get('end_dasha_transit_found', False))
        
        print(f"\n📈 Summary:")
        print(f"   Total periods: {len(periods)}")
        print(f"   Valid periods: {valid_periods}")
        print(f"   Periods with dasha transits: {dasha_transit_periods}")
        print(f"   Jupiter in 6th House analysis complete")
        
    else:
        print("❌ Query failed!")
        if 'error' in result:
            print(f"Error: {result['error']}")
        else:
            print(json.dumps(result, indent=2, default=str))
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
