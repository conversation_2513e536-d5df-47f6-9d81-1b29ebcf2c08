#!/usr/bin/env python3
"""
Test iterative refinement with degree-based calculations
"""

import sys
import os
from datetime import datetime, timedelta

# Add the project path
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

def test_degree_calculation():
    """Test the degree-based calculation function directly"""
    
    def calculate_jupiter_transit_degrees(current_long, target_long, start_date):
        """Calculate when Jupiter reaches target longitude using degree-based math"""
        
        # Jupiter's average motion: 0.083°/day (12-year cycle = 4383 days)
        jupiter_daily_motion = 360.0 / 4383.0  # More precise: 0.08214°/day
        
        # Calculate forward distance through zodiac
        distance_degrees = (target_long - current_long) % 360
        
        # Handle cases where Jupiter needs to complete nearly full cycle
        if distance_degrees > 350:  # Very close, likely next cycle
            distance_degrees = distance_degrees - 360
            if distance_degrees < 0:
                distance_degrees = abs(distance_degrees)
        
        # Calculate days to reach target
        days_to_target = distance_degrees / jupiter_daily_motion
        
        # Calculate target date
        target_date = start_date + timedelta(days=int(days_to_target))
        
        return target_date, int(days_to_target), distance_degrees
    
    print("🔍 Testing Degree-Based Calculation Function")
    print("=" * 50)
    
    # Test cases from KOCHARAM
    test_cases = [
        # (current_longitude, target_longitude, description)
        (36.39, 335.0, "Jupiter from Rishabam to Meenam start range"),
        (328.34, 335.0, "Jupiter from Kumbam to Meenam start range"),
        (344.51, 355.0, "Jupiter from Meenam middle to end range"),
        (327.0, 330.0, "Jupiter just before Meenam to start of Meenam"),
    ]
    
    start_date = datetime(1986, 8, 5)
    
    for current_long, target_long, description in test_cases:
        print(f"\n📊 Test: {description}")
        print(f"   Current: {current_long}°")
        print(f"   Target: {target_long}°")
        
        try:
            target_date, days, distance = calculate_jupiter_transit_degrees(current_long, target_long, start_date)
            
            print(f"   Distance: {distance:.1f}°")
            print(f"   Days: {days}")
            print(f"   Target Date: {target_date.strftime('%Y-%m-%d')}")
            print(f"   ✅ Degree-based calculation successful")
            
            # Verify the calculation
            jupiter_daily_motion = 360.0 / 4383.0
            expected_days = distance / jupiter_daily_motion
            print(f"   Verification: {expected_days:.0f} days (matches: {abs(days - expected_days) < 1})")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n🎯 Key Points:")
    print(f"   - Jupiter daily motion: {360.0 / 4383.0:.5f}°/day")
    print(f"   - Jupiter cycle: 4383 days (12 years)")
    print(f"   - Degree-based calculation is more accurate than simple 0.083°/day")
    print(f"   - Handles zodiac wrap-around correctly")

if __name__ == "__main__":
    test_degree_calculation()
