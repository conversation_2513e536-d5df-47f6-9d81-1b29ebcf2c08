#!/usr/bin/env python3
"""
Test chart synchronization after iterative refinement
"""

import sys
import os
import json
from datetime import datetime

# Add the project path
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

try:
    # Import Flask app factory and create app context
    from app import create_app
    from app.services.rule_engine.main_rule_engine import process_rule_engine_request
    
    # Create Flask app with development config
    app = create_app('development')
    
    # Test data - limit to first period only
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Bhukti_Dates)AND KOCHARAM_FILTER(JUPITER in 7th_House)",
        "chart_type": "D1"
    }
    
    print("🔍 Testing Chart Synchronization After Iterative Refinement")
    print("=" * 70)
    
    with app.app_context():
        result = process_rule_engine_request(test_data)
    
    if result.get('success', False) and 'result' in result:
        periods = result['result']
        
        # Focus on first period only
        if len(periods) > 0:
            period = periods[0]
            print(f"📊 Period 1 Analysis:")
            print(f"   Dasha: {period.get('start_date', 'N/A')} to {period.get('end_date', 'N/A')}")
            
            if 'kocharam_filter' in period:
                kf = period['kocharam_filter']
                
                print(f"\n🎯 KOCHARAM Results After Chart Regeneration:")
                print(f"   Predicted Start Date: {kf.get('predicted_start_date', 'N/A')}")
                print(f"   Predicted End Date: {kf.get('predicted_end_date', 'N/A')}")
                
                # Check for synchronization between predicted longitude and chart data
                predicted_start_long = kf.get('predicted_start_longitude', 'N/A')
                predicted_end_long = kf.get('predicted_end_longitude', 'N/A')
                
                print(f"\n📐 Longitude Analysis:")
                print(f"   Predicted Start Longitude: {predicted_start_long}°")
                print(f"   Predicted End Longitude: {predicted_end_long}°")
                
                # Check chart data
                start_chart = kf.get('predicted_start_date_chart', {})
                if start_chart and 'planet_positions' in start_chart:
                    jupiter_chart_data = start_chart['planet_positions'].get('JUPITER', {})
                    chart_longitude = jupiter_chart_data.get('longitude', 'N/A')
                    chart_sign = jupiter_chart_data.get('sign', 'N/A')
                    
                    print(f"\n🔍 Chart Verification:")
                    print(f"   Chart Start Longitude: {chart_longitude}°")
                    print(f"   Chart Start Sign: {chart_sign}")
                    
                    # Check synchronization
                    if isinstance(predicted_start_long, (int, float)) and isinstance(chart_longitude, (int, float)):
                        longitude_diff = abs(predicted_start_long - chart_longitude)
                        print(f"   Longitude Difference: {longitude_diff:.2f}°")
                        
                        if longitude_diff < 1.0:
                            print(f"   ✅ SYNCHRONIZED: Chart and prediction match!")
                        else:
                            print(f"   ❌ MISMATCH: Chart and prediction don't match")
                    
                    # Check if both are in Meenam range (330-360°)
                    if isinstance(predicted_start_long, (int, float)):
                        pred_in_meenam = 330 <= predicted_start_long <= 360
                        print(f"   Predicted longitude in Meenam: {pred_in_meenam}")
                    
                    if isinstance(chart_longitude, (int, float)):
                        chart_in_meenam = 330 <= chart_longitude <= 360
                        print(f"   Chart longitude in Meenam: {chart_in_meenam}")
                
                # Check validation results
                start_valid = kf.get('start_sign_validation', False)
                end_valid = kf.get('end_sign_validation', False)
                overall_valid = kf.get('validation', False)
                
                print(f"\n✅ Final Validation Results:")
                print(f"   Start Sign Valid: {start_valid}")
                print(f"   End Sign Valid: {end_valid}")
                print(f"   Overall Valid: {overall_valid}")
                
                if start_valid and end_valid:
                    print(f"\n🌟 SUCCESS: Both sign validations are TRUE!")
                    print(f"   The iterative refinement worked correctly!")
                else:
                    print(f"\n⚠️ Still needs work:")
                    if not start_valid:
                        print(f"     Start validation failed")
                    if not end_valid:
                        print(f"     End validation failed")
        
    else:
        print("❌ Query failed!")
        print(json.dumps(result, indent=2, default=str))
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
