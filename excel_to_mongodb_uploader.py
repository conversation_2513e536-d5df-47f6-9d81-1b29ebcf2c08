#!/usr/bin/env python3
"""
Excel to MongoDB Data Uploader for Fortune Lens
Safely uploads Excel data to MongoDB with proper validation and chart generation
"""

import pandas as pd
import pymongo
from pymongo import MongoClient
from bson import ObjectId
import json
from datetime import datetime, time
import sys
import os

# Add the astro_insights_pro app to the path
sys.path.append('astro_insights_pro')

try:
    from app.services.chart_service import generate_chart
    CHART_GENERATION_AVAILABLE = True
except ImportError:
    print("Warning: Chart generation not available")
    CHART_GENERATION_AVAILABLE = False

def get_location_info(place, state, country):
    """Simple location info function"""
    # Default coordinates for common places
    locations = {
        'cuddalore': {'latitude': 11.7480, 'longitude': 79.7714},
        'pondicherry': {'latitude': 11.9416, 'longitude': 79.8083},
        'salem': {'latitude': 11.6643, 'longitude': 78.1460},
        'dharmapuri': {'latitude': 12.1211, 'longitude': 78.1583},
        'kumbakonam': {'latitude': 10.9601, 'longitude': 79.3788},
        'vellore': {'latitude': 12.9165, 'longitude': 79.1325}
    }

    place_lower = place.lower() if place else ''
    if place_lower in locations:
        return locations[place_lower]
    else:
        # Default to Chennai coordinates
        return {'latitude': 13.0827, 'longitude': 80.2707}

class ExcelToMongoUploader:
    def __init__(self, mongodb_uri='mongodb://localhost:27017/', db_name='fortune_lens'):
        """Initialize the uploader with MongoDB connection"""
        self.client = MongoClient(mongodb_uri)
        self.db = self.client[db_name]
        self.collections = {
            'user_profile': 'user_profile',
            'member_profile': 'member_profile',
            'astro_data': 'user_member_astro_profile_data'
        }
        
    def load_excel_data(self, excel_file='Astro FL User Data Master v1.xlsx'):
        """Load and validate Excel data"""
        try:
            df = pd.read_excel(excel_file, sheet_name='User Astro Data Master')
            print(f"Loaded {len(df)} records from Excel")
            
            # Clean and validate data
            df = df.dropna(subset=['user_birthdate', 'user_birthtime', 'user_birthplace'])
            print(f"After removing incomplete records: {len(df)} records")
            
            return df
        except Exception as e:
            print(f"Error loading Excel data: {e}")
            return None
    
    def check_existing_data(self, user_id):
        """Check if user data already exists in MongoDB"""
        user_id = int(user_id)  # Convert numpy int64 to Python int
        user_exists = self.db[self.collections['user_profile']].find_one({'user_profile_id': user_id})
        member_count = self.db[self.collections['member_profile']].count_documents({'user_profile_id': user_id})
        astro_count = self.db[self.collections['astro_data']].count_documents({'user_profile_id': user_id})
        
        return {
            'user_exists': bool(user_exists),
            'member_count': member_count,
            'astro_count': astro_count,
            'user_data': user_exists
        }
    
    def get_next_sequence_id(self, collection_name, field_name):
        """Get the next sequence ID for a collection"""
        try:
            last_doc = self.db[collection_name].find().sort(field_name, -1).limit(1)
            last_doc = list(last_doc)
            if last_doc:
                return last_doc[0][field_name] + 1
            else:
                return 1
        except:
            return 1
    
    def create_user_profile(self, user_data):
        """Create user profile from Excel data"""
        user_id = int(user_data['user_id'])  # Convert numpy types
        user_profile = {
            '_id': ObjectId(),
            'user_profile_id': user_id,
            'email': f"user_{user_id}@fortunelens.com",
            'password': '$2b$12$default.hash.for.excel.import',  # Default hash
            'name': str(user_data['user_name']) if pd.notna(user_data['user_name']) else f"User {user_id}",
            'mobile': str(user_data.get('user_mobile', '0000000000')),
            'unique_key': f"FL{user_id}{datetime.now().strftime('%H%M%S')}",
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        return user_profile
    
    def create_member_profile(self, user_data, user_profile_id):
        """Create member profile from Excel data"""
        # Convert numpy types
        user_profile_id = int(user_profile_id)
        member_id = int(user_data['member_id'])

        # Get location info
        location_info = get_location_info(
            user_data['user_birthplace'],
            user_data.get('user_state', ''),
            user_data.get('user_country', 'India')
        )

        member_profile = {
            '_id': ObjectId(),
            'member_profile_id': member_id,
            'user_profile_id': user_profile_id,
            'unique_key': f"M{user_profile_id}_{member_id}_{datetime.now().strftime('%H%M%S')}",
            'name': str(user_data['user_name']) if pd.notna(user_data['user_name']) else f"Member {member_id}",
            'relation': 'self' if member_id == 1 else f"member_{member_id}",
            'birth_date': user_data['user_birthdate'].strftime('%Y-%m-%d') if pd.notna(user_data['user_birthdate']) else None,
            'birth_time': str(user_data['user_birthtime']) if pd.notna(user_data['user_birthtime']) else None,
            'birth_place': str(user_data['user_birthplace']),
            'state': str(user_data.get('user_state', '')),
            'country': str(user_data.get('user_country', 'India')),
            'latitude': float(location_info.get('latitude', 0.0)),
            'longitude': float(location_info.get('longitude', 0.0)),
            'gender': str(user_data.get('user_gender', '')).title() if pd.notna(user_data.get('user_gender')) else None,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        return member_profile
    
    def create_astro_profile(self, member_profile, user_data):
        """Create astrological profile with chart data"""
        # Prepare birth data for chart generation
        birth_data = {
            'user_birthdate': member_profile['birth_date'],
            'user_birthtime': member_profile['birth_time'],
            'user_birthplace': member_profile['birth_place'],
            'user_state': member_profile['state'],
            'user_country': member_profile['country']
        }
        
        # Generate chart data
        chart_data = {}
        if CHART_GENERATION_AVAILABLE:
            try:
                chart_data = generate_chart(birth_data)
                print(f"Generated chart data for user {member_profile['user_profile_id']}, member {member_profile['member_profile_id']}")
            except Exception as e:
                print(f"Error generating chart data: {e}")
                chart_data = {}
        else:
            print(f"Chart generation skipped for user {member_profile['user_profile_id']}, member {member_profile['member_profile_id']}")
        
        # Create astro profile
        astro_profile = dict(member_profile)
        astro_profile['_id'] = ObjectId()  # New ID for astro collection
        
        # Add astro-specific data
        astro_profile['astro_data'] = {
            'user_profession': str(user_data.get('user_profession', '')),
            'user_data_label_1': str(user_data.get('User_Data_Label_1 (Profession)', '')),
            'user_data_label_2': str(user_data.get('User_Data_Label_2 (Specialization)', '')),
            'user_data_label_3': str(user_data.get('User_Data_Label_3 (Foreign / Domestic)', '')),
            'user_data_label_4': str(user_data.get('User_Data_Label_4 (Foreign Travel Date1)', '')),
            'user_data_label_5': str(user_data.get('User_Data_Label_5 (Foreign Travel Country1)', ''))
        }
        
        # Add chart data
        astro_profile['chart_data'] = chart_data
        
        return astro_profile
    
    def upload_user_data(self, user_data_group, dry_run=True):
        """Upload a group of user data (all members for one user_id)"""
        user_id = user_data_group['user_id'].iloc[0]
        
        print(f"\n=== Processing User ID: {user_id} ===")
        
        # Check existing data
        existing = self.check_existing_data(user_id)
        print(f"Existing data: {existing}")
        
        if existing['user_exists'] and not dry_run:
            print(f"User {user_id} already exists. Skipping...")
            return False
        
        # Create user profile (from first record)
        first_record = user_data_group.iloc[0]
        user_profile = self.create_user_profile(first_record)
        
        if not dry_run:
            try:
                self.db[self.collections['user_profile']].insert_one(user_profile)
                print(f"Created user profile for user {user_id}")
            except Exception as e:
                print(f"Error creating user profile: {e}")
                return False
        else:
            print(f"[DRY RUN] Would create user profile: {user_profile['name']}")
        
        # Create member profiles and astro data for each member
        for idx, member_data in user_data_group.iterrows():
            member_profile = self.create_member_profile(member_data, user_id)
            
            if not dry_run:
                try:
                    # Insert member profile
                    self.db[self.collections['member_profile']].insert_one(member_profile)
                    print(f"Created member profile {member_data['member_id']} for user {user_id}")
                    
                    # Create and insert astro profile
                    astro_profile = self.create_astro_profile(member_profile, member_data)
                    self.db[self.collections['astro_data']].insert_one(astro_profile)
                    print(f"Created astro profile {member_data['member_id']} for user {user_id}")
                    
                except Exception as e:
                    print(f"Error creating member/astro profile: {e}")
                    continue
            else:
                print(f"[DRY RUN] Would create member {member_data['member_id']}: {member_profile['name']}")
        
        return True
    
    def upload_all_data(self, df, dry_run=True, max_users=None):
        """Upload all data from Excel to MongoDB"""
        print(f"Starting upload process (dry_run={dry_run})")
        
        # Group by user_id
        user_groups = df.groupby('user_id')
        
        success_count = 0
        error_count = 0
        
        for user_id, user_data_group in user_groups:
            if max_users and success_count >= max_users:
                break
                
            try:
                if self.upload_user_data(user_data_group, dry_run):
                    success_count += 1
                else:
                    error_count += 1
            except Exception as e:
                print(f"Error processing user {user_id}: {e}")
                error_count += 1
        
        print(f"\n=== UPLOAD SUMMARY ===")
        print(f"Successful: {success_count}")
        print(f"Errors: {error_count}")
        print(f"Total processed: {success_count + error_count}")
        
        return success_count, error_count
    
    def close(self):
        """Close MongoDB connection"""
        self.client.close()

def main():
    """Main function to run the uploader"""
    uploader = ExcelToMongoUploader()
    
    try:
        # Load Excel data
        df = uploader.load_excel_data()
        if df is None:
            return
        
        print(f"Loaded {len(df)} records from Excel")
        print(f"Unique users: {df['user_id'].nunique()}")
        print(f"Member distribution: {df['member_id'].value_counts().to_dict()}")
        
        # First run in dry-run mode
        print("\n=== DRY RUN MODE ===")
        uploader.upload_all_data(df, dry_run=True, max_users=3)
        
        # Ask for confirmation
        response = input("\nDo you want to proceed with actual upload? (yes/no): ")
        if response.lower() == 'yes':
            print("\n=== ACTUAL UPLOAD ===")
            uploader.upload_all_data(df, dry_run=False)
        else:
            print("Upload cancelled.")
    
    finally:
        uploader.close()

if __name__ == "__main__":
    main()
