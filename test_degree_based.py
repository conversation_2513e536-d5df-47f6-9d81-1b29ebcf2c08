#!/usr/bin/env python3
"""
Test degree-based calculations in KOCHARAM filter
"""

import sys
import os
import json
from datetime import datetime

# Add the project path
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

try:
    # Import Flask app factory and create app context
    from app import create_app
    from app.services.rule_engine.main_rule_engine import process_rule_engine_request
    
    # Create Flask app with development config
    app = create_app('development')
    
    # Test data - single period
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Bhukti_Dates)AND KOCHARAM_FILTER(JUPITER in 7th_House)",
        "chart_type": "D1"
    }
    
    print("🔍 Testing Degree-Based Calculations in KOCHARAM")
    print("=" * 60)
    print("Looking for:")
    print("- 'DEGREE-BASED calculation' messages")
    print("- 'DEGREE-BASED adjustment' messages")
    print("- Proper iterative refinement using degree calculations")
    print()
    
    with app.app_context():
        result = process_rule_engine_request(test_data)
    
    if result.get('success', False) and 'result' in result:
        periods = result['result']
        print(f"📊 Found {len(periods)} periods after KOCHARAM filtering")
        
        # Check first period for degree-based calculations
        if len(periods) > 0:
            period = periods[0]
            if 'kocharam_filter' in period:
                kf = period['kocharam_filter']
                
                print(f"\n🔍 First Period Analysis:")
                print(f"   Dasha: {period.get('start_date', 'N/A')} to {period.get('end_date', 'N/A')}")
                print(f"   Method: {kf.get('calculation_method', 'N/A')}")
                print(f"   Predicted Start: {kf.get('predicted_start_date', 'N/A')}")
                print(f"   Predicted End: {kf.get('predicted_end_date', 'N/A')}")
                print(f"   Start Longitude: {kf.get('predicted_start_longitude', 'N/A')}°")
                print(f"   End Longitude: {kf.get('predicted_end_longitude', 'N/A')}°")
                
                # Check validations
                start_sign = kf.get('start_sign_validation', False)
                end_sign = kf.get('end_sign_validation', False)
                start_degree = kf.get('start_degree_range', False)
                end_degree = kf.get('end_degree_range', False)
                
                print(f"\n✅ Validation Results:")
                print(f"   Start Sign Valid: {start_sign}")
                print(f"   End Sign Valid: {end_sign}")
                print(f"   Start Degree Valid: {start_degree}")
                print(f"   End Degree Valid: {end_degree}")
                
                all_valid = start_sign and end_sign and start_degree and end_degree
                print(f"   All Valid: {all_valid}")
                
                if all_valid:
                    print(f"\n🌟 SUCCESS: Degree-based calculations produced valid results!")
                else:
                    print(f"\n⚠️ PARTIAL: Some validations still false - may need more iterations")
        
        print(f"\n📈 Summary:")
        print(f"   Total valid periods returned: {len(periods)}")
        print(f"   All periods should have used degree-based calculations")
        
    else:
        print("❌ Query failed!")
        if 'error' in result:
            print(f"Error: {result['error']}")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
