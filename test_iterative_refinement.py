#!/usr/bin/env python3
"""
Test iterative refinement for one period only
"""

import sys
import os
import json
from datetime import datetime

# Add the project path
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

try:
    # Import Flask app factory and create app context
    from app import create_app
    from app.services.rule_engine.main_rule_engine import process_rule_engine_request
    
    # Create Flask app with development config
    app = create_app('development')
    
    # Test data - limit to first period only
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Bhukti_Dates)AND KOCHARAM_FILTER(JUPITER in 7th_House)",
        "chart_type": "D1"
    }
    
    print("🔍 Testing Iterative Refinement for KOCHARAM")
    print("=" * 60)
    
    with app.app_context():
        result = process_rule_engine_request(test_data)
    
    if result.get('success', False) and 'result' in result:
        periods = result['result']
        
        # Focus on first period only
        if len(periods) > 0:
            period = periods[0]
            print(f"📊 Period 1 Analysis:")
            print(f"   Dasha: {period.get('start_date', 'N/A')} to {period.get('end_date', 'N/A')}")
            
            if 'kocharam_filter' in period:
                kf = period['kocharam_filter']
                
                print(f"\n🎯 KOCHARAM Results After Iterative Refinement:")
                print(f"   Predicted Start Date: {kf.get('predicted_start_date', 'N/A')}")
                print(f"   Predicted End Date: {kf.get('predicted_end_date', 'N/A')}")
                print(f"   Start Longitude: {kf.get('predicted_start_longitude', 'N/A')}°")
                print(f"   End Longitude: {kf.get('predicted_end_longitude', 'N/A')}°")
                print(f"   Start Sign: {kf.get('predicted_start_sign', 'N/A')}")
                print(f"   End Sign: {kf.get('predicted_end_sign', 'N/A')}")
                
                # Check validation results
                start_valid = kf.get('start_sign_validation', False)
                end_valid = kf.get('end_sign_validation', False)
                overall_valid = kf.get('validation', False)
                
                print(f"\n✅ Validation Results:")
                print(f"   Start Sign Valid: {start_valid}")
                print(f"   End Sign Valid: {end_valid}")
                print(f"   Overall Valid: {overall_valid}")
                
                if start_valid and end_valid:
                    print(f"\n🌟 SUCCESS: Both sign validations are now TRUE!")
                    print(f"   Jupiter will be in Meenam for both predicted dates")
                else:
                    print(f"\n⚠️ Still needs refinement:")
                    if not start_valid:
                        print(f"     Start date needs adjustment")
                    if not end_valid:
                        print(f"     End date needs adjustment")
                
                # Show degree range validation
                start_degree = kf.get('start_degree_range', False)
                end_degree = kf.get('end_degree_range', False)
                print(f"\n📐 Degree Range Validation:")
                print(f"   Start in 1-10° range: {start_degree}")
                print(f"   End in 20-30° range: {end_degree}")
        
        print(f"\n📈 Summary:")
        print(f"   Total periods processed: {len(periods)}")
        print(f"   Iterative refinement applied to improve accuracy")
        
    else:
        print("❌ Query failed!")
        print(json.dumps(result, indent=2, default=str))
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
