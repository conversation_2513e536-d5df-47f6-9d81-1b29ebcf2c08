#!/usr/bin/env python3
"""
Test enhanced all_transit_periods with multiple transits
"""

import sys
import os
import json
from datetime import datetime

# Add the project path
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

try:
    # Import Flask app factory and create app context
    from app import create_app
    from app.services.rule_engine.main_rule_engine import process_rule_engine_request
    
    # Create Flask app with development config
    app = create_app('development')
    
    # Test data - Jupiter in 6th House
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Dasa_Dates)AND KOCHARAM_FILTER(JUPITER in 6th_House)",
        "chart_type": "D1"
    }
    
    print("🔍 Testing Enhanced all_transit_periods")
    print("=" * 60)
    print("Expected features:")
    print("- Multiple transit periods within dasha")
    print("- predicted_start_date and predicted_end_date")
    print("- Next transits using degree-based calculations")
    print("- All dates within dasha period only")
    print()
    
    with app.app_context():
        result = process_rule_engine_request(test_data)
    
    if result.get('success', False) and 'result' in result:
        periods = result['result']
        
        print(f"📊 Total periods returned: {len(periods)}")
        
        # Check first period for enhanced all_transit_periods
        if len(periods) > 0 and 'kocharam_filter' in periods[0]:
            kf = periods[0]['kocharam_filter']
            
            print(f"\n🔍 Enhanced KOCHARAM Analysis:")
            print(f"   Planet: {kf.get('planet', 'N/A')}")
            print(f"   Target House: {kf.get('target_house_name', 'N/A')}")
            print(f"   Dasha Period: {periods[0].get('start_date', 'N/A')} to {periods[0].get('end_date', 'N/A')}")
            
            # Check all_transit_periods
            all_transits = kf.get('all_transit_periods', [])
            print(f"\n🎯 Enhanced all_transit_periods: {len(all_transits)} periods")
            
            if all_transits:
                for i, transit in enumerate(all_transits):
                    print(f"\n   Transit {i+1}:")
                    print(f"     Date: {transit.get('date', 'N/A')}")
                    print(f"     Timing: {transit.get('timing', 'N/A')}")
                    print(f"     Longitude: {transit.get('longitude', 'N/A')}°")
                    print(f"     Sign: {transit.get('sign', 'N/A')}")
                    print(f"     Degree Range: {transit.get('degree_range', 'N/A')}")
                    print(f"     Within Dasha: {transit.get('within_dasha', 'N/A')}")
                    print(f"     Verified: {transit.get('verified', 'N/A')}")
                    
                    # Check for enhanced fields
                    if 'predicted_start_date' in transit:
                        print(f"     Predicted Start Date: {transit.get('predicted_start_date', 'N/A')}")
                    if 'predicted_end_date' in transit:
                        print(f"     Predicted End Date: {transit.get('predicted_end_date', 'N/A')}")
                    if 'transit_type' in transit:
                        print(f"     Transit Type: {transit.get('transit_type', 'N/A')}")
                
                # Summary
                within_dasha_count = sum(1 for t in all_transits if t.get('within_dasha', False))
                verified_count = sum(1 for t in all_transits if t.get('verified', False))
                
                print(f"\n📈 Summary:")
                print(f"   Total transits: {len(all_transits)}")
                print(f"   Within dasha: {within_dasha_count}")
                print(f"   Verified: {verified_count}")
                
                if within_dasha_count == len(all_transits):
                    print(f"   ✅ SUCCESS: All transits are within dasha period!")
                else:
                    print(f"   ⚠️ ISSUE: Some transits are outside dasha period")
                
                if verified_count == len(all_transits):
                    print(f"   ✅ SUCCESS: All transits are verified with charts!")
                else:
                    print(f"   ⚠️ ISSUE: Some transits are not verified")
            else:
                print(f"   ⚠️ No transit periods found")
        else:
            print(f"❌ No KOCHARAM filter data found")
        
    else:
        print("❌ Query failed!")
        if 'error' in result:
            print(f"Error: {result['error']}")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
