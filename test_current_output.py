#!/usr/bin/env python3
"""
Test script to show current KOCHARAM output structure
"""

import requests
import json
import time

def test_current_kocharam_output():
    """Test current KOCHARAM output structure"""
    
    # API endpoint
    base_url = "http://localhost:5003"
    endpoint = f"{base_url}/api/rule-engine/"
    
    # Simple test query that should be fast
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "7th_House_Ruling_Planet Bhukti_Dates",  # Remove KOCHARAM for faster test
        "chart_type": "D1"
    }
    
    print("🔍 Testing Current Output Structure (without KOCHARAM)")
    print("=" * 60)
    print(f"Query: {test_data['query']}")
    print()
    
    try:
        print("📡 Sending API Request...")
        start_time = time.time()
        
        response = requests.post(endpoint, json=test_data, timeout=30)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"⏱️  Execution Time: {execution_time:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        print()
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ SUCCESS - Current Output Structure:")
            print("=" * 50)
            
            if 'result' in result and 'dasha_dates' in result['result']:
                dasha_dates = result['result']['dasha_dates']
                print(f"Total Periods: {len(dasha_dates)}")
                
                # Show structure of first period
                if len(dasha_dates) > 0:
                    first_period = dasha_dates[0]
                    print(f"\n📋 FIRST PERIOD STRUCTURE:")
                    print("-" * 30)
                    for key, value in first_period.items():
                        if key != 'kocharam_filter':  # Skip KOCHARAM for now
                            print(f"  {key}: {value}")
                    
                    print(f"\n🔍 Available Keys in Period:")
                    print(f"  {list(first_period.keys())}")
                    
                    # Check if KOCHARAM filter exists
                    if 'kocharam_filter' in first_period:
                        print(f"\n🪐 KOCHARAM Filter Structure:")
                        kocharam = first_period['kocharam_filter']
                        print(f"  Keys: {list(kocharam.keys())}")
                        
                        # Show key fields
                        key_fields = ['condition', 'planet', 'target_house_name', 'predicted_date', 
                                    'transit_found', 'calculation_method']
                        for field in key_fields:
                            if field in kocharam:
                                print(f"  {field}: {kocharam[field]}")
                    else:
                        print(f"\n🪐 No KOCHARAM Filter (as expected)")
            
            print(f"\n📄 Response Keys:")
            print(f"  {list(result.keys())}")
            
        else:
            print("❌ ERROR Response:")
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Flask server not running")
        
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: Request took too long")
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")

def show_expected_kocharam_structure():
    """Show what the KOCHARAM output structure should look like"""
    
    print("\n🎯 EXPECTED KOCHARAM OUTPUT STRUCTURE")
    print("=" * 60)
    
    expected_structure = {
        "condition": "JUPITER in Meenam",
        "planet": "JUPITER",
        "target_house_name": "Meenam",
        "predicted_start_date": "2025-03-15",
        "predicted_end_date": "2025-04-20",
        "start_longitude": 331.25,
        "end_longitude": 355.75,
        "predicted_sign": "Meenam",
        "validation": True,
        "transit_found": True,
        "calculation_method": "ENHANCED DEGREE RANGE SEARCH - Found both 1-10° and 20-30° ranges",
        "start_date": "2022-07-11",
        "end_date": "2023-06-17",
        "start_days_from_dasha": 1234,
        "end_days_from_dasha": 1270,
        "start_sign_validation": True,
        "end_sign_validation": True,
        "chart_validation": True,
        "start_dasha_transit_found": True,
        "end_dasha_transit_found": True,
        "predicted_start_sign": "Meenam",
        "predicted_end_sign": "Meenam",
        "start_degree_validation": True,
        "end_degree_validation": True,
        "predicted_start_chart": {"lagna": "...", "planet_positions": "..."},
        "predicted_end_chart": {"lagna": "...", "planet_positions": "..."},
        "all_transit_periods": [
            {
                "date": "2025-03-15",
                "longitude": 331.25,
                "sign": "Meenam",
                "degree_range": "1-10°",
                "verified": True
            }
        ]
    }
    
    print("📋 Expected KOCHARAM Fields:")
    for key, value in expected_structure.items():
        if isinstance(value, dict):
            print(f"  {key}: {{...}} (object)")
        elif isinstance(value, list):
            print(f"  {key}: [...] (array)")
        else:
            print(f"  {key}: {value}")
    
    print(f"\n📊 Total Expected Fields: {len(expected_structure)}")
    print(f"🎯 This is your 'old output structure' with complex fields")

if __name__ == "__main__":
    show_expected_kocharam_structure()
    test_current_kocharam_output()
