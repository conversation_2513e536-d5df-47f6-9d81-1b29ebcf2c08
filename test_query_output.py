#!/usr/bin/env python3
"""
Test the specific query and check all_transit_periods output
"""

import sys
import os
import json
from datetime import datetime

# Add the project path
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

try:
    # Import Flask app factory and create app context
    from app import create_app
    from app.services.rule_engine.main_rule_engine import process_rule_engine_request
    
    # Create Flask app with development config
    app = create_app('development')
    
    # Test data - exact query from user
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Bhukti_Dates)AND KOCHARAM_FILTER(JUPITER in 7th_House)",
        "chart_type": "D1"
    }
    
    print("🔍 Testing Query Output")
    print("=" * 50)
    print(f"Query: {test_data['query']}")
    print()
    
    with app.app_context():
        result = process_rule_engine_request(test_data)
    
    if result.get('success', False) and 'result' in result:
        periods = result['result']
        
        print(f"📊 Total periods returned: {len(periods)}")
        print()
        
        # Check first few periods for all_transit_periods
        for i, period in enumerate(periods[:3]):  # First 3 periods
            print(f"🔍 Period {i+1}:")
            print(f"   Dasha: {period.get('start_date', 'N/A')} to {period.get('end_date', 'N/A')}")
            print(f"   Planet: {period.get('planet_name', 'N/A')}")
            print(f"   Duration: {period.get('duration', {}).get('formatted', 'N/A')}")
            
            if 'kocharam_filter' in period:
                kf = period['kocharam_filter']
                
                # Check all_transit_periods
                all_transits = kf.get('all_transit_periods', [])
                print(f"   Transit periods: {len(all_transits)}")
                
                if all_transits:
                    for j, transit in enumerate(all_transits):
                        print(f"     Transit {j+1}:")
                        print(f"       Date: {transit.get('date', 'N/A')}")
                        print(f"       Timing: {transit.get('timing', 'N/A')}")
                        print(f"       Longitude: {transit.get('longitude', 'N/A')}°")
                        print(f"       Sign: {transit.get('sign', 'N/A')}")
                        print(f"       Degree Range: {transit.get('degree_range', 'N/A')}")
                        print(f"       Within Dasha: {transit.get('within_dasha', 'N/A')}")
                        print(f"       Verified: {transit.get('verified', 'N/A')}")
                        print()
                
                # Check validation results
                print(f"   Validations:")
                print(f"     Start Sign: {kf.get('start_sign_validation', 'N/A')}")
                print(f"     End Sign: {kf.get('end_sign_validation', 'N/A')}")
                print(f"     Start Degree: {kf.get('start_degree_range', 'N/A')}")
                print(f"     End Degree: {kf.get('end_degree_range', 'N/A')}")
                print(f"     Overall: {kf.get('validation', 'N/A')}")
                
            print("-" * 50)
        
        # Show summary
        valid_periods = sum(1 for p in periods if p.get('kocharam_filter', {}).get('validation', False))
        print(f"\n📈 Summary:")
        print(f"   Total periods: {len(periods)}")
        print(f"   Valid periods: {valid_periods}")
        print(f"   All periods should show all_transit_periods with proper timing format")
        
    else:
        print("❌ Query failed!")
        if 'error' in result:
            print(f"Error: {result['error']}")
        else:
            print(json.dumps(result, indent=2, default=str))
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
