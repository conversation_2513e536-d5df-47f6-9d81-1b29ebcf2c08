#!/usr/bin/env python3
"""
Test script to verify the final KOCHARAM implementation with complex output structure
"""

import requests
import json
import time

def test_final_kocharam():
    """Test the final KOCHARAM implementation"""
    
    # API endpoint
    base_url = "http://localhost:5003"
    endpoint = f"{base_url}/api/rule-engine/"
    
    # Your original query with KOCHARAM
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Bhukti_Dates)AND KOCHARAM_FILTER(JUPITER in 7th_House)",
        "chart_type": "D1"
    }
    
    print("🎯 FINAL KOCHARAM TEST - Complex Output with Degree-Based Calculation")
    print("=" * 70)
    print(f"Query: {test_data['query']}")
    print()
    
    print("🔧 EXPECTED IMPROVEMENTS:")
    print("-" * 25)
    print("✅ Complex output structure (your old format)")
    print("✅ Degree-based calculation (no iterative searching)")
    print("✅ Fast execution (under 30 seconds)")
    print("✅ All original fields present")
    print("✅ Only 2 chart generations per period")
    print()
    
    try:
        print("📡 Sending API Request...")
        start_time = time.time()
        
        response = requests.post(endpoint, json=test_data, timeout=45)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"⏱️  Execution Time: {execution_time:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        print()
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ SUCCESS - Final KOCHARAM Results:")
            print("=" * 50)
            
            if 'result' in result and 'dasha_dates' in result['result']:
                dasha_dates = result['result']['dasha_dates']
                print(f"Total Periods: {len(dasha_dates)}")
                
                # Find periods with KOCHARAM filter
                kocharam_periods = [p for p in dasha_dates if 'kocharam_filter' in p]
                print(f"Periods with KOCHARAM: {len(kocharam_periods)}")
                
                # Show first period with KOCHARAM
                for i, period in enumerate(dasha_dates[:3]):
                    if 'kocharam_filter' in period:
                        print(f"\n📅 Period {i+1} KOCHARAM Analysis:")
                        print(f"  Planet: {period.get('planet_name', 'N/A')}")
                        print(f"  Period: {period.get('start_date', 'N/A')} to {period.get('end_date', 'N/A')}")
                        
                        kocharam = period['kocharam_filter']
                        print(f"\n🪐 KOCHARAM Structure Analysis:")
                        print(f"  Total Fields: {len(kocharam)}")
                        
                        # Check for complex fields
                        complex_fields = [
                            'predicted_start_date', 'predicted_end_date', 'start_longitude', 'end_longitude',
                            'start_degree_validation', 'end_degree_validation', 'all_transit_periods'
                        ]
                        
                        present_complex_fields = [f for f in complex_fields if f in kocharam]
                        print(f"  Complex Fields Present: {len(present_complex_fields)}/{len(complex_fields)}")
                        
                        # Show key results
                        key_results = {
                            'condition': kocharam.get('condition'),
                            'transit_found': kocharam.get('transit_found'),
                            'calculation_method': kocharam.get('calculation_method'),
                            'predicted_start_date': kocharam.get('predicted_start_date'),
                            'predicted_end_date': kocharam.get('predicted_end_date'),
                            'start_degree_validation': kocharam.get('start_degree_validation'),
                            'end_degree_validation': kocharam.get('end_degree_validation')
                        }
                        
                        print(f"\n📊 Key Results:")
                        for key, value in key_results.items():
                            print(f"    {key}: {value}")
                        
                        # Check all_transit_periods
                        if 'all_transit_periods' in kocharam:
                            periods = kocharam['all_transit_periods']
                            print(f"\n🔄 Transit Periods: {len(periods)}")
                            for j, tp in enumerate(periods):
                                print(f"    Period {j+1}: {tp.get('date')} - {tp.get('degree_range')} - Verified: {tp.get('verified')}")
                        
                        # Show all available fields
                        print(f"\n📋 All KOCHARAM Fields:")
                        for key in sorted(kocharam.keys()):
                            if key not in ['predicted_start_chart', 'predicted_end_chart', 'dasha_start_chart']:
                                value = kocharam[key]
                                if isinstance(value, (dict, list)):
                                    print(f"    {key}: {type(value).__name__}")
                                else:
                                    print(f"    {key}: {value}")
                        
                        break  # Show only first KOCHARAM period
                
                # Performance analysis
                print(f"\n⚡ PERFORMANCE ANALYSIS:")
                print(f"  Execution Time: {execution_time:.2f}s")
                if execution_time < 30:
                    print(f"  ✅ FAST: Under 30 seconds")
                elif execution_time < 60:
                    print(f"  ⚠️  MODERATE: Under 1 minute")
                else:
                    print(f"  ❌ SLOW: Over 1 minute")
                
                # Check for degree-based calculation
                sample_kocharam = None
                for period in dasha_dates:
                    if 'kocharam_filter' in period:
                        sample_kocharam = period['kocharam_filter']
                        break
                
                if sample_kocharam:
                    method = sample_kocharam.get('calculation_method', '')
                    if 'degree' in method.lower() or 'DEGREE' in method:
                        print(f"  ✅ DEGREE-BASED: Calculation method indicates degree-based approach")
                    else:
                        print(f"  ⚠️  METHOD: {method}")
                        
        else:
            print("❌ ERROR Response:")
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Flask server not running")
        
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: Request took too long")
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")

if __name__ == "__main__":
    test_final_kocharam()
