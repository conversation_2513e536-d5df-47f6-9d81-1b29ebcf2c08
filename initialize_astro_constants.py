#!/usr/bin/env python3
"""
Initialize Astrological Constants in MongoDB

This script creates and populates MongoDB collections with astrological constants
that were previously hardcoded in the rule engine. It checks if collections exist
and only creates them if they're missing.
"""

import pymongo
from pymongo import MongoClient
from datetime import datetime
import sys
import os

def connect_to_mongodb():
    """Connect to MongoDB database"""
    try:
        client = MongoClient('mongodb://localhost:27017/')
        db = client['fortune_lens']
        
        # Test connection
        db.command('ping')
        print("✅ Connected to MongoDB successfully")
        return db
        
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        sys.exit(1)

def initialize_planetary_rotation_periods(db):
    """Initialize planetary rotation periods collection"""
    collection_name = 'astro_planetary_rotation_periods'
    
    if collection_name in db.list_collection_names():
        print(f"⚠️  Collection {collection_name} already exists, skipping...")
        return
    
    rotation_data = [
        {
            'planet': 'SUN',
            'rotation_period_days': 365,
            'description': 'Solar year - time for Sun to complete zodiac cycle',
            'source': 'Traditional Vedic Astrology'
        },
        {
            'planet': 'MOON',
            'rotation_period_days': 30,
            'description': 'Lunar month - time for Moon to complete zodiac cycle',
            'source': 'Traditional Vedic Astrology'
        },
        {
            'planet': 'MERCURY',
            'rotation_period_days': 88,
            'description': 'Mercury orbital period around Sun',
            'source': 'Astronomical data'
        },
        {
            'planet': 'VENUS',
            'rotation_period_days': 225,
            'description': 'Venus orbital period around Sun',
            'source': 'Astronomical data'
        },
        {
            'planet': 'MARS',
            'rotation_period_days': 687,
            'description': 'Mars orbital period around Sun',
            'source': 'Astronomical data'
        },
        {
            'planet': 'JUPITER',
            'rotation_period_days': 4333,
            'description': 'Jupiter orbital period around Sun (approximately 12 years)',
            'source': 'Astronomical data'
        },
        {
            'planet': 'RAHU',
            'rotation_period_days': 6790,
            'description': 'Rahu nodal period (approximately 18.6 years)',
            'source': 'Traditional Vedic Astrology'
        },
        {
            'planet': 'KETU',
            'rotation_period_days': 6790,
            'description': 'Ketu nodal period (approximately 18.6 years)',
            'source': 'Traditional Vedic Astrology'
        },
        {
            'planet': 'SATURN',
            'rotation_period_days': 10756,
            'description': 'Saturn orbital period around Sun (approximately 29.5 years)',
            'source': 'Astronomical data'
        }
    ]
    
    # Add timestamps
    now = datetime.utcnow()
    for item in rotation_data:
        item['created_at'] = now
        item['updated_at'] = now
    
    try:
        db[collection_name].insert_many(rotation_data)
        print(f"✅ Created {collection_name} with {len(rotation_data)} records")
    except Exception as e:
        print(f"❌ Failed to create {collection_name}: {e}")

def initialize_sign_degree_mappings(db):
    """Initialize zodiac sign degree mappings collection"""
    collection_name = 'astro_sign_degree_mappings'
    
    if collection_name in db.list_collection_names():
        print(f"⚠️  Collection {collection_name} already exists, skipping...")
        return
    
    degree_data = [
        {
            'sign_name': 'Mesham',
            'start_degree': 0,
            'end_degree': 30,
            'english_name': 'Aries',
            'element': 'Fire',
            'quality': 'Cardinal',
            'ruling_planet': 'MARS'
        },
        {
            'sign_name': 'Rishabam',
            'start_degree': 30,
            'end_degree': 60,
            'english_name': 'Taurus',
            'element': 'Earth',
            'quality': 'Fixed',
            'ruling_planet': 'VENUS'
        },
        {
            'sign_name': 'Midunam',
            'start_degree': 60,
            'end_degree': 90,
            'english_name': 'Gemini',
            'element': 'Air',
            'quality': 'Mutable',
            'ruling_planet': 'MERCURY'
        },
        {
            'sign_name': 'Kadagam',
            'start_degree': 90,
            'end_degree': 120,
            'english_name': 'Cancer',
            'element': 'Water',
            'quality': 'Cardinal',
            'ruling_planet': 'MOON'
        },
        {
            'sign_name': 'Simmam',
            'start_degree': 120,
            'end_degree': 150,
            'english_name': 'Leo',
            'element': 'Fire',
            'quality': 'Fixed',
            'ruling_planet': 'SUN'
        },
        {
            'sign_name': 'Kanni',
            'start_degree': 150,
            'end_degree': 180,
            'english_name': 'Virgo',
            'element': 'Earth',
            'quality': 'Mutable',
            'ruling_planet': 'MERCURY'
        },
        {
            'sign_name': 'Thulam',
            'start_degree': 180,
            'end_degree': 210,
            'english_name': 'Libra',
            'element': 'Air',
            'quality': 'Cardinal',
            'ruling_planet': 'VENUS'
        },
        {
            'sign_name': 'Virichigam',
            'start_degree': 210,
            'end_degree': 240,
            'english_name': 'Scorpio',
            'element': 'Water',
            'quality': 'Fixed',
            'ruling_planet': 'MARS'
        },
        {
            'sign_name': 'Dhanusu',
            'start_degree': 240,
            'end_degree': 270,
            'english_name': 'Sagittarius',
            'element': 'Fire',
            'quality': 'Mutable',
            'ruling_planet': 'JUPITER'
        },
        {
            'sign_name': 'MAGARAM',
            'start_degree': 270,
            'end_degree': 300,
            'english_name': 'Capricorn',
            'element': 'Earth',
            'quality': 'Cardinal',
            'ruling_planet': 'SATURN'
        },
        {
            'sign_name': 'Kumbam',
            'start_degree': 300,
            'end_degree': 330,
            'english_name': 'Aquarius',
            'element': 'Air',
            'quality': 'Fixed',
            'ruling_planet': 'SATURN'
        },
        {
            'sign_name': 'Meenam',
            'start_degree': 330,
            'end_degree': 360,
            'english_name': 'Pisces',
            'element': 'Water',
            'quality': 'Mutable',
            'ruling_planet': 'JUPITER'
        }
    ]
    
    # Add timestamps
    now = datetime.utcnow()
    for item in degree_data:
        item['created_at'] = now
        item['updated_at'] = now
    
    try:
        db[collection_name].insert_many(degree_data)
        print(f"✅ Created {collection_name} with {len(degree_data)} records")
    except Exception as e:
        print(f"❌ Failed to create {collection_name}: {e}")

def update_existing_aspects_collection(db):
    """Update existing astro_planets_aspects collection with missing data"""
    collection_name = 'astro_planets_aspects'
    
    if collection_name not in db.list_collection_names():
        print(f"⚠️  Collection {collection_name} does not exist, skipping update...")
        return
    
    # Check what planets are missing
    existing_planets = set()
    for doc in db[collection_name].find():
        existing_planets.add(doc.get('Planet', '').upper())
    
    # Define complete aspect data
    complete_aspects = {
        'SUN': '7th',
        'MOON': '7th',
        'MERCURY': '7th',
        'VENUS': '7th',
        'MARS': '4th, 7th, 8th',
        'JUPITER': '5th, 7th, 9th',
        'SATURN': '3rd, 7th, 10th',
        'RAHU': '5th, 7th, 9th',
        'KETU': '5th, 7th, 9th'
    }
    
    missing_planets = []
    for planet, aspects in complete_aspects.items():
        if planet not in existing_planets:
            missing_planets.append({
                'Planet': planet,
                'Aspect Houses': aspects,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            })
    
    if missing_planets:
        try:
            db[collection_name].insert_many(missing_planets)
            print(f"✅ Added {len(missing_planets)} missing planets to {collection_name}")
        except Exception as e:
            print(f"❌ Failed to update {collection_name}: {e}")
    else:
        print(f"✅ Collection {collection_name} is already complete")

def main():
    """Main function to initialize all astrological constants"""
    print("🚀 Initializing Astrological Constants in MongoDB")
    print("=" * 60)
    
    # Connect to database
    db = connect_to_mongodb()
    
    # Initialize collections
    print("\n📊 Initializing Collections:")
    initialize_planetary_rotation_periods(db)
    initialize_sign_degree_mappings(db)
    update_existing_aspects_collection(db)
    
    # Show final status
    print("\n📈 Final Status:")
    collections = [
        'astro_planetary_rotation_periods',
        'astro_sign_degree_mappings',
        'astro_planets_aspects',
        'astro_house_names',
        'astro_planets_exalt_debilitate',
        'astro_planets_relationships',
        'astro_stars'
    ]
    
    for collection in collections:
        if collection in db.list_collection_names():
            count = db[collection].count_documents({})
            print(f"  ✅ {collection}: {count} documents")
        else:
            print(f"  ❌ {collection}: Collection not found")
    
    print("\n🎉 Astrological constants initialization completed!")
    print("\nNext steps:")
    print("1. Update main_rule_engine.py to use database_constants.py")
    print("2. Replace hardcoded user/member profile IDs with dynamic parameters")
    print("3. Test the updated rule engine with database-driven constants")

if __name__ == "__main__":
    main()
