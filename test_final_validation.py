#!/usr/bin/env python3
"""
Test final validation results with corrected degree ranges
"""

def test_degree_validation():
    print("🔍 Testing Final Degree Range Validation")
    print("=" * 50)
    
    # Test the corrected logic: 0-10° and 20-30°
    test_cases = [
        # (longitude, description)
        (330.0, "Start of Meenam (0° in house)"),
        (335.0, "Middle of start range (5° in house)"),
        (340.0, "End of start range (10° in house)"),
        (344.51, "Between ranges (14.5° in house)"),
        (350.0, "Start of end range (20° in house)"),
        (355.0, "Middle of end range (25° in house)"),
        (360.0, "End of end range (30° in house)"),
        (328.34, "Before Meenam (in Kumbam)"),
    ]
    
    print("Testing corrected degree ranges:")
    print("Start range: 0-10° in house (330-340° absolute)")
    print("End range: 20-30° in house (350-360° absolute)")
    print()
    
    for longitude, description in test_cases:
        # Check if in Meenam
        in_meenam = 330 <= longitude <= 360
        
        if in_meenam:
            # Calculate degree within house
            degree_in_house = longitude - 330
            
            # Check start range (0-10° in house)
            start_range_valid = 0 <= degree_in_house <= 10
            
            # Check end range (20-30° in house)
            end_range_valid = 20 <= degree_in_house <= 30
            
            print(f"📍 {longitude:.2f}° → {degree_in_house:.1f}° in Meenam")
            print(f"   Start range (0-10°): {start_range_valid}")
            print(f"   End range (20-30°): {end_range_valid}")
            print(f"   Description: {description}")
            
            if start_range_valid:
                print(f"   ✅ VALID for start prediction")
            elif end_range_valid:
                print(f"   ✅ VALID for end prediction")
            else:
                print(f"   🔄 NEEDS REFINEMENT - between ranges")
        else:
            print(f"📍 {longitude:.2f}° → NOT in Meenam")
            print(f"   Start range: False")
            print(f"   End range: False")
            print(f"   Description: {description}")
            print(f"   🔄 NEEDS REFINEMENT - not in target sign")
        
        print()
    
    # Test the requirement
    print("🎯 Requirement Summary:")
    print("Both start_degree_range and end_degree_range must be TRUE")
    print("If either is FALSE, predict again until both become TRUE")
    print()
    
    # Perfect case
    print("🌟 Perfect Case Example:")
    start_long = 335.0  # 5° in Meenam (0-10° range)
    end_long = 355.0    # 25° in Meenam (20-30° range)
    
    start_degree_in_house = start_long - 330
    end_degree_in_house = end_long - 330
    
    start_valid = 0 <= start_degree_in_house <= 10
    end_valid = 20 <= end_degree_in_house <= 30
    
    print(f"   Start: {start_long}° → {start_degree_in_house:.1f}° in Meenam → Valid: {start_valid}")
    print(f"   End: {end_long}° → {end_degree_in_house:.1f}° in Meenam → Valid: {end_valid}")
    print(f"   Both valid: {start_valid and end_valid}")
    
    if start_valid and end_valid:
        print(f"   ✅ STOP: Both validations TRUE - prediction complete!")
    else:
        print(f"   🔄 CONTINUE: Keep predicting until both become TRUE")

if __name__ == "__main__":
    test_degree_validation()
