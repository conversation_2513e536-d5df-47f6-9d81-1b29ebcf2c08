#!/usr/bin/env python3
"""
Fortune Lens Rule Engine Documentation - PDF Conversion Script

This script converts the markdown documentation files to PDF format
for printing and distribution.

Requirements:
- pip install markdown2 weasyprint
- or pip install markdown2 pdfkit wkhtmltopdf

Usage:
python convert_to_pdf.py
"""

import os
import sys
from pathlib import Path

def install_requirements():
    """Install required packages for PDF conversion"""
    try:
        import markdown2
        print("✅ markdown2 already installed")

        # Try weasyprint first, fallback to pdfkit if it fails
        try:
            import weasyprint
            print("✅ weasyprint available")
            return True, "weasyprint"
        except (ImportError, OSError) as e:
            print(f"⚠️ weasyprint not available: {e}")
            try:
                import pdfkit
                print("✅ pdfkit available")
                return True, "pdfkit"
            except ImportError:
                print("📦 Installing pdfkit...")
                os.system("pip install pdfkit")
                try:
                    import pdfkit
                    print("✅ pdfkit installed successfully")
                    return True, "pdfkit"
                except ImportError:
                    print("❌ Failed to install pdfkit")
                    return False, None

    except ImportError:
        print("📦 Installing required packages...")
        os.system("pip install markdown2 pdfkit")
        try:
            import markdown2
            import pdfkit
            print("✅ Packages installed successfully")
            return True, "pdfkit"
        except ImportError:
            print("❌ Failed to install packages. Please install manually:")
            print("pip install markdown2 pdfkit")
            print("Note: pdfkit also requires wkhtmltopdf to be installed:")
            print("macOS: brew install wkhtmltopdf")
            return False, None

def convert_markdown_to_pdf(markdown_file, output_file, pdf_engine="pdfkit"):
    """Convert markdown file to PDF using specified engine"""
    try:
        import markdown2

        # Read markdown file
        with open(markdown_file, 'r', encoding='utf-8') as f:
            markdown_content = f.read()

        # Convert markdown to HTML
        html_content = markdown2.markdown(markdown_content, extras=['tables', 'fenced-code-blocks'])

        # Add CSS styling for better PDF output
        css_style = """
        <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 5px;
        }
        h3 {
            color: #7f8c8d;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #bdc3c7;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #ecf0f1;
            font-weight: bold;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .page-break {
            page-break-before: always;
        }
        @page {
            margin: 2cm;
        }
        </style>
        """

        # Combine CSS and HTML
        full_html = f"<!DOCTYPE html><html><head>{css_style}</head><body>{html_content}</body></html>"

        # Convert to PDF based on available engine
        if pdf_engine == "weasyprint":
            import weasyprint
            weasyprint.HTML(string=full_html).write_pdf(output_file)
        elif pdf_engine == "pdfkit":
            import pdfkit
            options = {
                'page-size': 'A4',
                'margin-top': '0.75in',
                'margin-right': '0.75in',
                'margin-bottom': '0.75in',
                'margin-left': '0.75in',
                'encoding': "UTF-8",
                'no-outline': None
            }
            pdfkit.from_string(full_html, output_file, options=options)
        else:
            # Fallback: save as HTML
            html_output = output_file.replace('.pdf', '.html')
            with open(html_output, 'w', encoding='utf-8') as f:
                f.write(full_html)
            print(f"⚠️ Saved as HTML instead: {html_output}")
            return True

        print(f"✅ Converted {markdown_file} → {output_file}")
        return True

    except Exception as e:
        print(f"❌ Error converting {markdown_file}: {e}")
        # Fallback: save as HTML
        try:
            html_output = output_file.replace('.pdf', '.html')
            with open(html_output, 'w', encoding='utf-8') as f:
                f.write(full_html)
            print(f"⚠️ Saved as HTML fallback: {html_output}")
            return True
        except:
            return False

def create_combined_pdf():
    """Create a combined PDF with all documentation"""
    try:
        import markdown2
        import weasyprint
        
        # List of markdown files to combine
        markdown_files = [
            "fortune_lens_rule_engine_print_ready.md",
            "main_rule_engine_technical_analysis.md",
            "sample_query_processing_walkthrough.md",
            "function_reference_table.md"
        ]
        
        combined_content = ""
        
        for md_file in markdown_files:
            if os.path.exists(md_file):
                with open(md_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    combined_content += content + "\n\n<div class='page-break'></div>\n\n"
                print(f"📄 Added {md_file} to combined document")
            else:
                print(f"⚠️ File not found: {md_file}")
        
        # Convert combined markdown to HTML
        html_content = markdown2.markdown(combined_content, extras=['tables', 'fenced-code-blocks'])
        
        # Enhanced CSS for combined document
        css_style = """
        <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            page-break-before: always;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 5px;
        }
        h3 {
            color: #7f8c8d;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            font-size: 12px;
        }
        th, td {
            border: 1px solid #bdc3c7;
            padding: 6px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #ecf0f1;
            font-weight: bold;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 11px;
        }
        .page-break {
            page-break-before: always;
        }
        @page {
            margin: 2cm;
            @bottom-right {
                content: "Page " counter(page) " of " counter(pages);
            }
            @top-center {
                content: "Fortune Lens Rule Engine - Technical Documentation";
                font-size: 10px;
                color: #7f8c8d;
            }
        }
        </style>
        """
        
        # Create title page
        title_page = """
        <div style="text-align: center; margin-top: 200px;">
            <h1 style="font-size: 36px; border: none;">Fortune Lens Rule Engine</h1>
            <h2 style="font-size: 24px; border: none; color: #7f8c8d;">Complete Technical Documentation</h2>
            <p style="font-size: 18px; margin-top: 50px;">Version 1.0</p>
            <p style="font-size: 16px;">Generated: """ + str(Path().absolute()) + """</p>
            <p style="font-size: 14px; margin-top: 100px;">
                This document contains comprehensive technical analysis,<br>
                function documentation, and implementation guidelines<br>
                for the Fortune Lens astrological rule engine.
            </p>
        </div>
        <div class='page-break'></div>
        """
        
        # Combine title page and content
        full_html = f"<!DOCTYPE html><html><head>{css_style}</head><body>{title_page}{html_content}</body></html>"
        
        # Convert to PDF
        output_file = "Fortune_Lens_Rule_Engine_Complete_Documentation.pdf"
        weasyprint.HTML(string=full_html).write_pdf(output_file)
        print(f"✅ Created combined PDF: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating combined PDF: {e}")
        return False

def main():
    """Main function to convert all documentation to PDF"""
    print("🚀 Fortune Lens Rule Engine - PDF Conversion Tool")
    print("=" * 60)

    # Check and install requirements
    success, pdf_engine = install_requirements()
    if not success:
        print("❌ Could not install required packages. Creating HTML files instead...")
        pdf_engine = "html"
    
    # Individual PDF conversions
    markdown_files = [
        "fortune_lens_rule_engine_print_ready.md",
        "main_rule_engine_technical_analysis.md", 
        "sample_query_processing_walkthrough.md",
        "function_reference_table.md"
    ]
    
    print("\n📄 Converting individual files to PDF...")
    success_count = 0
    
    for md_file in markdown_files:
        if os.path.exists(md_file):
            if pdf_engine == "html":
                output_file = md_file.replace('.md', '.html')
            else:
                output_file = md_file.replace('.md', '.pdf')
            if convert_markdown_to_pdf(md_file, output_file, pdf_engine):
                success_count += 1
        else:
            print(f"⚠️ File not found: {md_file}")
    
    print(f"\n✅ Successfully converted {success_count}/{len(markdown_files)} files")
    
    # Create combined PDF
    print("\n📚 Creating combined documentation PDF...")
    if create_combined_pdf():
        print("✅ Combined PDF created successfully")
    
    print("\n🎉 PDF conversion complete!")
    print("\nGenerated files:")
    for md_file in markdown_files:
        pdf_file = md_file.replace('.md', '.pdf')
        if os.path.exists(pdf_file):
            print(f"  📄 {pdf_file}")
    
    if os.path.exists("Fortune_Lens_Rule_Engine_Complete_Documentation.pdf"):
        print(f"  📚 Fortune_Lens_Rule_Engine_Complete_Documentation.pdf")
    
    print("\n💡 Tip: Use the combined PDF for complete documentation")
    print("    or individual PDFs for specific sections.")

if __name__ == "__main__":
    main()
