#!/usr/bin/env python3
"""
Astro Insights Pro - API Testing Script

This script tests all API endpoints to ensure they're working correctly.
"""

import requests
import json
import sys
from datetime import datetime

BASE_URL = "http://localhost:5003"

def test_endpoint(method, endpoint, data=None, headers=None, expected_status=200):
    """Test a single API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers, timeout=10)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers, timeout=10)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data, headers=headers, timeout=10)
        elif method.upper() == 'DELETE':
            response = requests.delete(url, headers=headers, timeout=10)
        else:
            return {"status": "ERROR", "message": f"Unsupported method: {method}"}
        
        result = {
            "status": "SUCCESS" if response.status_code == expected_status else "FAILED",
            "status_code": response.status_code,
            "expected_status": expected_status,
            "response_size": len(response.content),
            "content_type": response.headers.get('content-type', 'unknown')
        }
        
        # Try to parse JSON response
        try:
            json_response = response.json()
            result["has_json"] = True
            result["response_keys"] = list(json_response.keys()) if isinstance(json_response, dict) else []
        except:
            result["has_json"] = False
            result["response_preview"] = response.text[:100] if response.text else "Empty response"
        
        return result
        
    except requests.exceptions.RequestException as e:
        return {"status": "ERROR", "message": str(e)}

def main():
    """Test all API endpoints"""
    print("🚀 Astro Insights Pro - API Testing")
    print("=" * 60)
    
    # Test cases: (method, endpoint, data, expected_status, description)
    test_cases = [
        # Core API
        ("GET", "/", None, 200, "Root endpoint"),
        ("GET", "/api/", None, 200, "API health check"),
        
        # Rule Engine API
        ("GET", "/api/rule-engine/", None, 200, "Rule Engine documentation"),
        ("POST", "/api/rule-engine/", {
            "user_profile_id": 1,
            "member_profile_id": 1,
            "query": "7th_House_Ruling_Planet Bhukti_Dates",
            "chart_type": "D1"
        }, 404, "Rule Engine query (chart data not found expected)"),
        
        # Charts API
        ("GET", "/api/charts/types", None, 200, "Chart types"),
        
        # Daily Panchanga API
        ("GET", "/api/daily-panchanga/daily?date=2024-01-15&place=Chennai", None, 200, "Daily Panchanga"),
        ("GET", "/api/daily-panchanga/monthly?year=2024&month=1&place=Chennai", None, 200, "Monthly Panchanga"),
        ("GET", "/api/daily-panchanga/festivals?year=2024&place=Chennai", None, 200, "Festivals"),
        ("GET", "/api/daily-panchanga/tamil-date?date=2024-01-15", None, 200, "Tamil Date"),
        
        # Authentication endpoints (expect 400 for missing data)
        ("POST", "/api/auth/register", {}, 400, "Register endpoint (validation error expected)"),
        ("POST", "/api/auth/login", {}, 400, "Login endpoint (validation error expected)"),
        
        # Protected endpoints (expect 401 for missing auth)
        ("GET", "/api/users", None, 401, "Users list (auth required)"),
        ("GET", "/api/member-profiles", None, 401, "Member profiles (auth required)"),
        ("POST", "/api/charts/generate", {}, 401, "Chart generation (auth required)"),
        ("POST", "/api/marriage-matching/lagna", {}, 401, "Marriage matching (auth required)"),
        ("POST", "/api/career-prediction/medical-profession", {}, 400, "Medical prediction (missing data expected)"),
    ]
    
    results = []
    success_count = 0
    total_count = len(test_cases)
    
    for method, endpoint, data, expected_status, description in test_cases:
        print(f"\n📍 Testing: {description}")
        print(f"   {method} {endpoint}")
        
        result = test_endpoint(method, endpoint, data, expected_status=expected_status)
        result["description"] = description
        result["endpoint"] = endpoint
        result["method"] = method
        
        if result["status"] == "SUCCESS":
            print(f"   ✅ {result['status']} (Status: {result['status_code']})")
            success_count += 1
        elif result["status"] == "FAILED":
            print(f"   ❌ {result['status']} (Expected: {result['expected_status']}, Got: {result['status_code']})")
        else:
            print(f"   🔥 {result['status']}: {result.get('message', 'Unknown error')}")
        
        results.append(result)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 API TESTING SUMMARY")
    print("=" * 60)
    print(f"Total Endpoints Tested: {total_count}")
    print(f"Successful Tests: {success_count}")
    print(f"Failed Tests: {total_count - success_count}")
    print(f"Success Rate: {(success_count/total_count)*100:.1f}%")
    
    # Detailed results
    print("\n📋 DETAILED RESULTS:")
    print("-" * 60)
    
    for result in results:
        status_icon = "✅" if result["status"] == "SUCCESS" else "❌" if result["status"] == "FAILED" else "🔥"
        print(f"{status_icon} {result['method']} {result['endpoint']}")
        print(f"   {result['description']}")
        print(f"   Status: {result['status']} ({result.get('status_code', 'N/A')})")
        
        if result.get('has_json'):
            print(f"   Response: JSON with keys: {result.get('response_keys', [])}")
        elif result.get('response_preview'):
            print(f"   Response: {result['response_preview']}")
        
        if result["status"] == "ERROR":
            print(f"   Error: {result.get('message', 'Unknown error')}")
        
        print()
    
    # Recommendations
    print("🔧 RECOMMENDATIONS:")
    print("-" * 60)
    
    failed_tests = [r for r in results if r["status"] != "SUCCESS"]
    if not failed_tests:
        print("🎉 All API endpoints are working correctly!")
    else:
        print("Issues found:")
        for result in failed_tests:
            if result["status"] == "ERROR":
                print(f"• Fix connection/server issue for {result['endpoint']}")
            elif result["status"] == "FAILED":
                print(f"• Check status code for {result['endpoint']} (Expected: {result['expected_status']}, Got: {result['status_code']})")
    
    print(f"\n🕒 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
