#!/usr/bin/env python3
"""
Script to fix BaseConfig references to BaseConfiguration
"""

import os
import re

def fix_baseconfig_references():
    """Fix all BaseConfig references to BaseConfiguration"""
    
    # Get the astro_insights_pro directory
    app_dir = os.path.join(os.path.dirname(__file__), '..', 'astro_insights_pro')
    
    # Find all Python files
    for root, dirs, files in os.walk(app_dir):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                # Read the file
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check if file contains BaseConfig references
                if 'BaseConfig' in content:
                    print(f"Fixing {file_path}")
                    
                    # Replace BaseConfig with BaseConfiguration
                    content = content.replace('from ...config import BaseConfig', 'from ...config import BaseConfiguration')
                    content = content.replace('from ..config import BaseConfig', 'from ..config import BaseConfiguration')
                    content = content.replace('BaseConfig.', 'BaseConfiguration.')
                    
                    # Write the file back
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print(f"Fixed {file_path}")

if __name__ == '__main__':
    fix_baseconfig_references()
    print("All BaseConfig references fixed!")
