#!/usr/bin/env python3
"""
Clean MongoDB and Upload Excel Data Script
Removes all existing data and uploads Excel data with same structure
"""

import pandas as pd
import pymongo
from pymongo import MongoClient
from bson import ObjectId
import json
from datetime import datetime, time
import sys
import os

# Add the astro_insights_pro app to the path
sys.path.append('astro_insights_pro')

try:
    from app.services.chart_service import generate_chart
    CHART_GENERATION_AVAILABLE = True
    print("Chart generation service loaded successfully")
except ImportError as e:
    print(f"Warning: Chart generation not available: {e}")
    CHART_GENERATION_AVAILABLE = False

def get_location_info(place, state, country):
    """Simple location info function with Tamil Nadu locations"""
    locations = {
        'cuddalore': {'latitude': 11.7480, 'longitude': 79.7714},
        'pondicherry': {'latitude': 11.9416, 'longitude': 79.8083},
        'salem': {'latitude': 11.6643, 'longitude': 78.1460},
        'dharmapuri': {'latitude': 12.1211, 'longitude': 78.1583},
        'kumbakonam': {'latitude': 10.9601, 'longitude': 79.3788},
        'vellore': {'latitude': 12.9165, 'longitude': 79.1325},
        'chennai': {'latitude': 13.0827, 'longitude': 80.2707},
        'madurai': {'latitude': 9.9252, 'longitude': 78.1198},
        'coimbatore': {'latitude': 11.0168, 'longitude': 76.9558},
        'tiruchirappalli': {'latitude': 10.7905, 'longitude': 78.7047},
        'tirunelveli': {'latitude': 8.7139, 'longitude': 77.7567}
    }
    
    place_lower = place.lower() if place else ''
    if place_lower in locations:
        return locations[place_lower]
    else:
        # Default to Chennai coordinates
        return {'latitude': 13.0827, 'longitude': 80.2707}

class DatabaseCleaner:
    def __init__(self, mongodb_uri='mongodb://localhost:27017/', db_name='fortune_lens'):
        """Initialize with MongoDB connection"""
        self.client = MongoClient(mongodb_uri)
        self.db = self.client[db_name]
        self.collections_to_clean = [
            'user_profile',
            'member_profile', 
            'user_member_astro_profile_data'
        ]
    
    def create_backup(self):
        """Create backup before cleaning"""
        backup_dir = f'mongodb_backup_before_clean_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        os.makedirs(backup_dir, exist_ok=True)
        
        print(f"Creating backup in: {backup_dir}")
        
        for collection_name in self.collections_to_clean:
            if collection_name in self.db.list_collection_names():
                collection = self.db[collection_name]
                documents = list(collection.find())
                
                # Convert ObjectId to string for JSON serialization
                def convert_objectid(obj):
                    if hasattr(obj, '__dict__'):
                        return str(obj)
                    elif isinstance(obj, dict):
                        return {k: convert_objectid(v) for k, v in obj.items()}
                    elif isinstance(obj, list):
                        return [convert_objectid(item) for item in obj]
                    else:
                        return obj
                
                documents_clean = [convert_objectid(doc) for doc in documents]
                
                # Save to JSON file
                backup_file = os.path.join(backup_dir, f'{collection_name}.json')
                with open(backup_file, 'w') as f:
                    json.dump(documents_clean, f, indent=2, default=str)
                
                print(f"Backed up {len(documents)} documents from {collection_name}")
        
        return backup_dir
    
    def clean_collections(self):
        """Remove all data from specified collections"""
        print("\nCleaning collections...")
        
        for collection_name in self.collections_to_clean:
            if collection_name in self.db.list_collection_names():
                result = self.db[collection_name].delete_many({})
                print(f"Deleted {result.deleted_count} documents from {collection_name}")
            else:
                print(f"Collection {collection_name} does not exist")
    
    def close(self):
        """Close MongoDB connection"""
        self.client.close()

class ExcelUploader:
    def __init__(self, mongodb_uri='mongodb://localhost:27017/', db_name='fortune_lens'):
        """Initialize uploader"""
        self.client = MongoClient(mongodb_uri)
        self.db = self.client[db_name]
        self.collections = {
            'user_profile': 'user_profile',
            'member_profile': 'member_profile',
            'astro_data': 'user_member_astro_profile_data'
        }
    
    def load_excel_data(self, excel_file='Astro FL User Data Master v1.xlsx'):
        """Load Excel data"""
        try:
            df = pd.read_excel(excel_file, sheet_name='User Astro Data Master')
            print(f"Loaded {len(df)} records from Excel")
            
            # Clean and validate data
            df = df.dropna(subset=['user_birthdate', 'user_birthtime', 'user_birthplace'])
            print(f"After removing incomplete records: {len(df)} records")
            
            return df
        except Exception as e:
            print(f"Error loading Excel data: {e}")
            return None
    
    def create_user_profile(self, user_data):
        """Create user profile maintaining same structure"""
        user_id = int(user_data['user_id'])
        
        user_profile = {
            '_id': ObjectId(),
            'user_profile_id': user_id,
            'email': f"user_{user_id}@fortunelens.com",
            'password': '$2b$12$default.hash.for.excel.import',
            'name': str(user_data['user_name']) if pd.notna(user_data['user_name']) else f"User {user_id}",
            'mobile': str(user_data.get('user_mobile', '0000000000')),
            'unique_key': f"FL{user_id}{datetime.now().strftime('%H%M%S')}",
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        return user_profile
    
    def create_member_profile(self, user_data, user_profile_id):
        """Create member profile maintaining same structure"""
        user_profile_id = int(user_profile_id)
        member_id = int(user_data['member_id'])
        
        # Get location info
        location_info = get_location_info(
            user_data['user_birthplace'],
            user_data.get('user_state', ''),
            user_data.get('user_country', 'India')
        )
        
        member_profile = {
            '_id': ObjectId(),
            'member_profile_id': member_id,
            'user_profile_id': user_profile_id,
            'unique_key': f"M{user_profile_id}_{member_id}_{datetime.now().strftime('%H%M%S')}",
            'name': str(user_data['user_name']) if pd.notna(user_data['user_name']) else f"Member {member_id}",
            'relation': 'self' if member_id == 1 else f"member_{member_id}",
            'birth_date': user_data['user_birthdate'].strftime('%Y-%m-%d') if pd.notna(user_data['user_birthdate']) else None,
            'birth_time': str(user_data['user_birthtime']) if pd.notna(user_data['user_birthtime']) else None,
            'birth_place': str(user_data['user_birthplace']),
            'state': str(user_data.get('user_state', '')),
            'country': str(user_data.get('user_country', 'India')),
            'latitude': float(location_info.get('latitude', 0.0)),
            'longitude': float(location_info.get('longitude', 0.0)),
            'gender': str(user_data.get('user_gender', '')).title() if pd.notna(user_data.get('user_gender')) else None,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        return member_profile
    
    def create_astro_profile(self, member_profile, user_data):
        """Create astrological profile with chart data"""
        # Prepare birth data for chart generation
        birth_data = {
            'user_birthdate': member_profile['birth_date'],
            'user_birthtime': member_profile['birth_time'],
            'user_birthplace': member_profile['birth_place'],
            'user_state': member_profile['state'],
            'user_country': member_profile['country']
        }
        
        # Generate chart data
        chart_data = {}
        if CHART_GENERATION_AVAILABLE:
            try:
                chart_data = generate_chart(birth_data)
                print(f"✓ Generated chart data for user {member_profile['user_profile_id']}, member {member_profile['member_profile_id']}")
            except Exception as e:
                print(f"✗ Error generating chart data for user {member_profile['user_profile_id']}, member {member_profile['member_profile_id']}: {e}")
                chart_data = {}
        else:
            print(f"⚠ Chart generation skipped for user {member_profile['user_profile_id']}, member {member_profile['member_profile_id']}")
        
        # Create astro profile (copy member profile structure)
        astro_profile = dict(member_profile)
        astro_profile['_id'] = ObjectId()  # New ID for astro collection
        
        # Add astro-specific data
        astro_profile['astro_data'] = {
            'user_profession': str(user_data.get('user_profession', '')),
            'user_data_label_1': str(user_data.get('User_Data_Label_1 (Profession)', '')),
            'user_data_label_2': str(user_data.get('User_Data_Label_2 (Specialization)', '')),
            'user_data_label_3': str(user_data.get('User_Data_Label_3 (Foreign / Domestic)', '')),
            'user_data_label_4': str(user_data.get('User_Data_Label_4 (Foreign Travel Date1)', '')),
            'user_data_label_5': str(user_data.get('User_Data_Label_5 (Foreign Travel Country1)', ''))
        }
        
        # Add chart data
        astro_profile['chart_data'] = chart_data
        
        return astro_profile
    
    def upload_all_data(self, df):
        """Upload all Excel data to MongoDB"""
        print(f"\nStarting upload of {len(df)} records...")
        
        # Group by user_id
        user_groups = df.groupby('user_id')
        
        success_count = 0
        error_count = 0
        
        for user_id, user_data_group in user_groups:
            try:
                print(f"\n=== Processing User ID: {user_id} ===")
                
                # Create user profile (from first record)
                first_record = user_data_group.iloc[0]
                user_profile = self.create_user_profile(first_record)
                
                # Insert user profile
                self.db[self.collections['user_profile']].insert_one(user_profile)
                print(f"✓ Created user profile: {user_profile['name']}")
                
                # Create member profiles and astro data for each member
                for idx, member_data in user_data_group.iterrows():
                    member_profile = self.create_member_profile(member_data, user_id)
                    
                    # Insert member profile
                    self.db[self.collections['member_profile']].insert_one(member_profile)
                    print(f"✓ Created member {member_data['member_id']}: {member_profile['name']}")
                    
                    # Create and insert astro profile
                    astro_profile = self.create_astro_profile(member_profile, member_data)
                    self.db[self.collections['astro_data']].insert_one(astro_profile)
                    print(f"✓ Created astro profile for member {member_data['member_id']}")
                
                success_count += 1
                
            except Exception as e:
                print(f"✗ Error processing user {user_id}: {e}")
                error_count += 1
        
        print(f"\n=== UPLOAD SUMMARY ===")
        print(f"✓ Successful: {success_count}")
        print(f"✗ Errors: {error_count}")
        print(f"📊 Total processed: {success_count + error_count}")
        
        return success_count, error_count
    
    def close(self):
        """Close MongoDB connection"""
        self.client.close()

def main():
    """Main function"""
    print("=" * 60)
    print("FORTUNE LENS DATABASE CLEAN & UPLOAD")
    print("=" * 60)
    
    # Ask for confirmation
    print("\n⚠️  WARNING: This will DELETE ALL existing data and upload Excel data!")
    response = input("Are you sure you want to proceed? (type 'YES' to confirm): ")
    
    if response != 'YES':
        print("Operation cancelled.")
        return
    
    # Initialize services
    cleaner = DatabaseCleaner()
    uploader = ExcelUploader()
    
    try:
        # Step 1: Create backup
        print("\n📦 Step 1: Creating backup...")
        backup_dir = cleaner.create_backup()
        print(f"✓ Backup created: {backup_dir}")
        
        # Step 2: Clean database
        print("\n🧹 Step 2: Cleaning database...")
        cleaner.clean_collections()
        print("✓ Database cleaned")
        
        # Step 3: Load Excel data
        print("\n📊 Step 3: Loading Excel data...")
        df = uploader.load_excel_data()
        if df is None:
            print("✗ Failed to load Excel data")
            return
        
        print(f"✓ Loaded {len(df)} records")
        print(f"📈 Unique users: {df['user_id'].nunique()}")
        print(f"👥 Member distribution: {df['member_id'].value_counts().to_dict()}")
        
        # Step 4: Upload data
        print("\n⬆️  Step 4: Uploading data...")
        success_count, error_count = uploader.upload_all_data(df)
        
        # Step 5: Final summary
        print("\n" + "=" * 60)
        print("OPERATION COMPLETED")
        print("=" * 60)
        
        if error_count == 0:
            print("🎉 SUCCESS: All data uploaded successfully!")
            print(f"📊 Users created: {success_count}")
            print(f"📦 Backup location: {backup_dir}")
        else:
            print(f"⚠️  PARTIAL SUCCESS: {success_count} users uploaded, {error_count} errors")
            print(f"📦 Backup location: {backup_dir}")
        
    except Exception as e:
        print(f"✗ Fatal error: {e}")
    
    finally:
        cleaner.close()
        uploader.close()

if __name__ == "__main__":
    main()
