#!/usr/bin/env python3
"""
Test script to demonstrate the simplified KOCHARAM filter
"""

import requests
import json
import time

def test_simple_kocharam():
    """Test the simplified KOCHARAM filter"""
    
    # API endpoint
    base_url = "http://localhost:5003"
    endpoint = f"{base_url}/api/rule-engine/"
    
    # Your specific query
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Bhukti_Dates)AND KOCHARAM_FILTER(JUPITER in 7th_House)",
        "chart_type": "D1"
    }
    
    print("🎯 SIMPLIFIED KOCHARAM FILTER TEST")
    print("=" * 60)
    print(f"Query: {test_data['query']}")
    print()
    
    print("🔧 SIMPLIFIED APPROACH:")
    print("-" * 30)
    print("1. Get current Jupiter degree from dasha start chart")
    print("2. Calculate distance to target house (Meenam = 330°)")
    print("3. Use Jupiter speed (0.0831°/day) to estimate transit time")
    print("4. Verify with ONE chart generation at predicted date")
    print("5. Return simple result (no complex searching)")
    print()
    
    print("⚡ BENEFITS:")
    print("-" * 15)
    print("• Much faster execution (1 chart vs 100+ charts)")
    print("• Simple degree-based calculation")
    print("• Single verification step")
    print("• Clear result structure")
    print("• No complex degree range searching")
    print()
    
    try:
        print("📡 Sending API Request...")
        start_time = time.time()
        
        response = requests.post(endpoint, json=test_data, timeout=30)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"⏱️  Execution Time: {execution_time:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        print()
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ SUCCESS - Simplified KOCHARAM Results:")
            print("=" * 50)
            
            if 'result' in result and 'dasha_dates' in result['result']:
                dasha_dates = result['result']['dasha_dates']
                print(f"Total Periods: {len(dasha_dates)}")
                
                # Show first few periods with KOCHARAM details
                for i, period in enumerate(dasha_dates[:3]):
                    print(f"\n📅 Period {i+1}:")
                    print(f"  Planet: {period.get('planet', 'N/A')}")
                    print(f"  Start: {period.get('start_date', 'N/A')}")
                    print(f"  End: {period.get('end_date', 'N/A')}")
                    
                    if 'kocharam_filter' in period:
                        kocharam = period['kocharam_filter']
                        print(f"  🪐 KOCHARAM Results:")
                        print(f"    Method: {kocharam.get('calculation_method', 'N/A')}")
                        print(f"    Transit Found: {kocharam.get('transit_found', 'N/A')}")
                        print(f"    Predicted Date: {kocharam.get('predicted_date', 'N/A')}")
                        print(f"    Predicted Sign: {kocharam.get('predicted_sign', 'N/A')}")
                        print(f"    Sign Validation: {kocharam.get('sign_validation', 'N/A')}")
                        print(f"    Estimated Days: {kocharam.get('estimated_days', 'N/A')}")
                        
                        if kocharam.get('transit_found'):
                            print(f"    ✅ Jupiter will enter {kocharam.get('target_house_name')} on {kocharam.get('predicted_date')}")
                        else:
                            print(f"    ❌ No Jupiter transit to {kocharam.get('target_house_name')} in this period")
            
            print(f"\n📄 Sample KOCHARAM Result Structure:")
            print("-" * 40)
            if 'result' in result and 'dasha_dates' in result['result'] and len(result['result']['dasha_dates']) > 0:
                sample_kocharam = result['result']['dasha_dates'][0].get('kocharam_filter', {})
                for key, value in sample_kocharam.items():
                    if key not in ['dasha_start_chart', 'predicted_date_chart']:  # Skip large chart data
                        print(f"  {key}: {value}")
                        
        else:
            print("❌ ERROR Response:")
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Flask server not running")
        print("💡 Start the server with: cd astro_insights_pro && python run.py")
        
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: Request took too long")
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")

def explain_simplified_approach():
    """Explain the simplified approach"""
    
    print("\n🔍 SIMPLIFIED KOCHARAM APPROACH EXPLANATION")
    print("=" * 60)
    
    print("\n📊 EXAMPLE CALCULATION:")
    print("-" * 25)
    print("Current Jupiter Position: 36.39° (Rishabam/Taurus)")
    print("Target House: 7th House = Meenam (330°-360°)")
    print("Distance to Travel: 330° - 36.39° = 293.61°")
    print("Jupiter Speed: 0.0831°/day")
    print("Estimated Days: 293.61° ÷ 0.0831°/day = 3,533 days")
    print("Estimated Date: Start Date + 3,533 days")
    print()
    
    print("🔍 VERIFICATION STEP:")
    print("-" * 20)
    print("1. Generate D1 chart for estimated date")
    print("2. Check Jupiter's actual position")
    print("3. Verify if Jupiter is in Meenam (target house)")
    print("4. Return result with validation flags")
    print()
    
    print("📋 RESULT STRUCTURE:")
    print("-" * 20)
    print("• condition: 'JUPITER in Meenam'")
    print("• predicted_date: '1985-03-15'")
    print("• predicted_longitude: 331.25°")
    print("• predicted_sign: 'Meenam'")
    print("• transit_found: True/False")
    print("• sign_validation: True/False")
    print("• calculation_method: 'Simple degree calculation + verification'")
    print("• estimated_days: 3533")

if __name__ == "__main__":
    explain_simplified_approach()
    test_simple_kocharam()
