#!/usr/bin/env python3
"""
Test script to verify KOCHARAM filter aspect calculation implementation
"""

import requests
import json
import time
def test_aspect_calculation():
    """Test JUPITER ASPECT 7th_House calculation"""

    # API endpoint
    base_url = "http://localhost:5003"
    endpoint = f"{base_url}/api/rule-engine/"

    # Test data - Original user query: SATURN Dasa with age filter and JUPITER ASPECT 7th_House
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(SATURN Dasa_Bhukti_Dates) AND Member_Age >= 23 AND <= 26 AND KOCHARAM_FILTER(JUPITER ASPECT 7th_House)",
        "chart_type": "D1"
    }

    print("🔍 Testing KOCHARAM Filter: JUPITER ASPECT 7th_House")
    print("=" * 70)
    print(f"Query: {test_data['query']}")
    print(f"Endpoint: {endpoint}")
    print()

    try:
        response = requests.post(endpoint, json=test_data, timeout=60)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
        else:
            print("❌ Error Response:")
            print(response.text)
            return

        print("📊 RESULT:")
        print("=" * 50)

        if result.get('success'):
            print("✅ Query executed successfully")

            # Check for periods
            periods = result.get('periods', [])
            print(f"📅 Total periods found: {len(periods)}")

            # Also check for kocharam_result which might contain the actual results
            kocharam_result = result.get('kocharam_result', {})
            if kocharam_result:
                print(f"🎯 KOCHARAM Result found:")
                print(f"   Periods passed: {kocharam_result.get('periods_passed', 'N/A')}")
                print(f"   Success rate: {kocharam_result.get('success_rate', 'N/A')}%")
                print(f"   Next favorable period: {kocharam_result.get('next_favorable_period', 'N/A')}")

            if periods:
                for i, period in enumerate(periods[:3]):  # Show first 3 periods
                    print(f"\n🔍 Period {i+1}:")
                    print(f"   Planet: {period.get('planet_name', 'N/A')}")
                    print(f"   Start: {period.get('start_date', 'N/A')}")
                    print(f"   End: {period.get('end_date', 'N/A')}")

                    # Check KOCHARAM filter details
                    kocharam = period.get('kocharam_filter', {})
                    if kocharam:
                        print(f"   🎯 KOCHARAM Details:")
                        print(f"      Condition: {kocharam.get('condition', 'N/A')}")
                        print(f"      Transit Found: {kocharam.get('transit_found', False)}")
                        print(f"      Planet Current Angle: {kocharam.get('planet_current_angle', 'N/A')}")
                        print(f"      Planet Desired Angle: {kocharam.get('planet_desired_angle', 'N/A')}")
                        print(f"      Angle Distance: {kocharam.get('angle_distance', 'N/A')}")
                        print(f"      Time Taken: {kocharam.get('time_taken', 'N/A')}")

                        # Check enhanced transit details
                        enhanced_details = kocharam.get('enhanced_transit_details', [])
                        if enhanced_details:
                            print(f"      📅 Enhanced Transit Details ({len(enhanced_details)} transits):")
                            for j, transit in enumerate(enhanced_details[:2]):  # Show first 2 transits
                                print(f"         Transit {j+1}:")
                                print(f"            Start Date: {transit.get('predicted_start_date', 'N/A')}")
                                print(f"            End Date: {transit.get('predicted_end_date', 'N/A')}")
                                print(f"            Duration: {transit.get('duration_days', 'N/A')} days")
                                print(f"            Start Validation: {transit.get('start_sign_validation', 'N/A')}")
                                print(f"            End Validation: {transit.get('end_sign_validation', 'N/A')}")

                        # Check aspect calculation details
                        if 'aspect_calculation_details' in kocharam:
                            aspect_details = kocharam['aspect_calculation_details']
                            print(f"      🎯 Aspect Calculation Details:")
                            print(f"         Target House: {aspect_details.get('target_house', 'N/A')}")
                            print(f"         Aspect Numbers: {aspect_details.get('aspect_numbers', 'N/A')}")
                            print(f"         Source Houses: {aspect_details.get('source_houses', 'N/A')}")
                            print(f"         Fastest Aspect: {aspect_details.get('fastest_aspect', 'N/A')}")
            else:
                print("❌ No periods found matching the criteria")

            # Check for any error messages
            if 'error' in result:
                print(f"\n⚠️ Warning: {result['error']}")

        else:
            print("❌ Query failed")
            print(f"Error: {result.get('error', 'Unknown error')}")

            if 'details' in result:
                print(f"Details: {result['details']}")

    except Exception as e:
        print(f"❌ Error running test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_aspect_calculation()
