{"info": {"name": "Fortune Lens - Complete Rule Engine API Collection", "description": "Comprehensive collection of all 88+ rule engine patterns with detailed examples and documentation", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:5003", "type": "string"}, {"key": "user_profile_id", "value": "1", "type": "string"}, {"key": "member_profile_id", "value": "1", "type": "string"}], "item": [{"name": "1. <PERSON> Dasha Queries", "description": "Direct planet maha dasha queries - returns 1 period per planet", "item": [{"name": "VENUS Dasa_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"VENUS Dasa_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "JUPITER Dasa_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"JUPITER Dasa_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "MERCURY Dasa_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"MERCURY Dasa_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "MARS Dasa_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"MARS Dasa_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "SATURN Dasa_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"SATURN Dasa_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "SUN Dasa_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"SUN Dasa_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "MOON Dasa_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"MOON Dasa_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "RAHU Dasa_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"RAHU Dasa_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "KETU Dasa_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"KETU Dasa_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}]}, {"name": "2. Basic Bhukti Queries", "description": "Direct planet bhukti queries - returns 17 periods per planet", "item": [{"name": "VENUS Bhukti_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"VENUS Bhukti_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "JUPITER Bhukti_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"JUPITER Bhukti_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "MERCURY Bhukti_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"MERCURY Bhukti_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}]}, {"name": "3. House Ruling Planet Dasha", "description": "House ruling planet maha dasha queries - returns 1 period per house", "item": [{"name": "1st_House_Ruling_Planet Dasa_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"1st_House_Ruling_Planet Dasa_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "7th_House_Ruling_Planet Dasa_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"7th_House_Ruling_Planet Dasa_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "10th_House_Ruling_Planet Dasa_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"10th_House_Ruling_Planet Dasa_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}]}, {"name": "4. House Ruling Planet Bhukti", "description": "House ruling planet bhu<PERSON><PERSON> queries - returns 17 periods per house", "item": [{"name": "7th_House_Ruling_Planet Bhukti_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"7th_House_Ruling_Planet Bhukti_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "10th_House_Ruling_Planet Bhukti_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"10th_House_Ruling_Planet Bhukti_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "2nd_House_Ruling_Planet Bhukti_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"2nd_House_Ruling_Planet Bhukti_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}]}, {"name": "5. WITH_STARS_OF Patterns", "description": "Nakshatra-based queries - finds periods for planets in specified planet's nakshatras", "item": [{"name": "VENUS WITH_STARS_OF Bhukti_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"VENUS WITH_STARS_OF Bhukti_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "JUPITER WITH_STARS_OF Dasa_Dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"JUPITER WITH_STARS_OF Dasa_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}]}, {"name": "6. Time Constraints", "description": "PREDICTION_DURATION with years, months, and days support", "item": [{"name": "VENUS Bhukti_Dates - 2 Years", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"VENUS Bhukti_Dates AND PREDICTION_DURATION = 2 Years\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "JUPITER Bhukti_Dates - 6 Months", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"JUPITER Bhukti_Dates AND PREDICTION_DURATION = 6 Months\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "MERCURY Bhukti_Dates - 365 Days", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"MERCURY Bhukti_Dates AND PREDICTION_DURATION = 365 Days\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}]}, {"name": "7. KOCHA<PERSON><PERSON> Filters", "description": "Transit-based filtering for precise timing", "item": [{"name": "VENUS Bhukti_Dates with KOCHARAM", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"VENUS Bhukti_Dates AND KOCHARAM_FILTER(VENUS in 7th_House)\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "7th House with Complex KOCHARAM", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"7th_House_Ruling_Planet Bhukti_Dates AND KOCHARAM_FILTER(VENUS in 7th_House AND JUPITER in 1st_House)\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}]}, {"name": "8. Complex Combinations", "description": "Advanced queries combining multiple patterns", "item": [{"name": "Marriage Analysis - OR Logic", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"(7th_House_Ruling_Planet Bhukti_Dates OR VENUS Bhukti_Dates)\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "Career Analysis with Time Constraint", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"10th_House_Ruling_Planet Bhukti_Dates AND PREDICTION_DURATION = 5 Years\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "Complete Marriage Prediction", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"VENUS Bhukti_Dates AND PREDICTION_DURATION = 5 Years AND KOCHARAM_FILTER(JUPITER in 1st_House)\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}, {"name": "Alternative Format - House2", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"query\": \"House7 Bhukti_Dates\",\n  \"chart_type\": \"D1\"\n}"}, "url": {"raw": "{{base_url}}/api/rule-engine/", "host": ["{{base_url}}"], "path": ["api", "rule-engine", ""]}}}]}]}