#!/usr/bin/env python3
"""
Test degree range validation specifically
"""

def test_degree_range_validation():
    print("🔍 Testing Degree Range Validation Logic")
    print("=" * 50)
    
    # Test cases for Meenam (330-360°)
    test_cases = [
        # (longitude, expected_start_range, expected_end_range, description)
        (331.0, True, <PERSON>als<PERSON>, "Start of start range (1° in house)"),
        (335.0, True, <PERSON><PERSON><PERSON>, "Middle of start range (5° in house)"),
        (340.0, True, <PERSON>als<PERSON>, "End of start range (10° in house)"),
        (345.0, <PERSON>alse, <PERSON>alse, "Between ranges (15° in house)"),
        (350.0, <PERSON>alse, True, "Start of end range (20° in house)"),
        (355.0, <PERSON>als<PERSON>, <PERSON>, "Middle of end range (25° in house)"),
        (360.0, <PERSON>als<PERSON>, <PERSON>, "End of end range (30° in house)"),
        (327.27, <PERSON>als<PERSON>, <PERSON>als<PERSON>, "Before Meenam (in Kumbam)"),
        (344.51, <PERSON><PERSON><PERSON>, <PERSON>als<PERSON>, "In Meenam but between ranges"),
        (358.23, <PERSON><PERSON><PERSON>, <PERSON>, "In end range"),
        (332.77, <PERSON>, <PERSON><PERSON><PERSON>, "In start range"),
    ]
    
    print("Testing degree range validation for <PERSON><PERSON><PERSON> (330-360°):")
    print("Start range: 1-10° in house (331-340° absolute)")
    print("End range: 20-30° in house (350-360° absolute)")
    print()
    
    for longitude, expected_start, expected_end, description in test_cases:
        # Check if in Meenam
        in_meenam = 330 <= longitude <= 360
        
        if in_meenam:
            # Calculate degree within house
            degree_in_house = longitude - 330
            
            # Check start range (1-10° in house)
            start_range_valid = 1 <= degree_in_house <= 10
            
            # Check end range (20-30° in house)
            end_range_valid = 20 <= degree_in_house <= 30
            
            print(f"📍 {longitude:.2f}° → {degree_in_house:.1f}° in Meenam")
            print(f"   Start range (1-10°): {start_range_valid} (Expected: {expected_start})")
            print(f"   End range (20-30°): {end_range_valid} (Expected: {expected_end})")
            
            # Check if results match expectations
            start_match = start_range_valid == expected_start
            end_match = end_range_valid == expected_end
            
            if start_match and end_match:
                print(f"   ✅ CORRECT: {description}")
            else:
                print(f"   ❌ MISMATCH: {description}")
        else:
            print(f"📍 {longitude:.2f}° → NOT in Meenam")
            print(f"   Start range: False (Expected: {expected_start})")
            print(f"   End range: False (Expected: {expected_end})")
            
            if not expected_start and not expected_end:
                print(f"   ✅ CORRECT: {description}")
            else:
                print(f"   ❌ MISMATCH: {description}")
        
        print()
    
    # Test the specific requirement
    print("🎯 Testing Specific Requirement:")
    print("Both start_degree_range and end_degree_range must be TRUE")
    print("If either is FALSE, predict again until both become TRUE")
    print()
    
    # Example scenarios
    scenarios = [
        (335.0, 355.0, "Perfect case - both in correct ranges"),
        (327.0, 355.0, "Start needs adjustment - not in Meenam"),
        (335.0, 345.0, "End needs adjustment - between ranges"),
        (327.0, 345.0, "Both need adjustment"),
    ]
    
    for start_long, end_long, description in scenarios:
        print(f"📊 Scenario: {description}")
        print(f"   Start: {start_long:.1f}°, End: {end_long:.1f}°")
        
        # Check start
        start_in_meenam = 330 <= start_long <= 360
        start_degree_valid = False
        if start_in_meenam:
            start_degree_in_house = start_long - 330
            start_degree_valid = 1 <= start_degree_in_house <= 10
        
        # Check end
        end_in_meenam = 330 <= end_long <= 360
        end_degree_valid = False
        if end_in_meenam:
            end_degree_in_house = end_long - 330
            end_degree_valid = 20 <= end_degree_in_house <= 30
        
        print(f"   Start degree valid: {start_degree_valid}")
        print(f"   End degree valid: {end_degree_valid}")
        
        both_valid = start_degree_valid and end_degree_valid
        print(f"   Both valid: {both_valid}")
        
        if both_valid:
            print(f"   ✅ STOP: Both validations TRUE - no more prediction needed")
        else:
            print(f"   🔄 CONTINUE: Need to predict again until both become TRUE")
        
        print()

if __name__ == "__main__":
    test_degree_range_validation()
