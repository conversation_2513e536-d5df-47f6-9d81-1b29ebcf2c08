# Sample Query Processing Walkthrough

## Query Analysis
```json
{
  "user_profile_id": 100001,
  "member_profile_id": 1,
  "query": "(7th_House_Ruling_Planet Bhukti_Dates AND 7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates) AND Member_Age >= 23 AND <= 26 AND KOCHARAM_FILTER(JUPITER ASPECT 7th_House)",
  "chart_type": "D1"
}
```

## Step-by-Step Processing Flow

### 1. Entry Point: `process_rule_engine_request()`
- **Function Called**: `process_rule_engine_request(data)`
- **Action**: Validates input data and detects query type
- **Detection Result**: Contains "Bhukti_Dates" → Routes to dasha-based processing
- **Next Function**: `parse_and_evaluate_dasha_query()`

### 2. Query Parsing: `parse_and_evaluate_dasha_query()`
- **Function Called**: `parse_and_evaluate_dasha_query(chart_data, query, "D1", 100001, 1)`
- **Actions**:
  - Extract age constraints using regex: `r'Member_Age\s+>=\s+(\d+)\s+AND\s+<=\s+(\d+)'`
  - **Result**: `age_constraints = {"min_age": 23, "max_age": 26}`
  - Remove age constraints from query string
  - Extract KOCHARAM filter using `parse_kocharam_filter(query)`
  - **Result**: `kocharam_condition = "JUPITER ASPECT 7th_House"`
  - **Cleaned Query**: `"(7th_House_Ruling_Planet Bhukti_Dates AND 7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates)"`

### 3. Logical Operator Parsing
- **Function Called**: `split_by_operator(query, 'OR')`
- **Result**: No OR operators found, single OR part
- **Function Called**: `split_by_operator(or_part, 'AND')`
- **Result**: Two AND conditions:
  1. `"7th_House_Ruling_Planet Bhukti_Dates"`
  2. `"7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates"`

### 4. Individual Condition Processing

#### Condition 1: `7th_House_Ruling_Planet Bhukti_Dates`
- **Function Called**: `parse_dasha_condition("7th_House_Ruling_Planet Bhukti_Dates")`
- **Result**: `("HOUSE_RULING_PLANET_BHUKTI_DATES", {"house_number": 7, "dasha_type": "bhukti_dasha"})`
- **Function Called**: `evaluate_dasha_condition("HOUSE_RULING_PLANET_BHUKTI_DATES", parameters, chart_data, 100)`
- **Process**:
  - Get 7th house ruling planet from chart data using `get_house_sign_and_ruling_planet_from_chart(chart_data, 7)`
  - **Example Result**: 7th house is "Meenam" ruled by "JUPITER"
  - Get bhukti periods for JUPITER using `get_dasha_periods_for_planet(chart_data, "JUPITER", "bhukti_dasha")`
  - **Example Result**: List of periods where JUPITER appears as bhukti lord

#### Condition 2: `7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates`
- **Function Called**: `parse_dasha_condition("7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates")`
- **Result**: `("HOUSE_RULING_PLANET_WITH_STARS_OF_BHUKTI_DATES", {"house_number": 7, "dasha_type": "bhukti_dasha"})`
- **Function Called**: `evaluate_dasha_condition("HOUSE_RULING_PLANET_WITH_STARS_OF_BHUKTI_DATES", parameters, chart_data, 100)`
- **Process**:
  - Get 7th house ruling planet: "JUPITER"
  - Get JUPITER's nakshatra from chart data using `get_planet_nakshatra_from_chart(chart_data, "JUPITER")`
  - **Example Result**: JUPITER in "THIRUVADIRAI" nakshatra
  - Get nakshatra lord using `get_nakshatra_lord("THIRUVADIRAI")`
  - **Example Result**: THIRUVADIRAI is ruled by "RAHU"
  - Find all planets in RAHU's nakshatras using `get_planets_with_stars_of_planet(chart_data, "RAHU")`
  - **Example Result**: ["MARS", "VENUS"] are in RAHU's nakshatras
  - Get bhukti periods for these planets

### 5. AND Logic Evaluation
- **Logic**: Both conditions must be true
- **Result**: Combine dasha periods from both conditions
- **Deduplication**: Remove duplicate periods and sort by start date

### 6. Age Filtering Application
- **Function Called**: `filter_dasha_periods_by_age(dasha_periods, chart_data, 23, 26)`
- **Process**:
  - Get birth date from chart data using `get_member_birth_date_from_chart_data(chart_data)`
  - **Example**: Birth date = "1976-08-22 19:27:04"
  - For each dasha period, calculate age during that period
  - **Example**: Period "1999-01-15 to 2000-02-10" → Age 22-23 → Overlaps with 23-26 range
  - Filter periods where person's age overlaps with 23-26 range

### 7. KOCHARAM Filter Processing
- **Function Called**: `process_kocharam_filter(filtered_dasha_dates, "JUPITER ASPECT 7th_House", chart_data, 100001, 1, True, age_constraints)`

#### 7.1 KOCHARAM Condition Parsing
- **Function Called**: `parse_kocharam_condition("JUPITER ASPECT 7th_House")`
- **Result**: `{"type": "aspect", "planet": "JUPITER", "house": 7, "valid": True}`

#### 7.2 Enhanced KOCHARAM Algorithm Application
- **Function Called**: `apply_enhanced_kocharam_algorithm(dasha_periods, "JUPITER", 7, first_dasha_date, last_dasha_date, chart_data, birth_place_data, "aspect", age_start_date)`

**Step 2: Calculate Current Planet Position**
- **Reference Date**: Age 23 start date = "1999-08-22 19:27:04"
- **Function Called**: `get_planet_position_on_date("JUPITER", "1999-08-22 19:27:04", birth_place_data)`
- **Example Result**: JUPITER longitude = 45.5°

**Step 3: Determine Target House Angle**
- **Function Called**: `get_target_house_angle(chart_data, 7)`
- **Process**: Get 7th house starting angle from user's birth chart
- **Example Result**: 7th house (Meenam) starts at 330°

**Step 4: Calculate Aspect Angles**
- **JUPITER Aspects**: [5, 7, 9] (5th, 7th, 9th aspects)
- **Aspect Angle Calculation**: `((aspect_number - 1) × 30 + current_longitude) % 360`
- **Results**:
  - 5th Aspect: `((5-1) × 30 + 45.5) % 360 = 165.5°`
  - 7th Aspect: `((7-1) × 30 + 45.5) % 360 = 225.5°`
  - 9th Aspect: `((9-1) × 30 + 45.5) % 360 = 285.5°`

**Step 5: Calculate Distance to Target House**
- **Target House Range**: 330° - 360° (Meenam)
- **Distance Calculations**:
  - 5th Aspect (165.5°) to 330°: 164.5°
  - 7th Aspect (225.5°) to 330°: 104.5°
  - 9th Aspect (285.5°) to 330°: 44.5° ← **Fastest**

**Step 6: Calculate Transit Time**
- **JUPITER Rotation Period**: 4333 days
- **Formula**: `time_taken = 4333 × (44.5 / 360) = 535.8 days`
- **First Transit Date**: "1999-08-22" + 535.8 days = "2001-02-09"

**Step 7-9: Generate Transit Array and Find Overlaps**
- **Function Called**: `generate_complete_transit_array(first_transit_date, 4333, last_dasha_date)`
- **Process**: Generate all JUPITER aspect transits to 7th house during dasha periods
- **Function Called**: `find_overlapping_periods(dasha_periods, transit_dates, ...)`
- **Result**: Periods where dasha dates overlap with JUPITER aspect transit dates

### 8. Final Response Creation
- **Function Called**: `create_clean_dasha_response(...)`
- **Result**: Comprehensive response with:
  - Filtered dasha periods with KOCHARAM transit details
  - Age filtering analysis
  - KOCHARAM summary with transit predictions
  - Success indicators and confidence levels

## Key Algorithms Used

### 1. Age Calculation Algorithm
```python
def calculate_age_during_period(birth_date, period_start_date):
    age_at_start = relativedelta(period_start_date, birth_date).years
    return age_at_start
```

### 2. Aspect Angle Calculation
```python
def calculate_aspect_angle(aspect_number, current_longitude):
    return ((aspect_number - 1) * 30 + current_longitude) % 360
```

### 3. Transit Time Calculation
```python
def calculate_transit_time(rotation_period, angular_distance):
    return rotation_period * (angular_distance / 360.0)
```

### 4. Set Theory Operations for Logical Operators
- **OR Logic**: Union of all transit dates from any condition
- **AND Logic**: Intersection of transit dates from all conditions
- **NOT Logic**: Exclusion of transit dates from specified condition

## Performance Optimizations Applied

1. **Chart Caching**: Reuse generated charts for same date/location
2. **Degree-Based Calculations**: Mathematical approach instead of iterative search
3. **Lazy Loading**: Load data only when needed
4. **Batch Processing**: Group multiple calculations together
5. **Memory Management**: Automatic cleanup of temporary objects

This walkthrough demonstrates the sophisticated processing capabilities of the Fortune Lens rule engine, showing how complex astrological queries are parsed, evaluated, and optimized for accurate results.
