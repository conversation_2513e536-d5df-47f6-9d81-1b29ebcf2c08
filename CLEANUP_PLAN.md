# Main Rule Engine Cleanup Plan

## Issues Identified

### 1. **Duplicate Functions**
- `get_house_name_from_chart` (lines 1538 and 1909) - Same function defined twice
- `get_planets_in_house` (lines 8678 and 10185) - Duplicate implementations
- `get_member_birth_date_from_chart_data` (lines 2171 and 9585) - Duplicate functions

### 2. **Excessive Debug Print Statements**
- Lines with debug prints: 1354, 2746, 3037, 10917, and many more
- These should be replaced with proper logging or removed

### 3. **Commented Code Blocks**
- Over 1100+ commented lines that add no value
- Many are just explanatory comments that could be simplified

### 4. **Unused/Redundant Functions**
- Multiple similar calculation functions for the same purpose
- Functions that are never called or have been superseded

### 5. **Long Functions**
- `apply_enhanced_kocharam_algorithm` (200+ lines)
- `parse_and_evaluate_dasha_query` (600+ lines)
- `evaluate_rule` (500+ lines)

## Cleanup Strategy

### Phase 1: Remove Duplicates and Unused Code
1. Remove duplicate function definitions
2. Remove unused functions
3. Remove excessive debug prints
4. Remove commented code blocks

### Phase 2: Consolidate Similar Functions
1. Merge similar calculation functions
2. Create utility functions for common operations
3. Standardize function signatures

### Phase 3: Optimize Long Functions
1. Break down long functions into smaller ones
2. Extract common logic into helper functions
3. Improve readability and maintainability

### Phase 4: Clean Up Comments and Documentation
1. Remove unnecessary comments
2. Keep only essential documentation
3. Standardize docstring format

## Expected Results
- Reduce file size by 30-40%
- Improve code readability
- Remove redundancy
- Better maintainability
- Faster execution due to less code

## Files to Create
1. `rule_engine_utils.py` - Common utility functions
2. `kocharam_calculator.py` - KOCHARAM-specific calculations
3. `dasha_parser.py` - Dasha parsing logic

This will make the main rule engine more focused and maintainable.
