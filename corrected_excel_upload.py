#!/usr/bin/env python3
"""
Corrected Excel Upload Script - Uploads ALL 55 users including those with missing data
"""

import pandas as pd
import pymongo
from pymongo import MongoClient
from bson import ObjectId
import json
from datetime import datetime, time
import sys
import os

# Add the astro_insights_pro app to the path
sys.path.append('astro_insights_pro')

try:
    from app.services.chart_service import generate_chart
    CHART_GENERATION_AVAILABLE = True
    print("Chart generation service loaded successfully")
except ImportError as e:
    print(f"Warning: Chart generation not available: {e}")
    CHART_GENERATION_AVAILABLE = False

def get_location_info(place, state, country):
    """Simple location info function with Tamil Nadu locations"""
    locations = {
        'cuddalore': {'latitude': 11.7480, 'longitude': 79.7714},
        'pondicherry': {'latitude': 11.9416, 'longitude': 79.8083},
        'salem': {'latitude': 11.6643, 'longitude': 78.1460},
        'dharmapuri': {'latitude': 12.1211, 'longitude': 78.1583},
        'kumbakonam': {'latitude': 10.9601, 'longitude': 79.3788},
        'vellore': {'latitude': 12.9165, 'longitude': 79.1325},
        'chennai': {'latitude': 13.0827, 'longitude': 80.2707},
        'villupuram': {'latitude': 11.9401, 'longitude': 79.4861},
        'madurai': {'latitude': 9.9252, 'longitude': 78.1198},
        'coimbatore': {'latitude': 11.0168, 'longitude': 76.9558},
        'tiruchirappalli': {'latitude': 10.7905, 'longitude': 78.7047},
        'tirunelveli': {'latitude': 8.7139, 'longitude': 77.7567}
    }
    
    place_lower = place.lower() if place else ''
    if place_lower in locations:
        return locations[place_lower]
    else:
        # Default to Chennai coordinates
        return {'latitude': 13.0827, 'longitude': 80.2707}

class CorrectedExcelUploader:
    def __init__(self, mongodb_uri='mongodb://localhost:27017/', db_name='fortune_lens'):
        """Initialize uploader"""
        self.client = MongoClient(mongodb_uri)
        self.db = self.client[db_name]
        self.collections = {
            'user_profile': 'user_profile',
            'member_profile': 'member_profile',
            'astro_data': 'user_member_astro_profile_data'
        }
    
    def clean_database(self):
        """Clean existing data"""
        print("🧹 Cleaning database...")
        for collection_name in self.collections.values():
            result = self.db[collection_name].delete_many({})
            print(f"Deleted {result.deleted_count} documents from {collection_name}")
    
    def load_excel_data(self, excel_file='Astro FL User Data Master v1.xlsx'):
        """Load ALL Excel data including incomplete records"""
        try:
            df = pd.read_excel(excel_file, sheet_name='User Astro Data Master')
            print(f"Loaded {len(df)} records from Excel")
            
            # Don't filter out incomplete records - keep ALL data
            print(f"Keeping ALL {len(df)} records (including incomplete ones)")
            
            # Check for missing data but don't remove
            missing_birth = df[df['user_birthdate'].isna() | df['user_birthtime'].isna() | df['user_birthplace'].isna()]
            if len(missing_birth) > 0:
                print(f"⚠️  Found {len(missing_birth)} records with missing birth data:")
                for idx, row in missing_birth.iterrows():
                    print(f"  User {row['user_id']}, Member {row['member_id']}: {row['user_name']}")
            
            return df
        except Exception as e:
            print(f"Error loading Excel data: {e}")
            return None
    
    def create_user_profile(self, user_data):
        """Create user profile for ALL users"""
        user_id = int(user_data['user_id'])
        
        user_profile = {
            '_id': ObjectId(),
            'user_profile_id': user_id,
            'email': f"user_{user_id}@fortunelens.com",
            'password': '$2b$12$default.hash.for.excel.import',
            'name': str(user_data['user_name']) if pd.notna(user_data['user_name']) else f"User {user_id}",
            'mobile': str(user_data.get('user_mobile', '0000000000')),
            'unique_key': f"FL{user_id}{datetime.now().strftime('%H%M%S')}",
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        return user_profile
    
    def create_member_profile(self, user_data, user_profile_id):
        """Create member profile handling missing data"""
        user_profile_id = int(user_profile_id)
        member_id = int(user_data['member_id'])
        
        # Handle missing birth place
        birth_place = str(user_data['user_birthplace']) if pd.notna(user_data['user_birthplace']) else 'Unknown'
        
        # Get location info
        location_info = get_location_info(
            birth_place,
            user_data.get('user_state', ''),
            user_data.get('user_country', 'India')
        )
        
        member_profile = {
            '_id': ObjectId(),
            'member_profile_id': member_id,
            'user_profile_id': user_profile_id,
            'unique_key': f"M{user_profile_id}_{member_id}_{datetime.now().strftime('%H%M%S')}",
            'name': str(user_data['user_name']) if pd.notna(user_data['user_name']) else f"Member {member_id}",
            'relation': 'self' if member_id == 1 else f"member_{member_id}",
            'birth_date': user_data['user_birthdate'].strftime('%Y-%m-%d') if pd.notna(user_data['user_birthdate']) else None,
            'birth_time': str(user_data['user_birthtime']) if pd.notna(user_data['user_birthtime']) else None,
            'birth_place': birth_place,
            'state': str(user_data.get('user_state', '')),
            'country': str(user_data.get('user_country', 'India')),
            'latitude': float(location_info.get('latitude', 0.0)),
            'longitude': float(location_info.get('longitude', 0.0)),
            'gender': str(user_data.get('user_gender', '')).title() if pd.notna(user_data.get('user_gender')) else None,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        return member_profile
    
    def create_astro_profile(self, member_profile, user_data):
        """Create astrological profile with chart data (skip chart generation for incomplete data)"""
        # Check if we have complete birth data for chart generation
        has_complete_data = (
            member_profile['birth_date'] and 
            member_profile['birth_time'] and 
            member_profile['birth_place'] != 'Unknown'
        )
        
        chart_data = {}
        if has_complete_data and CHART_GENERATION_AVAILABLE:
            # Prepare birth data for chart generation
            birth_data = {
                'user_birthdate': member_profile['birth_date'],
                'user_birthtime': member_profile['birth_time'],
                'user_birthplace': member_profile['birth_place'],
                'user_state': member_profile['state'],
                'user_country': member_profile['country']
            }
            
            try:
                chart_data = generate_chart(birth_data)
                print(f"✓ Generated chart data for user {member_profile['user_profile_id']}, member {member_profile['member_profile_id']}")
            except Exception as e:
                print(f"✗ Error generating chart data for user {member_profile['user_profile_id']}, member {member_profile['member_profile_id']}: {e}")
                chart_data = {}
        else:
            if not has_complete_data:
                print(f"⚠ Skipping chart generation for user {member_profile['user_profile_id']}, member {member_profile['member_profile_id']} (incomplete birth data)")
            else:
                print(f"⚠ Chart generation not available for user {member_profile['user_profile_id']}, member {member_profile['member_profile_id']}")
        
        # Create astro profile (copy member profile structure)
        astro_profile = dict(member_profile)
        astro_profile['_id'] = ObjectId()  # New ID for astro collection
        
        # Add astro-specific data
        astro_profile['astro_data'] = {
            'user_profession': str(user_data.get('user_profession', '')),
            'user_data_label_1': str(user_data.get('User_Data_Label_1 (Profession)', '')),
            'user_data_label_2': str(user_data.get('User_Data_Label_2 (Specialization)', '')),
            'user_data_label_3': str(user_data.get('User_Data_Label_3 (Foreign / Domestic)', '')),
            'user_data_label_4': str(user_data.get('User_Data_Label_4 (Foreign Travel Date1)', '')),
            'user_data_label_5': str(user_data.get('User_Data_Label_5 (Foreign Travel Country1)', ''))
        }
        
        # Add chart data (empty if incomplete birth data)
        astro_profile['chart_data'] = chart_data
        
        return astro_profile
    
    def upload_all_data(self, df):
        """Upload ALL Excel data to MongoDB"""
        print(f"\nStarting upload of ALL {len(df)} records...")
        
        # Group by user_id
        user_groups = df.groupby('user_id')
        
        success_count = 0
        error_count = 0
        incomplete_count = 0
        
        for user_id, user_data_group in user_groups:
            try:
                print(f"\n=== Processing User ID: {user_id} ===")
                
                # Create user profile (from first record)
                first_record = user_data_group.iloc[0]
                user_profile = self.create_user_profile(first_record)
                
                # Insert user profile
                self.db[self.collections['user_profile']].insert_one(user_profile)
                print(f"✓ Created user profile: {user_profile['name']}")
                
                # Create member profiles and astro data for each member
                for idx, member_data in user_data_group.iterrows():
                    member_profile = self.create_member_profile(member_data, user_id)
                    
                    # Check if this is incomplete data
                    is_incomplete = (
                        not member_profile['birth_date'] or 
                        not member_profile['birth_time'] or 
                        member_profile['birth_place'] == 'Unknown'
                    )
                    
                    if is_incomplete:
                        incomplete_count += 1
                        print(f"⚠ Created incomplete member {member_data['member_id']}: {member_profile['name']}")
                    else:
                        print(f"✓ Created complete member {member_data['member_id']}: {member_profile['name']}")
                    
                    # Insert member profile
                    self.db[self.collections['member_profile']].insert_one(member_profile)
                    
                    # Create and insert astro profile
                    astro_profile = self.create_astro_profile(member_profile, member_data)
                    self.db[self.collections['astro_data']].insert_one(astro_profile)
                    print(f"✓ Created astro profile for member {member_data['member_id']}")
                
                success_count += 1
                
            except Exception as e:
                print(f"✗ Error processing user {user_id}: {e}")
                error_count += 1
        
        print(f"\n=== UPLOAD SUMMARY ===")
        print(f"✓ Successful users: {success_count}")
        print(f"✗ Error users: {error_count}")
        print(f"⚠ Incomplete records: {incomplete_count}")
        print(f"📊 Total processed: {success_count + error_count}")
        
        return success_count, error_count, incomplete_count
    
    def close(self):
        """Close MongoDB connection"""
        self.client.close()

def main():
    """Main function"""
    print("=" * 60)
    print("CORRECTED EXCEL UPLOAD - ALL 55 USERS")
    print("=" * 60)
    
    # Ask for confirmation
    print("\n⚠️  This will upload ALL 55 users including incomplete ones!")
    response = input("Proceed? (type 'YES' to confirm): ")
    
    if response != 'YES':
        print("Operation cancelled.")
        return
    
    uploader = CorrectedExcelUploader()
    
    try:
        # Clean database
        uploader.clean_database()
        
        # Load Excel data (ALL records)
        df = uploader.load_excel_data()
        if df is None:
            return
        
        print(f"✓ Loaded {len(df)} records")
        print(f"📈 Unique users: {df['user_id'].nunique()}")
        
        # Upload all data
        success_count, error_count, incomplete_count = uploader.upload_all_data(df)
        
        # Final summary
        print("\n" + "=" * 60)
        print("CORRECTED UPLOAD COMPLETED")
        print("=" * 60)
        
        if error_count == 0:
            print("🎉 SUCCESS: All users uploaded!")
            print(f"📊 Total users: {success_count}")
            print(f"⚠ Incomplete records: {incomplete_count}")
        else:
            print(f"⚠️  PARTIAL SUCCESS: {success_count} users uploaded, {error_count} errors")
        
    except Exception as e:
        print(f"✗ Fatal error: {e}")
    
    finally:
        uploader.close()

if __name__ == "__main__":
    main()
