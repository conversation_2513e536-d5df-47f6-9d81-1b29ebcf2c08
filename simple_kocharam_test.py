#!/usr/bin/env python3
"""
Simple test to check current KOCHARAM output without running the full server
"""

import sys
import os

# Add the project path
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

try:
    # Try to import the main rule engine
    from app.services.rule_engine.main_rule_engine import calculate_kocharam_transit_accurate
    print("✅ Successfully imported calculate_kocharam_transit_accurate")
    
    # Test basic function call
    test_result = calculate_kocharam_transit_accurate(
        planet_name="JUPITER",
        target_house_name="Meenam", 
        current_longitude=36.0,
        start_date="1976-08-22",
        end_date="1977-06-11",
        birth_place_data={
            "latitude": 13.0827,
            "longitude": 80.2707,
            "timezone": "Asia/Kolkata"
        }
    )
    
    print("✅ Function call successful!")
    print("\n🔍 CURRENT KOCHARAM OUTPUT ANALYSIS:")
    print("=" * 50)
    
    # Analyze the output structure
    if isinstance(test_result, dict):
        print(f"📊 Output Type: Dictionary with {len(test_result)} keys")
        
        # Check key fields
        key_fields = [
            'condition', 'planet', 'target_house_name', 
            'predicted_start_date', 'predicted_end_date',
            'predicted_start_timing', 'predicted_end_timing',
            'start_longitude', 'end_longitude',
            'validation', 'transit_found', 'calculation_method'
        ]
        
        print("\n📋 KEY FIELDS ANALYSIS:")
        for field in key_fields:
            value = test_result.get(field, 'MISSING')
            print(f"   {field}: {value}")
        
        # Check for the specific issues mentioned
        print("\n🔧 ISSUE ANALYSIS:")
        
        # Issue 1: Same dates
        start_date = test_result.get('predicted_start_date')
        end_date = test_result.get('predicted_end_date')
        print(f"   Same dates issue: {'❌ YES' if start_date == end_date else '✅ NO'}")
        
        # Issue 2: Static timing
        start_timing = test_result.get('predicted_start_timing')
        end_timing = test_result.get('predicted_end_timing')
        static_timing = '12:00:00' in str(start_timing) and '12:00:00' in str(end_timing)
        print(f"   Static timing issue: {'❌ YES' if static_timing else '✅ NO'}")
        
        # Issue 3: Validation consistency
        validation = test_result.get('validation', False)
        transit_found = test_result.get('transit_found', False)
        start_sign_val = test_result.get('start_sign_validation', False)
        inconsistent = (not validation and start_sign_val) or (validation and not transit_found)
        print(f"   Validation inconsistency: {'❌ YES' if inconsistent else '✅ NO'}")
        
        # Issue 4: Chart data
        start_chart = test_result.get('predicted_start_chart')
        end_chart = test_result.get('predicted_end_chart')
        missing_charts = start_chart is None and end_chart is None
        print(f"   Missing chart data: {'❌ YES' if missing_charts else '✅ NO'}")
        
        # Issue 5: Same longitudes
        start_long = test_result.get('start_longitude')
        end_long = test_result.get('end_longitude')
        same_longitudes = start_long == end_long
        print(f"   Same longitudes issue: {'❌ YES' if same_longitudes else '✅ NO'}")
        
        # Overall assessment
        issues = [
            start_date == end_date,
            static_timing,
            inconsistent,
            missing_charts,
            same_longitudes
        ]
        
        issue_count = sum(issues)
        print(f"\n🎯 OVERALL ASSESSMENT:")
        print(f"   Issues found: {issue_count}/5")
        print(f"   Status: {'❌ NEEDS FIXING' if issue_count > 0 else '✅ ALL GOOD'}")
        
        # Show calculation method
        calc_method = test_result.get('calculation_method', 'Unknown')
        print(f"   Calculation method: {calc_method}")
        
        # Check if using our FIXED method
        is_fixed = 'FIXED' in calc_method.upper()
        print(f"   Using FIXED method: {'✅ YES' if is_fixed else '❌ NO'}")
        
    else:
        print(f"❌ Unexpected output type: {type(test_result)}")
        print(f"Output: {test_result}")

except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("The main_rule_engine.py file has syntax errors")
    
except Exception as e:
    print(f"❌ Function Error: {e}")
    print("The function exists but has runtime errors")
    import traceback
    traceback.print_exc()

print("\n" + "="*50)
print("Test completed. Check the issues above to see what needs to be fixed.")
