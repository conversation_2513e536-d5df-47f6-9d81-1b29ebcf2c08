#!/usr/bin/env python3
"""
Test sequential transit periods generation
"""

import sys
import os
import json
from datetime import datetime

# Add the project path
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

try:
    # Import Flask app factory and create app context
    from app import create_app
    from app.services.rule_engine.main_rule_engine import process_rule_engine_request
    
    # Create Flask app with development config
    app = create_app('development')
    
    # Test data - Jupiter in 6th House
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Dasa_Dates)AND KOCHARAM_FILTER(JUPITER in 6th_House)",
        "chart_type": "D1"
    }
    
    print("🔍 Testing Sequential Transit Periods Generation")
    print("=" * 70)
    print("Expected features:")
    print("- Multiple sequential transit periods")
    print("- Each period's end date used for next period's start calculation")
    print("- Degree-based calculations for precise timing")
    print("- All dates verified with real D1 charts")
    print("- All dates within dasha period only")
    print()
    
    with app.app_context():
        result = process_rule_engine_request(test_data)
    
    if result.get('success', False) and 'result' in result:
        periods = result['result']
        
        print(f"📊 Total periods returned: {len(periods)}")
        
        # Check first period for sequential all_transit_periods
        if len(periods) > 0 and 'kocharam_filter' in periods[0]:
            kf = periods[0]['kocharam_filter']
            
            print(f"\n🔍 Sequential Transit Analysis:")
            print(f"   Planet: {kf.get('planet', 'N/A')}")
            print(f"   Target House: {kf.get('target_house_name', 'N/A')}")
            print(f"   Dasha Period: {periods[0].get('start_date', 'N/A')} to {periods[0].get('end_date', 'N/A')}")
            
            # Check all_transit_periods
            all_transits = kf.get('all_transit_periods', [])
            print(f"\n🎯 Sequential all_transit_periods: {len(all_transits)} periods")
            
            if all_transits:
                for i, transit in enumerate(all_transits):
                    print(f"\n   Transit Period {i+1}:")
                    print(f"     Transit Number: {transit.get('transit_number', 'N/A')}")
                    print(f"     Start Date: {transit.get('predicted_start_date', 'N/A')}")
                    print(f"     End Date: {transit.get('predicted_end_date', 'N/A')}")
                    print(f"     Start Timing: {transit.get('predicted_start_timing', 'N/A')}")
                    print(f"     End Timing: {transit.get('predicted_end_timing', 'N/A')}")
                    print(f"     Start Longitude: {transit.get('start_longitude', 'N/A')}°")
                    print(f"     End Longitude: {transit.get('end_longitude', 'N/A')}°")
                    print(f"     Start Sign: {transit.get('start_sign', 'N/A')}")
                    print(f"     End Sign: {transit.get('end_sign', 'N/A')}")
                    print(f"     Start Degree Range: {transit.get('start_degree_range', 'N/A')}")
                    print(f"     End Degree Range: {transit.get('end_degree_range', 'N/A')}")
                    print(f"     Duration: {transit.get('duration_days', 'N/A')} days")
                    print(f"     Within Dasha: {transit.get('within_dasha', 'N/A')}")
                    print(f"     Verified: {transit.get('verified', 'N/A')}")
                    print(f"     Transit Type: {transit.get('transit_type', 'N/A')}")
                
                # Verify sequential logic
                print(f"\n📈 Sequential Logic Verification:")
                for i in range(len(all_transits) - 1):
                    current_end = all_transits[i].get('predicted_end_date', '')
                    next_start = all_transits[i+1].get('predicted_start_date', '')
                    
                    if current_end and next_start:
                        try:
                            current_end_date = datetime.strptime(current_end, '%Y-%m-%d')
                            next_start_date = datetime.strptime(next_start, '%Y-%m-%d')
                            days_gap = (next_start_date - current_end_date).days
                            
                            print(f"   Period {i+1} → Period {i+2}:")
                            print(f"     End: {current_end} → Start: {next_start}")
                            print(f"     Gap: {days_gap} days")
                            
                            if days_gap > 0:
                                print(f"     ✅ Sequential: Next period starts after previous ends")
                            else:
                                print(f"     ⚠️ Issue: Dates overlap or are not sequential")
                        except:
                            print(f"     ⚠️ Could not parse dates for verification")
                
                # Summary
                within_dasha_count = sum(1 for t in all_transits if t.get('within_dasha', False))
                verified_count = sum(1 for t in all_transits if t.get('verified', False))
                
                print(f"\n📊 Summary:")
                print(f"   Total sequential transits: {len(all_transits)}")
                print(f"   Within dasha: {within_dasha_count}")
                print(f"   Verified: {verified_count}")
                
                if len(all_transits) > 1:
                    print(f"   ✅ SUCCESS: Multiple sequential transit periods generated!")
                    print(f"   Each period uses previous end date for next calculation")
                else:
                    print(f"   ⚠️ Only one transit period found")
                
                if within_dasha_count == len(all_transits):
                    print(f"   ✅ SUCCESS: All transits are within dasha period!")
                else:
                    print(f"   ⚠️ ISSUE: Some transits are outside dasha period")
                
                if verified_count == len(all_transits):
                    print(f"   ✅ SUCCESS: All transits are verified with charts!")
                else:
                    print(f"   ⚠️ ISSUE: Some transits are not verified")
            else:
                print(f"   ⚠️ No transit periods found")
        else:
            print(f"❌ No KOCHARAM filter data found")
        
    else:
        print("❌ Query failed!")
        if 'error' in result:
            print(f"Error: {result['error']}")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
