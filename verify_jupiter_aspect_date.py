#!/usr/bin/env python3
"""
Verify Jupiter aspect calculation for specific date 2002-09-10
"""

import requests
import json

def verify_jupiter_aspect_calculation():
    """Verify Jupiter aspect calculation for 2002-09-10"""
    
    # API endpoint
    base_url = "http://localhost:5003"
    endpoint = f"{base_url}/api/rule-engine/"
    
    # Test data - Check Jupiter aspect on specific date
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "KOCHARAM_FILTER(JUPITER ASPECT 7th_House)",
        "chart_type": "D1"
    }
    
    print("🔍 Verifying Jupiter Aspect Calculation for 2002-09-10")
    print("=" * 70)
    print(f"Query: {test_data['query']}")
    print()
    
    print("📊 Expected Calculation:")
    print("   Date: 2002-09-10")
    print("   Jupiter longitude: 104.52°")
    print("   9th Aspect formula: (9-1)*30 + 104.52 = 240 + 104.52 = 344.52°")
    print("   Target range: 330° to 360° (7th house = Meenam)")
    print("   Result: 344.52° is WITHIN 330°-360° range ✅")
    print("   Distance: 0.00° (already in range)")
    print("   Time: 0.00 days (immediate)")
    print()
    
    try:
        response = requests.post(endpoint, json=test_data, timeout=60)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("📊 API RESULT:")
            print("=" * 50)
            
            if result.get('success'):
                periods = result.get('periods', [])
                print(f"✅ Query executed successfully")
                print(f"📅 Total periods found: {len(periods)}")
                
                if periods:
                    for i, period in enumerate(periods):
                        print(f"\n🔍 Period {i+1}:")
                        print(f"   Start Date: {period.get('start_date', 'N/A')}")
                        print(f"   End Date: {period.get('end_date', 'N/A')}")
                        
                        # Check KOCHARAM filter details
                        kocharam = period.get('kocharam_filter', {})
                        if kocharam:
                            print(f"   🎯 KOCHARAM Details:")
                            print(f"      Transit Found: {kocharam.get('transit_found', False)}")
                            print(f"      Planet Current Angle: {kocharam.get('planet_current_angle', 'N/A')}°")
                            
                            # Check planet desired angles (aspect angles)
                            desired_angles = kocharam.get('planet_desired_angle', [])
                            if desired_angles:
                                print(f"      Aspect Angles: {desired_angles}")
                                
                                # Verify 9th aspect calculation
                                if len(desired_angles) >= 3:
                                    ninth_aspect_angle = desired_angles[2]  # 9th aspect is 3rd in list [5th, 7th, 9th]
                                    print(f"      9th Aspect Angle: {ninth_aspect_angle}°")
                                    
                                    # Check if it's in target range
                                    if 330 <= ninth_aspect_angle <= 360:
                                        print(f"      ✅ 9th Aspect ({ninth_aspect_angle}°) is WITHIN target range (330°-360°)")
                                        print(f"      ✅ Jupiter is ALREADY aspecting 7th house on 2002-09-10!")
                                    else:
                                        print(f"      ❌ 9th Aspect ({ninth_aspect_angle}°) is NOT in target range (330°-360°)")
                            
                            # Check angle distances
                            angle_distances = kocharam.get('angle_distance', [])
                            if angle_distances:
                                print(f"      Angle Distances: {angle_distances}")
                                if len(angle_distances) >= 3:
                                    ninth_distance = angle_distances[2]
                                    print(f"      9th Aspect Distance: {ninth_distance}°")
                                    if ninth_distance == 0.0:
                                        print(f"      ✅ Distance is 0.00° - Jupiter is already aspecting!")
                            
                            # Check time taken
                            time_taken = kocharam.get('time_taken', [])
                            if time_taken:
                                print(f"      Time Taken: {time_taken}")
                                if len(time_taken) >= 3:
                                    ninth_time = time_taken[2]
                                    print(f"      9th Aspect Time: {ninth_time} days")
                                    if ninth_time == 0.0:
                                        print(f"      ✅ Time is 0.00 days - immediate aspect!")
                            
                            # Check enhanced transit details
                            enhanced_details = kocharam.get('enhanced_transit_details', [])
                            if enhanced_details:
                                print(f"      📅 Enhanced Transit Details:")
                                for j, transit in enumerate(enhanced_details[:2]):
                                    print(f"         Transit {j+1}:")
                                    print(f"            Date: {transit.get('predicted_start_date', 'N/A')}")
                                    print(f"            Validation: {transit.get('start_sign_validation', 'N/A')}")
                else:
                    print("❌ No periods found - this might indicate a filtering issue")
                    
                # Check for KOCHARAM result summary
                kocharam_result = result.get('kocharam_result', {})
                if kocharam_result:
                    print(f"\n🎯 KOCHARAM Summary:")
                    print(f"   Periods passed: {kocharam_result.get('periods_passed', 'N/A')}")
                    print(f"   Success rate: {kocharam_result.get('success_rate', 'N/A')}%")
                    
            else:
                print("❌ Query failed")
                print(f"Error: {result.get('error', 'Unknown error')}")
                
        else:
            print("❌ Error Response:")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Error running verification: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    verify_jupiter_aspect_calculation()
