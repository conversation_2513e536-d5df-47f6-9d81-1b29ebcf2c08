#!/usr/bin/env python3
"""
Test the corrected degree range validation (0-10° and 20-30°)
"""

import sys
import os
import json
from datetime import datetime

# Add the project path
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

try:
    # Import Flask app factory and create app context
    from app import create_app
    from app.services.rule_engine.main_rule_engine import process_rule_engine_request
    
    # Create Flask app with development config
    app = create_app('development')
    
    # Test data - limit to first few periods
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Bhukti_Dates)AND KOCHARAM_FILTER(JUPITER in 7th_House)",
        "chart_type": "D1"
    }
    
    print("🔍 Testing Corrected Degree Range Validation (0-10° and 20-30°)")
    print("=" * 70)
    
    with app.app_context():
        result = process_rule_engine_request(test_data)
    
    if result.get('success', False) and 'result' in result:
        periods = result['result']
        
        print(f"📊 Found {len(periods)} periods")
        print()
        
        # Analyze degree range validation results
        perfect_periods = []
        partial_periods = []
        failed_periods = []
        
        for i, period in enumerate(periods[:5]):  # First 5 periods only
            if 'kocharam_filter' in period:
                kf = period['kocharam_filter']
                
                start_sign_valid = kf.get('start_sign_validation', False)
                end_sign_valid = kf.get('end_sign_validation', False)
                start_degree_valid = kf.get('start_degree_range', False)
                end_degree_valid = kf.get('end_degree_range', False)
                
                start_long = kf.get('predicted_start_longitude', 'N/A')
                end_long = kf.get('predicted_end_longitude', 'N/A')
                
                print(f"🔍 Period {i+1}: {period.get('start_date', 'N/A')} to {period.get('end_date', 'N/A')}")
                print(f"   Predicted Start: {kf.get('predicted_start_date', 'N/A')}")
                print(f"   Predicted End: {kf.get('predicted_end_date', 'N/A')}")
                print(f"   Start Longitude: {start_long}°")
                print(f"   End Longitude: {end_long}°")
                
                # Check degree ranges manually
                if isinstance(start_long, (int, float)):
                    if 330 <= start_long <= 360:  # In Meenam
                        degree_in_house = start_long - 330
                        expected_start_degree = 0 <= degree_in_house <= 10
                        print(f"   Start: {degree_in_house:.1f}° in Meenam → Expected 0-10° range: {expected_start_degree}")
                    else:
                        expected_start_degree = False
                        print(f"   Start: Not in Meenam → Expected 0-10° range: {expected_start_degree}")
                else:
                    expected_start_degree = False
                
                if isinstance(end_long, (int, float)):
                    if 330 <= end_long <= 360:  # In Meenam
                        degree_in_house = end_long - 330
                        expected_end_degree = 20 <= degree_in_house <= 30
                        print(f"   End: {degree_in_house:.1f}° in Meenam → Expected 20-30° range: {expected_end_degree}")
                    else:
                        expected_end_degree = False
                        print(f"   End: Not in Meenam → Expected 20-30° range: {expected_end_degree}")
                else:
                    expected_end_degree = False
                
                print(f"   ✅ Validation Results:")
                print(f"     Start Sign Valid: {start_sign_valid}")
                print(f"     End Sign Valid: {end_sign_valid}")
                print(f"     Start Degree Valid: {start_degree_valid} (Expected: {expected_start_degree})")
                print(f"     End Degree Valid: {end_degree_valid} (Expected: {expected_end_degree})")
                
                # Check if all validations are true
                all_valid = start_sign_valid and end_sign_valid and start_degree_valid and end_degree_valid
                print(f"     All Valid: {all_valid}")
                
                if all_valid:
                    perfect_periods.append(i+1)
                    print(f"   🌟 PERFECT: All validations TRUE - no more prediction needed!")
                else:
                    needs_refinement = []
                    if not start_sign_valid:
                        needs_refinement.append("start sign")
                    if not end_sign_valid:
                        needs_refinement.append("end sign")
                    if not start_degree_valid:
                        needs_refinement.append("start degree range")
                    if not end_degree_valid:
                        needs_refinement.append("end degree range")
                    
                    print(f"   🔄 NEEDS REFINEMENT: {', '.join(needs_refinement)}")
                
                print()
        
        print(f"📈 Summary:")
        print(f"   Perfect periods (all validations TRUE): {len(perfect_periods)}")
        if perfect_periods:
            print(f"   Perfect period numbers: {perfect_periods}")
        print(f"   The iterative refinement should continue until all validations are TRUE")
        
    else:
        print("❌ Query failed!")
        print(json.dumps(result, indent=2, default=str))
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
