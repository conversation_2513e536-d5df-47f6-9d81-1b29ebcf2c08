#!/usr/bin/env python3
"""
Test the improved sign validation logic
"""

import sys
import os
import json
from datetime import datetime

# Add the project path
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

try:
    # Import Flask app factory and create app context
    from app import create_app
    from app.services.rule_engine.main_rule_engine import process_rule_engine_request
    
    print("✅ Successfully imported rule engine")
    
    # Create Flask app with development config
    app = create_app('development')
    
    print("✅ Flask app created successfully")
    
    # Test data - your exact query
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Bhukti_Dates)AND KOCHARAM_FILTER(JUPITER in 7th_House)",
        "chart_type": "D1"
    }
    
    print("🔍 Testing Sign Validation Improvements")
    print("=" * 50)
    print(f"Query: {test_data['query']}")
    print()
    
    # Process the request within Flask app context
    print("⏳ Processing query...")
    start_time = datetime.now()
    
    with app.app_context():
        result = process_rule_engine_request(test_data)
    
    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()
    
    print(f"⏱️ Processing completed in {processing_time:.2f} seconds")
    print()
    
    # Display results focusing on sign validation
    if result.get('success', False):
        print("✅ Query executed successfully!")
        print("=" * 50)
        
        # Extract key information
        if 'result' in result and isinstance(result['result'], list):
            periods = result['result']
            print(f"\n📊 Summary: Found {len(periods)} dasha periods")
            
            # Show KOCHARAM results with focus on sign validation
            kocharam_periods = [p for p in periods if 'kocharam_filter' in p]
            print(f"🪐 KOCHARAM periods: {len(kocharam_periods)}")
            
            # Show periods with improved sign validation
            for i, period in enumerate(kocharam_periods[:5]):  # First 5 periods
                print(f"\n🔍 Period {i+1}: {period.get('planet_name', 'N/A')}")
                print(f"   Dasha: {period.get('start_date', 'N/A')} to {period.get('end_date', 'N/A')}")
                
                if 'kocharam_filter' in period:
                    kf = period['kocharam_filter']
                    print(f"   🎯 KOCHARAM Results:")
                    print(f"     Start Date: {kf.get('predicted_start_date', 'N/A')}")
                    print(f"     End Date: {kf.get('predicted_end_date', 'N/A')}")
                    print(f"     Start Longitude: {kf.get('predicted_start_longitude', 'N/A')}°")
                    print(f"     End Longitude: {kf.get('predicted_end_longitude', 'N/A')}°")
                    print(f"     Start Sign: {kf.get('predicted_start_sign', 'N/A')}")
                    print(f"     End Sign: {kf.get('predicted_end_sign', 'N/A')}")
                    
                    # Focus on validation results
                    start_sign_valid = kf.get('start_sign_validation', False)
                    end_sign_valid = kf.get('end_sign_validation', False)
                    overall_valid = kf.get('validation', False)
                    
                    print(f"     ✅ Start Sign Valid: {start_sign_valid}")
                    print(f"     ✅ End Sign Valid: {end_sign_valid}")
                    print(f"     🎯 Overall Valid: {overall_valid}")
                    
                    # Check if both validations are true
                    if start_sign_valid and end_sign_valid:
                        print(f"     🌟 PERFECT: Both start and end signs validated!")
                    elif start_sign_valid or end_sign_valid:
                        print(f"     ⚠️ PARTIAL: Only one sign validated")
                    else:
                        print(f"     ❌ ISSUE: Neither sign validated")
    else:
        print("❌ Query failed!")
        print("=" * 50)
        print(json.dumps(result, indent=2, default=str))
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
