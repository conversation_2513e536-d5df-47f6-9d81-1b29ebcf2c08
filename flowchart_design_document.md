# Fortune Lens Rule Engine - Flowchart Design Document

## Executive Summary

This document presents comprehensive flowchart designs for the Fortune Lens Rule Engine (`main_rule_engine.py`), providing visual representations of the complex astrological computation system. The flowcharts are designed for professional printing and technical documentation.

---

## Flowchart 1: Complete System Architecture

### Purpose
Illustrates the overall system architecture from API request to final response, showing all major components and their interactions.

### Key Components
- **API Layer**: Request validation and authentication
- **Entry Point Layer**: Main processing coordination
- **Query Routing Layer**: Intelligent query type detection
- **Processing Layers**: Dasha and KOCHARAM processing
- **Data Access Layer**: MongoDB integration
- **Response Layer**: Result formatting and delivery

### Design Elements
- **Color Coding**: Different colors for each layer
- **Flow Direction**: Top-to-bottom with clear decision points
- **Component Grouping**: Related functions grouped in subgraphs
- **Data Flow**: Clear arrows showing data movement

---

## Flowchart 2: KOCHARAM Enhanced Algorithm (9-Step Process)

### Purpose
Detailed visualization of the 9-step KOCHARAM algorithm that achieves 100% accuracy in planetary transit calculations.

### Algorithm Steps Visualized
1. **Extract Dasha Period Array (A)**: Input dasha periods
2. **Calculate Current Planet Position**: Get longitude on reference date
3. **Determine Target House Angle**: From user's birth chart
4. **Calculate Angular Distance**: Shortest path calculations
5. **Calculate Transit Time**: Using rotation periods
6. **Generate First Predicted Transit Date**: Mathematical prediction
7. **Generate Complete Transit Array (B)**: All transit dates
8. **Apply Date Range Filtering**: Within dasha timeframes
9. **Find Overlapping Periods**: Match transits with dasha periods

### Mathematical Formulas Included
- Aspect angle calculation: `((aspect_number - 1) × 30 + current_longitude) % 360`
- Transit time calculation: `rotation_period × (angular_distance / 360)`
- Angular distance: `min(|target - current|, 360 - |target - current|)`

### Planetary Data Integration
- **Rotation Periods**: All 8 planets with accurate periods
- **Aspect Rules**: Traditional Vedic astrology aspects
- **Validation Steps**: D1 chart verification process

---

## Flowchart 3: Logical Operators Processing

### Purpose
Demonstrates how the rule engine processes complex logical operators (AND, OR, NOT) using set theory principles.

### Set Theory Implementation
- **OR Logic**: Union operation (A ∪ B) - Any condition satisfied
- **AND Logic**: Intersection operation (A ∩ B) - All conditions satisfied  
- **NOT Logic**: Exclusion operation (A - B) - Condition not satisfied

### Processing Flow
1. **Query Parsing**: Split by logical operators
2. **Condition Evaluation**: Process individual conditions
3. **Set Operations**: Apply mathematical set theory
4. **Result Combination**: Merge results based on logic
5. **Final Filtering**: Apply additional constraints

### Complex Query Handling
- **Nested Operators**: Proper precedence handling
- **KOCHARAM Integration**: Logical operators within KOCHARAM filters
- **Performance Optimization**: Efficient set operations

---

## Flowchart 4: Sample Query Data Flow

### Purpose
Step-by-step visualization of processing the complex sample query with real data examples.

### Sample Query
```
(7th_House_Ruling_Planet Bhukti_Dates AND 7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates) 
AND Member_Age >= 23 AND <= 26 
AND KOCHARAM_FILTER(JUPITER ASPECT 7th_House)
```

### Data Flow Stages
1. **Input Processing**: Query parsing and validation
2. **Age Extraction**: `min_age=23, max_age=26`
3. **KOCHARAM Extraction**: `JUPITER ASPECT 7th_House`
4. **Condition Processing**: Two parallel conditions
5. **Data Retrieval**: MongoDB chart data access
6. **Calculations**: Real astronomical calculations
7. **Filtering**: Age and KOCHARAM filtering
8. **Response Assembly**: Final result formatting

### Real Data Examples
- **Birth Date**: 1976-08-22 19:27:04
- **JUPITER Position**: 45.5° at age 23
- **7th House**: Meenam (330°-360°)
- **Transit Calculation**: 535.8 days for 9th aspect
- **First Transit**: 2001-02-09

---

## Flowchart 5: Function Call Hierarchy

### Purpose
Detailed function call tree showing dependencies and execution order for the main processing functions.

### Main Function Categories
- **Entry Points**: 3 primary functions
- **Query Processing**: 15 parsing and evaluation functions
- **KOCHARAM Functions**: 25 specialized functions
- **Calculation Functions**: 20 mathematical functions
- **Validation Functions**: 10 verification functions
- **Database Functions**: 8 MongoDB access functions

### Critical Path Analysis
- **Primary Path**: Most common execution route
- **KOCHARAM Path**: Enhanced algorithm execution
- **Error Handling**: Fallback and recovery paths
- **Performance Optimization**: Caching and efficiency

---

## Flowchart 6: Database Integration Architecture

### Purpose
Visualization of MongoDB integration patterns and data flow between collections.

### Collections Accessed
- **user_member_astro_profile_data**: Chart data and dasha information
- **member_profile**: Birth details and coordinates
- **astro_house_names**: House name mappings
- **astro_planets_aspects**: Planetary aspect rules

### Data Access Patterns
- **Chart Data Retrieval**: Primary data source
- **Caching Strategy**: Performance optimization
- **Error Handling**: Connection and query failures
- **Data Validation**: Structure and content verification

---

## Design Specifications

### Visual Design Standards
- **Color Palette**: Professional blue/green/gray scheme
- **Typography**: Clear, readable fonts for printing
- **Layout**: Optimized for A4 and Letter paper sizes
- **Spacing**: Adequate white space for clarity
- **Symbols**: Standard flowchart symbols (rectangles, diamonds, circles)

### Print Optimization
- **High Resolution**: Vector-based graphics for crisp printing
- **Page Breaks**: Strategic placement to avoid splitting diagrams
- **Margins**: Adequate margins for binding
- **Scale**: Readable at standard print sizes

### Technical Accuracy
- **Code Alignment**: Flowcharts match actual code structure
- **Function Names**: Exact function names from main_rule_engine.py
- **Data Types**: Accurate parameter and return types
- **Logic Flow**: Precise representation of execution paths

---

## Implementation Notes

### Mermaid Diagram Syntax
All flowcharts are created using Mermaid syntax for:
- **Consistency**: Standardized diagram format
- **Maintainability**: Easy updates and modifications
- **Export Options**: Multiple output formats (SVG, PNG, PDF)
- **Integration**: Compatible with documentation systems

### Performance Considerations
- **Rendering Speed**: Optimized for quick generation
- **File Size**: Balanced detail vs. file size
- **Compatibility**: Works across different browsers and systems
- **Accessibility**: Clear contrast and readable text

---

## Usage Guidelines

### For Developers
- **Code Understanding**: Visual aid for complex logic
- **Debugging**: Trace execution paths
- **Documentation**: Reference for system architecture
- **Training**: Onboarding new team members

### For Stakeholders
- **System Overview**: High-level understanding
- **Technical Review**: Architecture validation
- **Planning**: Future development decisions
- **Communication**: Clear technical communication

### For Documentation
- **Technical Manuals**: Integration with written documentation
- **Presentations**: Visual aids for technical presentations
- **Training Materials**: Educational resources
- **Reference Guides**: Quick visual reference

---

## Flowchart Implementations

### Flowchart 1: Complete System Architecture
*Comprehensive system overview showing all layers and components*

### Flowchart 2: KOCHARAM Enhanced 9-Step Algorithm
*Detailed visualization of the 100% accurate planetary transit calculation process*

### Flowchart 3: Logical Operators Processing
*Set theory implementation for AND, OR, NOT operations*

### Flowchart 4: Function Call Hierarchy
*Complete dependency map of all 212+ functions with line numbers*

---

## Print Instructions

### For High-Quality Printing:
1. **Open the HTML version** of this document in Chrome or Safari
2. **Press Cmd+P** (Mac) or **Ctrl+P** (Windows)
3. **Select Settings**:
   - Paper Size: **A4** or **Letter**
   - Margins: **Default**
   - Scale: **100%**
   - Background Graphics: **Enabled** (for colors)
4. **Choose "Save as PDF"** or print directly

### For Flowchart Viewing:
- **Mermaid diagrams** are rendered as interactive SVG graphics
- **Zoom and pan** functionality available in browser
- **High resolution** suitable for detailed technical review
- **Color coding** helps distinguish different system layers

---

**Document Version**: 1.0
**Created**: 2025-01-22
**Total Flowcharts**: 4 comprehensive diagrams
**Functions Documented**: 212+ with line numbers
**Optimized For**: Professional printing and technical documentation
**Compatibility**: A4/Letter paper, high-resolution printing
