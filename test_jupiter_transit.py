#!/usr/bin/env python3
"""
Test Jupiter transit calculation specifically
"""

import sys
import os
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

from app import create_app
from app.services.rule_engine.main_rule_engine import generate_chart_for_date, get_planet_position_from_chart

# Create Flask app
app = create_app('development')

birth_place_data = {
    'user_birthdate': '1978-05-26', 
    'user_birthtime': '15:30:00', 
    'user_birthplace': 'Cuddalore', 
    'user_state': 'Tamil Nadu', 
    'user_country': 'India'
}

def test_jupiter_positions():
    print("🔍 Testing Jupiter Transit Calculation")
    print("=" * 50)
    
    # Test dates around the predicted transit
    test_dates = [
        "1983-07-05",  # Dasha start
        "1986-05-07",  # Predicted start
        "1986-12-21",  # Predicted end
        "1987-03-22",  # Previous working date
        "1987-11-06",  # Previous working date
    ]
    
    with app.app_context():
        for date in test_dates:
            print(f"\n📅 Date: {date}")
            chart = generate_chart_for_date(date, birth_place_data)
            if chart:
                jupiter_pos = get_planet_position_from_chart(chart, "JUPITER")
                if jupiter_pos:
                    longitude = jupiter_pos["longitude"]
                    sign = jupiter_pos["sign"]
                    
                    # Check if in Meenam (330-360°)
                    in_meenam = 330 <= longitude <= 360
                    
                    print(f"   Jupiter: {longitude:.2f}° in {sign}")
                    print(f"   In Meenam (330-360°): {in_meenam}")
                    
                    if in_meenam:
                        degree_in_house = longitude - 330
                        print(f"   Degree in Meenam: {degree_in_house:.2f}°")
                        print(f"   In range 1-10°: {1 <= degree_in_house <= 10}")
                        print(f"   In range 20-30°: {20 <= degree_in_house <= 30}")

if __name__ == "__main__":
    test_jupiter_positions()
