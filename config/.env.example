# Astro Insights Pro - Environment Configuration

# Application Settings
APP_NAME=Astro Insights Pro
APP_VERSION=1.0.0
DEBUG=True
SECRET_KEY=your-secret-key-here
PORT=5003
HOST=0.0.0.0

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/astro_insights_pro
MONGODB_DATABASE=astro_insights_pro

# Collections
USER_PROFILE_COLLECTION=user_profile
MEMBER_PROFILE_COLLECTION=member_profile
ASTRO_DATA_COLLECTION=user_member_astro_profile_data

# API Configuration
API_VERSION=v1
API_PREFIX=/api
CORS_ORIGINS=*
MAX_CONTENT_LENGTH=16777216

# Security
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ACCESS_TOKEN_EXPIRES=3600
BCRYPT_LOG_ROUNDS=12

# Rate Limiting
RATE_LIMIT_ENABLED=True
RATE_LIMIT_DEFAULT=1000 per hour
RATE_LIMIT_STORAGE_URL=redis://localhost:6379

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=logs/astro_insights_pro.log

# Cache Configuration
CACHE_TYPE=redis
CACHE_REDIS_URL=redis://localhost:6379/0
CACHE_DEFAULT_TIMEOUT=300

# External Services
TIMEZONE_API_KEY=your-timezone-api-key
GEOCODING_API_KEY=your-geocoding-api-key

# Performance
ENABLE_PROFILING=False
ENABLE_METRICS=True
METRICS_PORT=9090

# Development
FLASK_ENV=development
TESTING=False
