<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flowchart Design Document</title>
    
    <style>
        @media print {
            body { margin: 0.5in; }
            h1 { page-break-before: always; }
            table { page-break-inside: avoid; }
            pre { page-break-inside: avoid; }
        }
        
        body {
            font-family: 'Georgia', 'Times New Roman', serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 5px;
            margin-top: 25px;
        }
        
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            font-size: 12px;
        }
        
        th, td {
            border: 1px solid #bdc3c7;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #ecf0f1;
            font-weight: bold;
        }
        
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
        }
        
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 11px;
            border: 1px solid #e9ecef;
        }
        
        pre code {
            background: none;
            padding: 0;
        }
        
        ul, ol {
            margin: 10px 0;
            padding-left: 30px;
        }
        
        li {
            margin: 5px 0;
        }
        
        .print-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 20px;
        }
        
        .print-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .toc {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .toc h2 {
            margin-top: 0;
            border: none;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 8px 0;
        }
        
        .toc a {
            text-decoration: none;
            color: #3498db;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
    
</head>
<body>
    <div class="print-header">
        <h1>Fortune Lens Rule Engine</h1>
        <h2>Flowchart Design Document</h2>
        <p>Generated on /Users/<USER>/PycharmProjects/fortune_lens</p>
    </div>
    
    <h1>Fortune Lens Rule Engine - Flowchart Design Document</h1>

<h2>Executive Summary</h2>

<p>This document presents comprehensive flowchart designs for the Fortune Lens Rule Engine (<code>main_rule_engine.py</code>), providing visual representations of the complex astrological computation system. The flowcharts are designed for professional printing and technical documentation.</p>

<p>---</p>

<h2>Flowchart 1: Complete System Architecture</h2>

<h3>Purpose</h3>
Illustrates the overall system architecture from API request to final response, showing all major components and their interactions.

<h3>Key Components</h3>
<ul>
<li><strong>API Layer</strong>: Request validation and authentication</li>
<li><strong>Entry Point Layer</strong>: Main processing coordination</li>
<li><strong>Query Routing Layer</strong>: Intelligent query type detection</li>
<li><strong>Processing Layers</strong>: Dasha and KOCHARAM processing</li>
<li><strong>Data Access Layer</strong>: MongoDB integration</li>
<li><strong>Response Layer</strong>: Result formatting and delivery</li>
</ol>

<h3>Design Elements</h3>
<ul>
<li><strong>Color Coding</strong>: Different colors for each layer</li>
<li><strong>Flow Direction</strong>: Top-to-bottom with clear decision points</li>
<li><strong>Component Grouping</strong>: Related functions grouped in subgraphs</li>
<li><strong>Data Flow</strong>: Clear arrows showing data movement</li>
</ol>

<p>---</p>

<h2>Flowchart 2: KOCHARAM Enhanced Algorithm (9-Step Process)</h2>

<h3>Purpose</h3>
Detailed visualization of the 9-step KOCHARAM algorithm that achieves 100% accuracy in planetary transit calculations.

<h3>Algorithm Steps Visualized</h3>
<ol>
<li><strong>Extract Dasha Period Array (A)</strong>: Input dasha periods</li>
<li><strong>Calculate Current Planet Position</strong>: Get longitude on reference date</li>
<li><strong>Determine Target House Angle</strong>: From user's birth chart</li>
<li><strong>Calculate Angular Distance</strong>: Shortest path calculations</li>
<li><strong>Calculate Transit Time</strong>: Using rotation periods</li>
<li><strong>Generate First Predicted Transit Date</strong>: Mathematical prediction</li>
<li><strong>Generate Complete Transit Array (B)</strong>: All transit dates</li>
<li><strong>Apply Date Range Filtering</strong>: Within dasha timeframes</li>
<li><strong>Find Overlapping Periods</strong>: Match transits with dasha periods</li>
</ol>

<h3>Mathematical Formulas Included</h3>
<ul>
<li>Aspect angle calculation: <code>((aspect_number - 1) × 30 + current_longitude) % 360</code></li>
<li>Transit time calculation: <code>rotation_period × (angular_distance / 360)</code></li>
<li>Angular distance: <code>min(|target - current|, 360 - |target - current|)</code></li>
</ol>

<h3>Planetary Data Integration</h3>
<ul>
<li><strong>Rotation Periods</strong>: All 8 planets with accurate periods</li>
<li><strong>Aspect Rules</strong>: Traditional Vedic astrology aspects</li>
<li><strong>Validation Steps</strong>: D1 chart verification process</li>
</ol>

<p>---</p>

<h2>Flowchart 3: Logical Operators Processing</h2>

<h3>Purpose</h3>
Demonstrates how the rule engine processes complex logical operators (AND, OR, NOT) using set theory principles.

<h3>Set Theory Implementation</h3>
<ul>
<li><strong>OR Logic</strong>: Union operation (A ∪ B) - Any condition satisfied</li>
<li><strong>AND Logic</strong>: Intersection operation (A ∩ B) - All conditions satisfied  </li>
<li><strong>NOT Logic</strong>: Exclusion operation (A - B) - Condition not satisfied</li>
</ol>

<h3>Processing Flow</h3>
<ol>
<li><strong>Query Parsing</strong>: Split by logical operators</li>
<li><strong>Condition Evaluation</strong>: Process individual conditions</li>
<li><strong>Set Operations</strong>: Apply mathematical set theory</li>
<li><strong>Result Combination</strong>: Merge results based on logic</li>
<li><strong>Final Filtering</strong>: Apply additional constraints</li>
</ol>

<h3>Complex Query Handling</h3>
<ul>
<li><strong>Nested Operators</strong>: Proper precedence handling</li>
<li><strong>KOCHARAM Integration</strong>: Logical operators within KOCHARAM filters</li>
<li><strong>Performance Optimization</strong>: Efficient set operations</li>
</ol>

<p>---</p>

<h2>Flowchart 4: Sample Query Data Flow</h2>

<h3>Purpose</h3>
Step-by-step visualization of processing the complex sample query with real data examples.

<h3>Sample Query</h3>
<pre><code>(7th_House_Ruling_Planet Bhukti_Dates AND 7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates) 
AND Member_Age >= 23 AND <= 26 
AND KOCHARAM_FILTER(JUPITER ASPECT 7th_House)</code></pre>

<h3>Data Flow Stages</h3>
<ol>
<li><strong>Input Processing</strong>: Query parsing and validation</li>
<li><strong>Age Extraction</strong>: <code>min_age=23, max_age=26</code></li>
<li><strong>KOCHARAM Extraction</strong>: <code>JUPITER ASPECT 7th_House</code></li>
<li><strong>Condition Processing</strong>: Two parallel conditions</li>
<li><strong>Data Retrieval</strong>: MongoDB chart data access</li>
<li><strong>Calculations</strong>: Real astronomical calculations</li>
<li><strong>Filtering</strong>: Age and KOCHARAM filtering</li>
<li><strong>Response Assembly</strong>: Final result formatting</li>
</ol>

<h3>Real Data Examples</h3>
<ul>
<li><strong>Birth Date</strong>: 1976-08-22 19:27:04</li>
<li><strong>JUPITER Position</strong>: 45.5° at age 23</li>
<li><strong>7th House</strong>: Meenam (330°-360°)</li>
<li><strong>Transit Calculation</strong>: 535.8 days for 9th aspect</li>
<li><strong>First Transit</strong>: 2001-02-09</li>
</ol>

<p>---</p>

<h2>Flowchart 5: Function Call Hierarchy</h2>

<h3>Purpose</h3>
Detailed function call tree showing dependencies and execution order for the main processing functions.

<h3>Main Function Categories</h3>
<ul>
<li><strong>Entry Points</strong>: 3 primary functions</li>
<li><strong>Query Processing</strong>: 15 parsing and evaluation functions</li>
<li><strong>KOCHARAM Functions</strong>: 25 specialized functions</li>
<li><strong>Calculation Functions</strong>: 20 mathematical functions</li>
<li><strong>Validation Functions</strong>: 10 verification functions</li>
<li><strong>Database Functions</strong>: 8 MongoDB access functions</li>
</ol>

<h3>Critical Path Analysis</h3>
<ul>
<li><strong>Primary Path</strong>: Most common execution route</li>
<li><strong>KOCHARAM Path</strong>: Enhanced algorithm execution</li>
<li><strong>Error Handling</strong>: Fallback and recovery paths</li>
<li><strong>Performance Optimization</strong>: Caching and efficiency</li>
</ol>

<p>---</p>

<h2>Flowchart 6: Database Integration Architecture</h2>

<h3>Purpose</h3>
Visualization of MongoDB integration patterns and data flow between collections.

<h3>Collections Accessed</h3>
<ul>
<li><strong>user_member_astro_profile_data</strong>: Chart data and dasha information</li>
<li><strong>member_profile</strong>: Birth details and coordinates</li>
<li><strong>astro_house_names</strong>: House name mappings</li>
<li><strong>astro_planets_aspects</strong>: Planetary aspect rules</li>
</ol>

<h3>Data Access Patterns</h3>
<ul>
<li><strong>Chart Data Retrieval</strong>: Primary data source</li>
<li><strong>Caching Strategy</strong>: Performance optimization</li>
<li><strong>Error Handling</strong>: Connection and query failures</li>
<li><strong>Data Validation</strong>: Structure and content verification</li>
</ol>

<p>---</p>

<h2>Design Specifications</h2>

<h3>Visual Design Standards</h3>
<ul>
<li><strong>Color Palette</strong>: Professional blue/green/gray scheme</li>
<li><strong>Typography</strong>: Clear, readable fonts for printing</li>
<li><strong>Layout</strong>: Optimized for A4 and Letter paper sizes</li>
<li><strong>Spacing</strong>: Adequate white space for clarity</li>
<li><strong>Symbols</strong>: Standard flowchart symbols (rectangles, diamonds, circles)</li>
</ol>

<h3>Print Optimization</h3>
<ul>
<li><strong>High Resolution</strong>: Vector-based graphics for crisp printing</li>
<li><strong>Page Breaks</strong>: Strategic placement to avoid splitting diagrams</li>
<li><strong>Margins</strong>: Adequate margins for binding</li>
<li><strong>Scale</strong>: Readable at standard print sizes</li>
</ol>

<h3>Technical Accuracy</h3>
<ul>
<li><strong>Code Alignment</strong>: Flowcharts match actual code structure</li>
<li><strong>Function Names</strong>: Exact function names from main_rule_engine.py</li>
<li><strong>Data Types</strong>: Accurate parameter and return types</li>
<li><strong>Logic Flow</strong>: Precise representation of execution paths</li>
</ol>

<p>---</p>

<h2>Implementation Notes</h2>

<h3>Mermaid Diagram Syntax</h3>
All flowcharts are created using Mermaid syntax for:
<ul>
<li><strong>Consistency</strong>: Standardized diagram format</li>
<li><strong>Maintainability</strong>: Easy updates and modifications</li>
<li><strong>Export Options</strong>: Multiple output formats (SVG, PNG, PDF)</li>
<li><strong>Integration</strong>: Compatible with documentation systems</li>
</ol>

<h3>Performance Considerations</h3>
<ul>
<li><strong>Rendering Speed</strong>: Optimized for quick generation</li>
<li><strong>File Size</strong>: Balanced detail vs. file size</li>
<li><strong>Compatibility</strong>: Works across different browsers and systems</li>
<li><strong>Accessibility</strong>: Clear contrast and readable text</li>
</ol>

<p>---</p>

<h2>Usage Guidelines</h2>

<h3>For Developers</h3>
<ul>
<li><strong>Code Understanding</strong>: Visual aid for complex logic</li>
<li><strong>Debugging</strong>: Trace execution paths</li>
<li><strong>Documentation</strong>: Reference for system architecture</li>
<li><strong>Training</strong>: Onboarding new team members</li>
</ol>

<h3>For Stakeholders</h3>
<ul>
<li><strong>System Overview</strong>: High-level understanding</li>
<li><strong>Technical Review</strong>: Architecture validation</li>
<li><strong>Planning</strong>: Future development decisions</li>
<li><strong>Communication</strong>: Clear technical communication</li>
</ol>

<h3>For Documentation</h3>
<ul>
<li><strong>Technical Manuals</strong>: Integration with written documentation</li>
<li><strong>Presentations</strong>: Visual aids for technical presentations</li>
<li><strong>Training Materials</strong>: Educational resources</li>
<li><strong>Reference Guides</strong>: Quick visual reference</li>
</ol>

<p>---</p>

<h2>Flowchart Implementations</h2>

<h3>Flowchart 1: Complete System Architecture</h3>
<em>Comprehensive system overview showing all layers and components</em>

<h3>Flowchart 2: KOCHARAM Enhanced 9-Step Algorithm</h3>
<em>Detailed visualization of the 100% accurate planetary transit calculation process</em>

<h3>Flowchart 3: Logical Operators Processing</h3>
<em>Set theory implementation for AND, OR, NOT operations</em>

<h3>Flowchart 4: Function Call Hierarchy</h3>
<em>Complete dependency map of all 212+ functions with line numbers</em>

<p>---</p>

<h2>Print Instructions</h2>

<h3>For High-Quality Printing:</h3>
<ol>
<li><strong>Open the HTML version</strong> of this document in Chrome or Safari</li>
<li><strong>Press Cmd+P</strong> (Mac) or <strong>Ctrl+P</strong> (Windows)</li>
<li><strong>Select Settings</strong>:</li>
<li>Paper Size: <strong>A4</strong> or <strong>Letter</strong></li>
<li>Margins: <strong>Default</strong></li>
<li>Scale: <strong>100%</strong></li>
<li>Background Graphics: <strong>Enabled</strong> (for colors)</li>
<li><strong>Choose "Save as PDF"</strong> or print directly</li>
</ol>

<h3>For Flowchart Viewing:</h3>
<ul>
<li><strong>Mermaid diagrams</strong> are rendered as interactive SVG graphics</li>
<li><strong>Zoom and pan</strong> functionality available in browser</li>
<li><strong>High resolution</strong> suitable for detailed technical review</li>
<li><strong>Color coding</strong> helps distinguish different system layers</li>
</ol>

<p>---</p>

<strong>Document Version</strong>: 1.0
<strong>Created</strong>: 2025-01-22
<strong>Total Flowcharts</strong>: 4 comprehensive diagrams
<strong>Functions Documented</strong>: 212+ with line numbers
<strong>Optimized For</strong>: Professional printing and technical documentation
<strong>Compatibility</strong>: A4/Letter paper, high-resolution printing
    
    <div class="print-footer">
        <p>Fortune Lens Rule Engine - Technical Documentation</p>
        <p>For best printing results: Use Chrome/Safari → Print → More Settings → Paper Size: A4 → Margins: Default</p>
    </div>
</body>
</html>