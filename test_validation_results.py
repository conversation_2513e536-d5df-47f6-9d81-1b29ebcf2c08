#!/usr/bin/env python3
"""
Test specific validation results
"""

import sys
import os
import json
from datetime import datetime

# Add the project path
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

try:
    # Import Flask app factory and create app context
    from app import create_app
    from app.services.rule_engine.main_rule_engine import process_rule_engine_request
    
    # Create Flask app with development config
    app = create_app('development')
    
    # Test data
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Bhukti_Dates)AND KOCHARAM_FILTER(JUPITER in 7th_House)",
        "chart_type": "D1"
    }
    
    print("🔍 Testing Sign Validation Results")
    print("=" * 60)
    
    with app.app_context():
        result = process_rule_engine_request(test_data)
    
    if result.get('success', False) and 'result' in result:
        periods = result['result']
        
        print(f"📊 Found {len(periods)} periods with KOCHARAM data")
        print()
        
        # Analyze validation results
        perfect_periods = []
        partial_periods = []
        failed_periods = []
        
        for i, period in enumerate(periods):
            if 'kocharam_filter' in period:
                kf = period['kocharam_filter']
                
                start_sign_valid = kf.get('start_sign_validation', False)
                end_sign_valid = kf.get('end_sign_validation', False)
                start_long = kf.get('predicted_start_longitude', 'N/A')
                end_long = kf.get('predicted_end_longitude', 'N/A')
                start_sign = kf.get('predicted_start_sign', 'N/A')
                end_sign = kf.get('predicted_end_sign', 'N/A')
                
                period_info = {
                    'period': i + 1,
                    'dates': f"{period.get('start_date', 'N/A')} to {period.get('end_date', 'N/A')}",
                    'start_longitude': start_long,
                    'end_longitude': end_long,
                    'start_sign': start_sign,
                    'end_sign': end_sign,
                    'start_valid': start_sign_valid,
                    'end_valid': end_sign_valid
                }
                
                if start_sign_valid and end_sign_valid:
                    perfect_periods.append(period_info)
                elif start_sign_valid or end_sign_valid:
                    partial_periods.append(period_info)
                else:
                    failed_periods.append(period_info)
        
        # Display results
        print(f"🌟 PERFECT PERIODS (both validations true): {len(perfect_periods)}")
        for p in perfect_periods:
            print(f"   Period {p['period']}: {p['dates']}")
            print(f"     Start: {p['start_longitude']}° in {p['start_sign']} ✅")
            print(f"     End: {p['end_longitude']}° in {p['end_sign']} ✅")
            print()
        
        print(f"⚠️ PARTIAL PERIODS (one validation true): {len(partial_periods)}")
        for p in partial_periods:
            print(f"   Period {p['period']}: {p['dates']}")
            start_status = "✅" if p['start_valid'] else "❌"
            end_status = "✅" if p['end_valid'] else "❌"
            print(f"     Start: {p['start_longitude']}° in {p['start_sign']} {start_status}")
            print(f"     End: {p['end_longitude']}° in {p['end_sign']} {end_status}")
            print()
        
        print(f"❌ FAILED PERIODS (no validations): {len(failed_periods)}")
        for p in failed_periods:
            print(f"   Period {p['period']}: {p['dates']}")
            print(f"     Start: {p['start_longitude']}° in {p['start_sign']} ❌")
            print(f"     End: {p['end_longitude']}° in {p['end_sign']} ❌")
            
            # Check if longitude suggests it should be in Meenam
            if isinstance(p['start_longitude'], (int, float)):
                if 330 <= p['start_longitude'] <= 360:
                    print(f"     🔍 Start longitude {p['start_longitude']}° IS in Meenam range!")
            if isinstance(p['end_longitude'], (int, float)):
                if 330 <= p['end_longitude'] <= 360:
                    print(f"     🔍 End longitude {p['end_longitude']}° IS in Meenam range!")
            print()
        
        # Summary
        total = len(perfect_periods) + len(partial_periods) + len(failed_periods)
        print(f"📈 SUMMARY:")
        print(f"   Perfect: {len(perfect_periods)}/{total} ({len(perfect_periods)/total*100:.1f}%)")
        print(f"   Partial: {len(partial_periods)}/{total} ({len(partial_periods)/total*100:.1f}%)")
        print(f"   Failed: {len(failed_periods)}/{total} ({len(failed_periods)/total*100:.1f}%)")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
