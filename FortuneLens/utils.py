# Utility functions for Fortune Lens

import math
from datetime import datetime, timedelta

def degrees_to_dms(degrees):
    """Convert decimal degrees to degrees, minutes, seconds"""
    d = int(degrees)
    m = int((degrees - d) * 60)
    s = ((degrees - d) * 60 - m) * 60
    return d, m, s

def dms_to_degrees(degrees, minutes, seconds):
    """Convert degrees, minutes, seconds to decimal degrees"""
    return degrees + minutes/60 + seconds/3600

def normalize_degrees(degrees):
    """Normalize degrees to 0-360 range"""
    while degrees < 0:
        degrees += 360
    while degrees >= 360:
        degrees -= 360
    return degrees

def get_house_from_longitude(longitude, lagna_longitude):
    """Get house number from longitude"""
    relative_longitude = normalize_degrees(longitude - lagna_longitude)
    house = int(relative_longitude / 30) + 1
    return house if house <= 12 else house - 12

def get_nakshatra_from_longitude(longitude):
    """Get nakshatra from longitude"""
    nakshatra_length = 360 / 27  # Each nakshatra is 13°20'
    nakshatra_number = int(longitude / nakshatra_length) + 1
    return nakshatra_number

def get_pada_from_longitude(longitude):
    """Get pada from longitude"""
    nakshatra_length = 360 / 27
    pada_length = nakshatra_length / 4
    pada_number = int((longitude % nakshatra_length) / pada_length) + 1
    return pada_number

def julian_day_number(year, month, day):
    """Calculate Julian Day Number"""
    a = (14 - month) // 12
    y = year + 4800 - a
    m = month + 12 * a - 3
    
    jdn = day + (153 * m + 2) // 5 + 365 * y + y // 4 - y // 100 + y // 400 - 32045
    return jdn

def format_time_duration(days):
    """Format duration in days to years, months, days"""
    years = days // 365
    remaining_days = days % 365
    months = remaining_days // 30
    days_left = remaining_days % 30
    
    return {
        'years': years,
        'months': months,
        'days': days_left,
        'total_days': days
    }
