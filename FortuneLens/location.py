# Location utilities for Fortune Lens

import requests
import json

def get_location_coordinates(place_name):
    """Get latitude and longitude for a place name"""
    # Default coordinates for major cities
    default_locations = {
        'Chennai': {'latitude': 13.0878, 'longitude': 80.2785, 'timezone': 5.5},
        'Mumbai': {'latitude': 19.0760, 'longitude': 72.8777, 'timezone': 5.5},
        'Delhi': {'latitude': 28.7041, 'longitude': 77.1025, 'timezone': 5.5},
        'Bangalore': {'latitude': 12.9716, 'longitude': 77.5946, 'timezone': 5.5},
        'Kolkata': {'latitude': 22.5726, 'longitude': 88.3639, 'timezone': 5.5},
        'Hyderabad': {'latitude': 17.3850, 'longitude': 78.4867, 'timezone': 5.5},
        'Pune': {'latitude': 18.5204, 'longitude': 73.8567, 'timezone': 5.5},
        'Ahmedabad': {'latitude': 23.0225, 'longitude': 72.5714, 'timezone': 5.5},
        'Jaipur': {'latitude': 26.9124, 'longitude': 75.7873, 'timezone': 5.5},
        'Lucknow': {'latitude': 26.8467, 'longitude': 80.9462, 'timezone': 5.5}
    }
    
    if place_name in default_locations:
        return default_locations[place_name]
    
    # Default to Chennai if place not found
    return default_locations['Chennai']

def get_timezone_offset(latitude, longitude):
    """Get timezone offset for given coordinates"""
    # Simplified timezone calculation for India
    # Most of India uses IST (UTC+5:30)
    return 5.5

def validate_coordinates(latitude, longitude):
    """Validate latitude and longitude values"""
    if not (-90 <= latitude <= 90):
        raise ValueError("Latitude must be between -90 and 90 degrees")
    
    if not (-180 <= longitude <= 180):
        raise ValueError("Longitude must be between -180 and 180 degrees")
    
    return True

def format_location_info(place_name, latitude, longitude, timezone_offset):
    """Format location information"""
    return {
        'place_name': place_name,
        'latitude': round(latitude, 4),
        'longitude': round(longitude, 4),
        'timezone_offset': timezone_offset,
        'coordinates_string': f"{latitude:.4f}°N, {longitude:.4f}°E"
    }
