# Astro Insights Pro

**Professional Astrological Analysis Platform**

A comprehensive, enterprise-grade astrological analysis system built with Flask, providing advanced rule engine capabilities, chart generation, and predictive analytics.

## 🌟 Features

### Core Capabilities
- **Advanced Rule Engine**: Complex astrological rule evaluation with logical operators
- **Chart Generation**: Complete divisional chart calculations (D1-D60)
- **Dasha Analysis**: Comprehensive Vimshottari dasha calculations
- **Marriage Prediction**: Advanced compatibility analysis and date prediction
- **Career Insights**: Professional astrological career guidance
- **Transit Analysis**: Real-time planetary transit calculations

### Technical Excellence
- **RESTful API**: Clean, documented API endpoints
- **MongoDB Integration**: Scalable data storage
- **Professional Architecture**: Modular, maintainable codebase
- **Error Handling**: Comprehensive error management
- **Validation**: Input validation and data integrity
- **Documentation**: Complete API documentation

## 🏗️ Architecture

```
astro_insights_pro/          # Main application
├── app/                     # Core application
│   ├── api/                 # API endpoints
│   ├── services/            # Business logic
│   ├── schemas/             # Data validation
│   ├── errors/              # Error handling
│   └── config.py            # Configuration
├── docs/                    # Documentation
├── tests/                   # Test suites
├── deployment/              # Deployment configs
├── scripts/                 # Utility scripts
└── config/                  # Environment configs
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- MongoDB 4.4+
- pip package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd fortune_lens
   ```

2. **Install dependencies**
   ```bash
   cd astro_insights_pro
   pip install -r requirements.txt
   ```

3. **Configure environment**
   ```bash
   cp config/.env.example config/.env
   # Edit config/.env with your settings
   ```

4. **Run the application**
   ```bash
   cd astro_insights_pro
   python run.py
   ```

5. **Access the API**
   - Base URL: `http://localhost:5003`
   - API Documentation: `http://localhost:5003/api/docs`

## 📚 API Documentation

### Rule Engine
```http
POST /api/rule-engine/
Content-Type: application/json

{
  "user_profile_id": 1,
  "member_profile_id": 1,
  "query": "7th_House_Ruling_Planet Bhukti_Dates",
  "chart_type": "D1"
}
```

### Chart Generation
```http
POST /api/charts/generate
Content-Type: application/json

{
  "user_profile_id": 1,
  "member_profile_id": 1,
  "chart_type": "D1"
}
```

## 🔧 Configuration

### Environment Variables
- `MONGODB_URI`: MongoDB connection string
- `SECRET_KEY`: Application secret key
- `DEBUG`: Debug mode (True/False)
- `PORT`: Application port (default: 5003)

### Database Setup
The application uses MongoDB with the following collections:
- `user_profile`: User information
- `member_profile`: Member details
- `user_member_astro_profile_data`: Astrological data

## 🧪 Testing

```bash
# Run all tests
python -m pytest tests/

# Run specific test suite
python -m pytest tests/test_rule_engine.py

# Run with coverage
python -m pytest --cov=app tests/
```

## 📈 Performance

- **Response Time**: < 200ms for standard queries
- **Throughput**: 1000+ requests/minute
- **Scalability**: Horizontal scaling supported
- **Caching**: Redis integration for performance

## 🔒 Security

- Input validation and sanitization
- SQL injection prevention
- Rate limiting
- CORS configuration
- JWT authentication support

## 📦 Deployment

### Docker
```bash
docker build -t astro-insights-pro .
docker run -p 5003:5003 astro-insights-pro
```

### Production
See `deployment/` directory for production deployment guides.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Documentation: [docs/](./docs/)
- Issues: GitHub Issues

## 🏆 Credits

Developed with ❤️ by the Astro Insights Pro Team

---

**Astro Insights Pro** - Professional Astrological Analysis Platform
# Fortune-Lens
# Fortune-Lens
# Fortune-Lens
