#!/usr/bin/env python3
"""
Find the correct Jupiter transit start and end dates for 9th aspect to 7th house
"""

import sys
import os
from datetime import datetime, timedelta
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

from app.services.chart_service import generate_chart

def find_jupiter_transit_dates():
    """Find exact transit start and end dates for Jupiter 9th aspect to 7th house"""
    
    print("=== FINDING CORRECT JUPITER TRANSIT DATES ===")
    print("Looking for Jupiter 9th aspect to 7th house (Jupiter should be in 90°-120° range)")
    print("Transit START: Jupiter enters 90°-95° range")
    print("Transit END: Jupiter enters 115°-120° range")
    
    # Search from 2002 to 2004
    start_date = datetime(2002, 1, 1)
    end_date = datetime(2004, 12, 31)
    
    transit_start_found = False
    transit_end_found = False
    transit_start_date = None
    transit_end_date = None
    
    current_date = start_date
    step_days = 7  # Check every week initially
    
    print(f"\nSearching from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}...")
    
    while current_date <= end_date:
        jupiter_pos = get_jupiter_position(current_date)
        
        if jupiter_pos:
            longitude = jupiter_pos['longitude']
            
            # Check if Jupiter is in the 9th aspect range (90°-120°)
            if 90 <= longitude <= 120:
                degree_in_house = longitude - 90
                
                # Transit START: 0°-5° range (90°-95°)
                if 0 <= degree_in_house <= 5 and not transit_start_found:
                    transit_start_found = True
                    transit_start_date = current_date
                    print(f"🎯 TRANSIT START FOUND: {current_date.strftime('%Y-%m-%d')}")
                    print(f"   Jupiter at {longitude:.2f}° ({degree_in_house:.2f}° in house)")
                    
                    # Refine the start date with daily precision
                    refined_start = refine_transit_date(current_date - timedelta(days=7), current_date, 'start')
                    if refined_start:
                        transit_start_date = refined_start
                        print(f"   Refined START: {refined_start.strftime('%Y-%m-%d')}")
                
                # Transit END: 25°-30° range (115°-120°)
                elif 25 <= degree_in_house <= 30 and not transit_end_found:
                    transit_end_found = True
                    transit_end_date = current_date
                    print(f"🎯 TRANSIT END FOUND: {current_date.strftime('%Y-%m-%d')}")
                    print(f"   Jupiter at {longitude:.2f}° ({degree_in_house:.2f}° in house)")
                    
                    # Refine the end date with daily precision
                    refined_end = refine_transit_date(current_date - timedelta(days=7), current_date, 'end')
                    if refined_end:
                        transit_end_date = refined_end
                        print(f"   Refined END: {refined_end.strftime('%Y-%m-%d')}")
                
                # Print progress for active transit
                if transit_start_found and not transit_end_found:
                    if current_date.day == 1:  # Print monthly updates
                        print(f"   Active transit: {current_date.strftime('%Y-%m-%d')} - Jupiter at {longitude:.2f}° ({degree_in_house:.2f}° in house)")
        
        current_date += timedelta(days=step_days)
        
        # Break if both dates found
        if transit_start_found and transit_end_found:
            break
    
    print(f"\n=== FINAL RESULTS ===")
    if transit_start_date:
        print(f"✅ Transit START: {transit_start_date.strftime('%Y-%m-%d')}")
    else:
        print("❌ Transit START not found")
    
    if transit_end_date:
        print(f"✅ Transit END: {transit_end_date.strftime('%Y-%m-%d')}")
    else:
        print("❌ Transit END not found")
    
    if transit_start_date and transit_end_date:
        duration = (transit_end_date - transit_start_date).days
        print(f"📅 Transit DURATION: {duration} days")
        print(f"\n🔍 COMPARISON WITH KOCHARAM:")
        print(f"   KOCHARAM predicted: 2002-09-27 to 2003-09-27 (365 days)")
        print(f"   Corrected calculation: {transit_start_date.strftime('%Y-%m-%d')} to {transit_end_date.strftime('%Y-%m-%d')} ({duration} days)")
        
        # Check if KOCHARAM date falls within corrected range
        kocharam_date = datetime(2002, 9, 27)
        if transit_start_date <= kocharam_date <= transit_end_date:
            print(f"   ✅ KOCHARAM date falls within corrected transit period")
        else:
            print(f"   ❌ KOCHARAM date is outside corrected transit period")

def refine_transit_date(start_date, end_date, transit_type):
    """Refine transit date with daily precision"""
    current_date = start_date
    
    while current_date <= end_date:
        jupiter_pos = get_jupiter_position(current_date)
        
        if jupiter_pos:
            longitude = jupiter_pos['longitude']
            
            if 90 <= longitude <= 120:
                degree_in_house = longitude - 90
                
                if transit_type == 'start' and 0 <= degree_in_house <= 5:
                    return current_date
                elif transit_type == 'end' and 25 <= degree_in_house <= 30:
                    return current_date
        
        current_date += timedelta(days=1)
    
    return None

def get_jupiter_position(date):
    """Get Jupiter position for a specific date"""
    try:
        birth_data = {
            'user_birthdate': date.strftime('%d-%m-%Y'),
            'user_birthtime': '00:00:00',
            'user_birthplace': 'Cuddalore',
            'user_state': 'Tamil Nadu',
            'user_country': 'India'
        }
        
        chart_data = generate_chart(birth_data, chart_types=['D1'])
        
        if 'error' in chart_data:
            return None
        
        d1_chart = chart_data.get('D1', {})
        planets_precise = d1_chart.get('planets_precise', {})
        
        if 'jupiter' not in planets_precise:
            return None
        
        jupiter_data = planets_precise['jupiter']
        sign = jupiter_data.get('sign', 'Unknown')
        degree = jupiter_data.get('degree', 0)
        
        # Map sign to longitude base
        sign_to_longitude = {
            'Mesham': 0, 'Rishabam': 30, 'Midunam': 60, 'Kadagam': 90,
            'Simmam': 120, 'Kanni': 150, 'Thulam': 180, 'Virichigam': 210,
            'Dhanusu': 240, 'Magaram': 270, 'Kumbam': 300, 'Meenam': 330
        }
        
        longitude_base = sign_to_longitude.get(sign, 0)
        longitude = longitude_base + degree
        
        return {
            'longitude': longitude,
            'sign': sign,
            'degree': degree
        }
        
    except Exception as e:
        return None

if __name__ == "__main__":
    find_jupiter_transit_dates()
