#!/usr/bin/env python3
"""
Test the degree-based KOCHARAM calculation
"""

import sys
import os
from datetime import datetime, timedelta
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

from app import create_app
from app.services.rule_engine.main_rule_engine import generate_chart_for_date, get_planet_position_from_chart

# Create Flask app
app = create_app('development')

birth_place_data = {
    'user_birthdate': '1978-05-26', 
    'user_birthtime': '15:30:00', 
    'user_birthplace': 'Cuddalore', 
    'user_state': 'Tamil Nadu', 
    'user_country': 'India'
}

def test_degree_calculation():
    print("🔍 Testing Degree-Based KOCHARAM Calculation")
    print("=" * 60)
    
    # Test case from the output
    current_longitude = 36.39  # Jupiter in Rishabam
    target_start = 336.0       # Middle of 331-340° range
    target_end = 355.0         # Middle of 350-360° range
    
    # Jupiter's precise motion: 360° / 4383 days = 0.08214°/day
    jupiter_daily_motion = 360.0 / 4383.0
    
    print(f"📊 Input Parameters:")
    print(f"   Current Jupiter longitude: {current_longitude:.2f}°")
    print(f"   Target start longitude: {target_start:.1f}°")
    print(f"   Target end longitude: {target_end:.1f}°")
    print(f"   Jupiter daily motion: {jupiter_daily_motion:.5f}°/day")
    
    # Calculate distances
    distance_to_start = (target_start - current_longitude) % 360
    distance_to_end = (target_end - current_longitude) % 360
    
    print(f"\n🧮 Distance Calculations:")
    print(f"   Distance to start: {distance_to_start:.1f}°")
    print(f"   Distance to end: {distance_to_end:.1f}°")
    
    # Calculate days
    days_to_start = distance_to_start / jupiter_daily_motion
    days_to_end = distance_to_end / jupiter_daily_motion
    
    print(f"\n⏰ Time Calculations:")
    print(f"   Days to start: {days_to_start:.0f} days ({days_to_start/365:.1f} years)")
    print(f"   Days to end: {days_to_end:.0f} days ({days_to_end/365:.1f} years)")
    
    # Calculate dates from 1976-08-22
    start_date = datetime(1976, 8, 22)
    predicted_start = start_date + timedelta(days=int(days_to_start))
    predicted_end = start_date + timedelta(days=int(days_to_end))
    
    print(f"\n📅 Predicted Dates:")
    print(f"   Start date: {predicted_start.strftime('%Y-%m-%d')}")
    print(f"   End date: {predicted_end.strftime('%Y-%m-%d')}")
    
    # Verify with actual charts
    print(f"\n🔍 Chart Verification:")
    
    with app.app_context():
        # Test predicted start date
        start_chart = generate_chart_for_date(predicted_start.strftime('%Y-%m-%d'), birth_place_data)
        if start_chart:
            jupiter_pos = get_planet_position_from_chart(start_chart, "JUPITER")
            if jupiter_pos:
                print(f"   Start date verification:")
                print(f"     Date: {predicted_start.strftime('%Y-%m-%d')}")
                print(f"     Jupiter: {jupiter_pos['longitude']:.2f}° in {jupiter_pos['sign']}")
                print(f"     Target: 336.0° in Meenam")
                print(f"     Accuracy: {abs(jupiter_pos['longitude'] - target_start):.1f}° difference")
        
        # Test predicted end date
        end_chart = generate_chart_for_date(predicted_end.strftime('%Y-%m-%d'), birth_place_data)
        if end_chart:
            jupiter_pos = get_planet_position_from_chart(end_chart, "JUPITER")
            if jupiter_pos:
                print(f"   End date verification:")
                print(f"     Date: {predicted_end.strftime('%Y-%m-%d')}")
                print(f"     Jupiter: {jupiter_pos['longitude']:.2f}° in {jupiter_pos['sign']}")
                print(f"     Target: 355.0° in Meenam")
                print(f"     Accuracy: {abs(jupiter_pos['longitude'] - target_end):.1f}° difference")
        
        # Test known working dates for comparison
        print(f"\n📋 Known Working Dates (for comparison):")
        test_dates = ["1987-03-22", "1987-11-06"]
        
        for test_date in test_dates:
            chart = generate_chart_for_date(test_date, birth_place_data)
            if chart:
                jupiter_pos = get_planet_position_from_chart(chart, "JUPITER")
                if jupiter_pos:
                    degree_in_meenam = jupiter_pos['longitude'] - 330 if jupiter_pos['sign'] == 'Meenam' else 'N/A'
                    print(f"   {test_date}: Jupiter at {jupiter_pos['longitude']:.2f}° in {jupiter_pos['sign']}")
                    if jupiter_pos['sign'] == 'Meenam':
                        print(f"     Degree in Meenam: {degree_in_meenam:.1f}°")

if __name__ == "__main__":
    test_degree_calculation()
