#!/usr/bin/env python3
"""
Direct test of the KOCHARAM query without using the API
"""

import sys
import os
import json
from datetime import datetime

# Add the project path
sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens/astro_insights_pro')

try:
    # Import Flask app factory and create app context
    from app import create_app
    from app.services.rule_engine.main_rule_engine import process_rule_engine_request

    print("✅ Successfully imported rule engine")

    # Create Flask app with development config
    app = create_app('development')

    print("✅ Flask app created successfully")
    
    # Test data - your exact query
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Bhukti_Dates)AND KOCHARAM_FILTER(JUPITER in 7th_House)",
        "chart_type": "D1"
    }
    
    print("🔍 Testing KOCHARAM Query")
    print("=" * 50)
    print(f"Query: {test_data['query']}")
    print(f"User Profile ID: {test_data['user_profile_id']}")
    print(f"Member Profile ID: {test_data['member_profile_id']}")
    print(f"Chart Type: {test_data['chart_type']}")
    print()
    
    # Process the request within Flask app context
    print("⏳ Processing query...")
    start_time = datetime.now()

    with app.app_context():
        result = process_rule_engine_request(test_data)

    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()
    
    print(f"⏱️ Processing completed in {processing_time:.2f} seconds")
    print()
    
    # Display results
    if result.get('success', False):
        print("✅ Query executed successfully!")
        print("=" * 50)
        
        # Pretty print the result
        print(json.dumps(result, indent=2, default=str))
        
        # Extract key information
        if 'result' in result and isinstance(result['result'], list):
            periods = result['result']
            print(f"\n📊 Summary: Found {len(periods)} dasha periods")
            
            # Show KOCHARAM results
            kocharam_periods = [p for p in periods if 'kocharam_filter' in p]
            print(f"🪐 KOCHARAM periods: {len(kocharam_periods)}")
            
            # Show first few periods with KOCHARAM details
            for i, period in enumerate(kocharam_periods[:3]):
                print(f"\n🔍 Period {i+1}:")
                print(f"   Dasha: {period.get('dasha_lord', 'N/A')}")
                print(f"   Bhukti: {period.get('bhukti_lord', 'N/A')}")
                print(f"   Start: {period.get('start_date', 'N/A')}")
                print(f"   End: {period.get('end_date', 'N/A')}")
                
                if 'kocharam_filter' in period:
                    kf = period['kocharam_filter']
                    print(f"   KOCHARAM Transit Found: {kf.get('transit_found', False)}")
                    if kf.get('predicted_start_date'):
                        print(f"   Transit Start: {kf.get('predicted_start_date')}")
                    if kf.get('predicted_end_date'):
                        print(f"   Transit End: {kf.get('predicted_end_date')}")
    else:
        print("❌ Query failed!")
        print("=" * 50)
        print(json.dumps(result, indent=2, default=str))
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
