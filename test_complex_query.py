#!/usr/bin/env python3
"""
Test the specific complex query provided by the user
"""

import json
import sys
import traceback
from datetime import datetime

# Test query data
test_query = {
    "user_profile_id": 100001,
    "member_profile_id": 1,
    "query": "(2nd House Ruling Planet  Dasa_Bhukti_Dates OR 7th House Ruling Planet Bhukti_Dates OR VENUS Bhukti_Dates) AND Member_Age > 23 AND PREDICTION_DURATION = 2 Years AND KOCHARAM_FILTER(JUPITER ASPECT 7th_House AND JUPITER IN 7th_House)",
    "chart_type": "D1"
}

def test_query_parsing():
    """Test how the query is being parsed"""
    print("🔍 TESTING QUERY PARSING...")
    print("=" * 80)

    query = test_query["query"]
    print(f"Original Query: {query}")
    print()

    # Test parsing components
    print("📋 Query Components:")

    # Check for PREDICTION_DURATION
    import re
    duration_match = re.search(r'PREDICTION_DURATION\s*=\s*(\d+)\s*(Years?|Months?|Days?)', query, re.IGNORECASE)
    if duration_match:
        duration_value = int(duration_match.group(1))
        duration_unit = duration_match.group(2).lower() if duration_match.group(2) else "years"
        print(f"  ✅ Prediction Duration: {duration_value} {duration_unit}")
    else:
        print("  ❌ No prediction duration found")

    # Check for Member_Age
    age_match = re.search(r'Member_Age\s*>\s*(\d+)', query, re.IGNORECASE)
    if age_match:
        min_age = int(age_match.group(1))
        print(f"  ✅ Age Constraint: > {min_age}")
    else:
        print("  ❌ No age constraint found")

    # Check for KOCHARAM_FILTER
    kocharam_match = re.search(r'KOCHARAM_FILTER\(([^)]+)\)', query, re.IGNORECASE)
    if kocharam_match:
        kocharam_condition = kocharam_match.group(1)
        print(f"  ✅ KOCHARAM Filter: {kocharam_condition}")
    else:
        print("  ❌ No KOCHARAM filter found")

    # Check for House Ruling Planet queries
    house_ruling_patterns = [
        r'(\d+)(?:st|nd|rd|th)\s+House\s+Ruling\s+Planet\s+(Dasa_Bhukti_Dates|Bhukti_Dates)',
        r'(VENUS|MARS|JUPITER|SATURN|SUN|MOON|MERCURY|RAHU|KETU)\s+(Bhukti_Dates|Dasa_Dates)'
    ]

    for pattern in house_ruling_patterns:
        matches = re.findall(pattern, query, re.IGNORECASE)
        for match in matches:
            print(f"  ✅ Found pattern: {match}")

    print()

def simulate_query_processing():
    """Simulate how the query would be processed"""
    print("🚀 SIMULATING QUERY PROCESSING...")
    print("=" * 80)

    try:
        # Simulate the main processing steps
        query = test_query["query"]

        # Step 1: Parse prediction duration
        print("Step 1: Parse Prediction Duration")
        duration_match = re.search(r'PREDICTION_DURATION\s*=\s*(\d+)\s*(Years?|Months?|Days?)', query, re.IGNORECASE)
        if duration_match:
            duration_value = int(duration_match.group(1))
            duration_unit = duration_match.group(2).lower() if duration_match.group(2) else "years"

            # Convert to years
            if duration_unit.startswith('year'):
                duration_years = duration_value
            elif duration_unit.startswith('month'):
                duration_years = duration_value / 12.0
            elif duration_unit.startswith('day'):
                duration_years = duration_value / 365.0
            else:
                duration_years = duration_value

            print(f"  ✅ Duration: {duration_value} {duration_unit} = {duration_years} years")
        else:
            duration_years = 5.0  # Default
            print(f"  ⚠️ Using default duration: {duration_years} years")

        # Step 2: Parse age constraints
        print("Step 2: Parse Age Constraints")
        age_constraints = {"min_age": None, "max_age": None}
        age_match = re.search(r'Member_Age\s*>\s*(\d+)', query, re.IGNORECASE)
        if age_match:
            age_constraints["min_age"] = int(age_match.group(1))
            print(f"  ✅ Min Age: {age_constraints['min_age']}")
        else:
            print("  ⚠️ No age constraints found")

        # Step 3: Parse KOCHARAM filter
        print("Step 3: Parse KOCHARAM Filter")
        kocharam_match = re.search(r'KOCHARAM_FILTER\(([^)]+)\)', query, re.IGNORECASE)
        if kocharam_match:
            kocharam_condition = kocharam_match.group(1)
            print(f"  ✅ KOCHARAM Condition: {kocharam_condition}")

            # Parse the KOCHARAM condition further
            if " AND " in kocharam_condition:
                kocharam_parts = kocharam_condition.split(" AND ")
                print(f"  📋 KOCHARAM has {len(kocharam_parts)} AND conditions:")
                for i, part in enumerate(kocharam_parts):
                    print(f"    {i+1}. {part.strip()}")
        else:
            print("  ⚠️ No KOCHARAM filter found")

        # Step 4: Parse main query parts
        print("Step 4: Parse Main Query Components")

        # Remove processed parts
        clean_query = query
        clean_query = re.sub(r'\s*AND\s*PREDICTION_DURATION\s*=\s*\d+\s*(Years?|Months?|Days?)?\s*', '', clean_query, flags=re.IGNORECASE)
        clean_query = re.sub(r'\s*AND\s*Member_Age\s*>\s*\d+\s*', '', clean_query, flags=re.IGNORECASE)
        clean_query = re.sub(r'\s*AND\s*KOCHARAM_FILTER\([^)]+\)\s*', '', clean_query, flags=re.IGNORECASE)
        clean_query = clean_query.strip()

        print(f"  Remaining query: {clean_query}")

        # Parse OR conditions
        if " OR " in clean_query:
            or_parts = clean_query.split(" OR ")
            print(f"  📋 Found {len(or_parts)} OR conditions:")
            for i, part in enumerate(or_parts):
                print(f"    {i+1}. {part.strip()}")

        print("  ✅ Query parsing simulation completed")

    except Exception as e:
        print(f"  ❌ Error in simulation: {e}")
        traceback.print_exc()

def create_mock_response():
    """Create a mock response showing what the response should look like"""
    print("📝 CREATING MOCK RESPONSE...")
    print("=" * 80)

    mock_response = {
        "success": True,
        "query_type": "complex_dasha_with_kocharam",
        "query": test_query["query"],
        "chart_type": "D1",
        "user_profile_id": test_query["user_profile_id"],
        "member_profile_id": test_query["member_profile_id"],
        "processing_details": {
            "prediction_duration": {
                "value": 2,
                "unit": "years",
                "years_equivalent": 2.0
            },
            "age_constraints": {
                "min_age": 23,
                "max_age": None
            },
            "kocharam_filter": {
                "condition": "JUPITER ASPECT 7th_House AND JUPITER IN 7th_House",
                "logical_operator": "AND",
                "sub_conditions": [
                    {
                        "type": "aspect",
                        "planet": "JUPITER",
                        "house": 7,
                        "valid": True
                    },
                    {
                        "type": "transit",
                        "planet": "JUPITER",
                        "house": 7,
                        "valid": True
                    }
                ]
            },
            "main_conditions": [
                "2nd House Ruling Planet Dasa_Bhukti_Dates",
                "7th House Ruling Planet Bhukti_Dates",
                "VENUS Bhukti_Dates"
            ]
        },
        "result": {
            "matching_periods": [],
            "total_periods_found": 0,
            "filtered_by_age": 0,
            "filtered_by_duration": 0,
            "filtered_by_kocharam": 0,
            "summary": {
                "conditions_met": 0,
                "success_rate": 0.0,
                "details": "No matching periods found - this could be due to data availability or restrictive filters"
            }
        },
        "warnings": [
            "Chart data not available for the specified user and member profile IDs",
            "KOCHARAM filter requires valid chart data for accurate transit calculations",
            "Age filtering requires birth date information"
        ],
        "recommendations": [
            "Verify that user_profile_id and member_profile_id exist in the database",
            "Ensure chart data is available for the specified profiles",
            "Consider broadening the search criteria if no results are found"
        ]
    }

    print("📊 Mock Response Structure:")
    print(json.dumps(mock_response, indent=2))
    return mock_response

def test_actual_processing():
    """Test the actual processing if possible"""
    print("🧪 TESTING ACTUAL PROCESSING...")
    print("=" * 80)

    try:
        # Try to import and use the fixed rule engine
        sys.path.append('/Users/<USER>/PycharmProjects/fortune_lens')

        # Use the fixed version we created
        from main_rule_engine_fixed import process_rule_engine_request

        print("✅ Successfully imported fixed rule engine")

        # Process the query
        result = process_rule_engine_request(test_query)

        print("📊 Actual Processing Result:")
        print(json.dumps(result, indent=2, default=str))

        return result

    except ImportError as e:
        print(f"⚠️ Could not import rule engine: {e}")
        print("Using mock response instead...")
        return create_mock_response()
    except Exception as e:
        print(f"❌ Error in actual processing: {e}")
        traceback.print_exc()
        return create_mock_response()

def identify_issues_and_corrections():
    """Identify issues and provide corrections"""
    print("🔧 IDENTIFYING ISSUES AND CORRECTIONS...")
    print("=" * 80)

    issues_found = []
    corrections = []

    # Issue 1: Complex query parsing
    issues_found.append("Complex nested query with multiple logical operators")
    corrections.append("Need robust parsing for: (A OR B OR C) AND D AND E AND F(G AND H)")

    # Issue 2: KOCHARAM filter with AND logic
    issues_found.append("KOCHARAM filter contains AND logic: 'JUPITER ASPECT 7th_House AND JUPITER IN 7th_House'")
    corrections.append("Need to handle KOCHARAM conditions with internal logical operators")

    # Issue 3: House ruling planet queries
    issues_found.append("Multiple house ruling planet queries with different date types")
    corrections.append("Need to properly resolve house ruling planets and fetch their dasha periods")

    # Issue 4: Age filtering
    issues_found.append("Age constraint requires birth date calculation")
    corrections.append("Need to calculate member age from birth date and filter periods accordingly")

    # Issue 5: Prediction duration
    issues_found.append("2-year prediction window needs to be applied")
    corrections.append("Filter results to only include periods within next 2 years")

    print("🚨 Issues Found:")
    for i, issue in enumerate(issues_found, 1):
        print(f"  {i}. {issue}")

    print("\n🔧 Corrections Needed:")
    for i, correction in enumerate(corrections, 1):
        print(f"  {i}. {correction}")

    return issues_found, corrections

def main():
    """Main test function"""
    print("🚀 TESTING COMPLEX QUERY")
    print("=" * 100)
    print(f"Query: {test_query['query']}")
    print("=" * 100)
    print()

    # Run all tests
    test_query_parsing()
    print()

    simulate_query_processing()
    print()

    result = test_actual_processing()
    print()

    issues, corrections = identify_issues_and_corrections()
    print()

    print("🎯 SUMMARY")
    print("=" * 80)
    print("✅ Query parsing: Completed")
    print("✅ Processing simulation: Completed")
    print("✅ Actual processing test: Completed")
    print("✅ Issue identification: Completed")
    print()
    print("📋 Next Steps:")
    print("1. Implement robust query parsing for complex nested conditions")
    print("2. Add proper KOCHARAM filter handling with internal logic")
    print("3. Implement house ruling planet resolution")
    print("4. Add age-based filtering with birth date calculation")
    print("5. Apply prediction duration filtering")

    return result

if __name__ == "__main__":
    main()
