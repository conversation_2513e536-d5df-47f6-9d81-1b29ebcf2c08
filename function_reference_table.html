<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Function Reference Table</title>
    
    <style>
        @media print {
            body { margin: 0.5in; }
            h1 { page-break-before: always; }
            table { page-break-inside: avoid; }
            pre { page-break-inside: avoid; }
        }
        
        body {
            font-family: 'Georgia', 'Times New Roman', serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 5px;
            margin-top: 25px;
        }
        
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            font-size: 12px;
        }
        
        th, td {
            border: 1px solid #bdc3c7;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #ecf0f1;
            font-weight: bold;
        }
        
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
        }
        
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 11px;
            border: 1px solid #e9ecef;
        }
        
        pre code {
            background: none;
            padding: 0;
        }
        
        ul, ol {
            margin: 10px 0;
            padding-left: 30px;
        }
        
        li {
            margin: 5px 0;
        }
        
        .print-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 20px;
        }
        
        .print-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .toc {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .toc h2 {
            margin-top: 0;
            border: none;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 8px 0;
        }
        
        .toc a {
            text-decoration: none;
            color: #3498db;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
    
</head>
<body>
    <div class="print-header">
        <h1>Fortune Lens Rule Engine</h1>
        <h2>Function Reference Table</h2>
        <p>Generated on /Users/<USER>/PycharmProjects/fortune_lens</p>
    </div>
    
    <h1>Fortune Lens Rule Engine - Complete Function Reference Table</h1>

<h2>Core Entry Point Functions</h2>

<table>
<thead><tr>
<th>Function Name</th>
<th>Line Range</th>
<th>Parameters</th>
<th>Return Type</th>
<th>Purpose</th>
<th>Dependencies</th>
</tr></thead><tbody>
<tr>
<td>---------------</td>
<td>------------</td>
<td>------------</td>
<td>-------------</td>
<td>---------</td>
<td>--------------</td>
</tr>
<tr>
<td><code>process_rule_engine_request</code></td>
<td>1650-1738</td>
<td><code>data: dict</code></td>
<td><code>dict</code></td>
<td>Main API entry point</td>
<td>validate_api_request_data, get_chart_data</td>
</tr>
<tr>
<td><code>validate_api_request_data</code></td>
<td>1562-1648</td>
<td><code>data</code></td>
<td><code>tuple[bool, dict, str]</code></td>
<td>Input validation</td>
<td>None</td>
</tr>
<tr>
<td><code>get_chart_data</code></td>
<td>5774-5827</td>
<td><code>user_profile_id, member_profile_id, chart_type</code></td>
<td><code>dict</code></td>
<td>Chart data retrieval</td>
<td>MongoDB access</td>
</tr>
</tbody></table>

<h2>Query Processing Functions</h2>

<table>
<thead><tr>
<th>Function Name</th>
<th>Line Range</th>
<th>Parameters</th>
<th>Return Type</th>
<th>Purpose</th>
<th>Dependencies</th>
</tr></thead><tbody>
<tr>
<td>---------------</td>
<td>------------</td>
<td>------------</td>
<td>-------------</td>
<td>---------</td>
<td>--------------</td>
</tr>
<tr>
<td><code>parse_and_evaluate_dasha_query</code></td>
<td>12894-13185</td>
<td><code>chart_data, query, chart_type, user_profile_id, member_profile_id</code></td>
<td><code>dict</code></td>
<td>Main dasha query processor</td>
<td>15+ parsing functions</td>
</tr>
<tr>
<td><code>parse_dasha_condition</code></td>
<td>10382-10786</td>
<td><code>condition: str</code></td>
<td><code>tuple[str, dict]</code></td>
<td>Parse individual dasha conditions</td>
<td>Pattern matching</td>
</tr>
<tr>
<td><code>evaluate_dasha_condition</code></td>
<td>10809-11566</td>
<td><code>condition_type, parameters, chart_data, prediction_duration</code></td>
<td><code>dict</code></td>
<td>Evaluate parsed conditions</td>
<td>Multiple evaluation functions</td>
</tr>
<tr>
<td><code>split_by_operator</code></td>
<td>13187-13225</td>
<td><code>text: str, operator: str</code></td>
<td><code>list</code></td>
<td>Split query by logical operators</td>
<td>None</td>
</tr>
</tbody></table>

<h2>KOCHARAM Filter Functions</h2>

<table>
<thead><tr>
<th>Function Name</th>
<th>Line Range</th>
<th>Parameters</th>
<th>Return Type</th>
<th>Purpose</th>
<th>Dependencies</th>
</tr></thead><tbody>
<tr>
<td>---------------</td>
<td>------------</td>
<td>------------</td>
<td>-------------</td>
<td>---------</td>
<td>--------------</td>
</tr>
<tr>
<td><code>process_kocharam_filter</code></td>
<td>1963-2080</td>
<td><code>dasha_periods, kocharam_condition, chart_data, user_profile_id, member_profile_id, performance_mode, age_constraints</code></td>
<td><code>list</code></td>
<td>Main KOCHARAM processor</td>
<td>parse_kocharam_condition, apply_enhanced_kocharam_algorithm</td>
</tr>
<tr>
<td><code>parse_kocharam_condition</code></td>
<td>36-74</td>
<td><code>kocharam_condition: str</code></td>
<td><code>dict</code></td>
<td>Parse KOCHARAM conditions</td>
<td>parse_complex_kocharam_condition, parse_single_kocharam_condition</td>
</tr>
<tr>
<td><code>apply_enhanced_kocharam_algorithm</code></td>
<td>2522-2730</td>
<td><code>dasha_periods, planet_name, target_house_number, first_dasha_date, last_dasha_date, chart_data, birth_place_data, query_type, age_start_date</code></td>
<td><code>list</code></td>
<td>9-step KOCHARAM algorithm</td>
<td>20+ calculation functions</td>
</tr>
<tr>
<td><code>process_complex_kocharam_filter</code></td>
<td>269-303</td>
<td><code>dasha_periods, parsed_condition, chart_data, user_profile_id, member_profile_id, performance_mode</code></td>
<td><code>list</code></td>
<td>Complex logical operators</td>
<td>OR/AND/NOT processing functions</td>
</tr>
</tbody></table>

<h2>Logical Operator Processing</h2>

<table>
<thead><tr>
<th>Function Name</th>
<th>Line Range</th>
<th>Parameters</th>
<th>Return Type</th>
<th>Purpose</th>
<th>Dependencies</th>
</tr></thead><tbody>
<tr>
<td>---------------</td>
<td>------------</td>
<td>------------</td>
<td>-------------</td>
<td>---------</td>
<td>--------------</td>
</tr>
<tr>
<td><code>process_or_kocharam_conditions</code></td>
<td>305-366</td>
<td><code>dasha_periods, parsed_condition, chart_data, user_profile_id, member_profile_id, performance_mode</code></td>
<td><code>list</code></td>
<td>OR logic (Union A ∪ B)</td>
<td>create_or_union_result</td>
</tr>
<tr>
<td><code>process_and_kocharam_conditions</code></td>
<td>437-517</td>
<td><code>dasha_periods, parsed_condition, chart_data, user_profile_id, member_profile_id, performance_mode</code></td>
<td><code>list</code></td>
<td>AND logic (Intersection A ∩ B)</td>
<td>create_and_intersection_result</td>
</tr>
<tr>
<td><code>process_not_kocharam_condition</code></td>
<td>820-851</td>
<td><code>dasha_periods, parsed_condition, chart_data, user_profile_id, member_profile_id, performance_mode</code></td>
<td><code>list</code></td>
<td>NOT logic (Exclusion A - B)</td>
<td>Recursive processing</td>
</tr>
<tr>
<td><code>create_or_union_result</code></td>
<td>519-595</td>
<td><code>dasha_periods, all_condition_results, all_transit_dates</code></td>
<td><code>list</code></td>
<td>Create OR union results</td>
<td>Set theory operations</td>
</tr>
</tbody></table>

<h2>Calculation Functions</h2>

<table>
<thead><tr>
<th>Function Name</th>
<th>Line Range</th>
<th>Parameters</th>
<th>Return Type</th>
<th>Purpose</th>
<th>Dependencies</th>
</tr></thead><tbody>
<tr>
<td>---------------</td>
<td>------------</td>
<td>------------</td>
<td>-------------</td>
<td>---------</td>
<td>--------------</td>
</tr>
<tr>
<td><code>get_planet_position_on_date</code></td>
<td>2732-2760</td>
<td><code>planet_name: str, date_str: str, birth_place_data: dict</code></td>
<td><code>float</code></td>
<td>Planet longitude calculation</td>
<td>generate_chart_for_date</td>
</tr>
<tr>
<td><code>calculate_angular_distance</code></td>
<td>1201-1245</td>
<td><code>target_angle: float, current_angle: float</code></td>
<td><code>float</code></td>
<td>Angular distance calculation</td>
<td>None</td>
</tr>
<tr>
<td><code>calculate_all_aspect_arrays</code></td>
<td>1247-1368</td>
<td><code>planet_name, target_house_number, current_longitude, chart_data</code></td>
<td><code>dict</code></td>
<td>Aspect angle calculations</td>
<td>Planetary aspect rules</td>
</tr>
<tr>
<td><code>calculate_transit_time</code></td>
<td>2945-2961</td>
<td><code>rotation_period: int, angular_distance: float</code></td>
<td><code>float</code></td>
<td>Transit time calculation</td>
<td>None</td>
</tr>
<tr>
<td><code>get_target_house_angle</code></td>
<td>2762-2788</td>
<td><code>chart_data: dict, target_house_number: int</code></td>
<td><code>float</code></td>
<td>House angle from chart</td>
<td>Chart data parsing</td>
</tr>
</tbody></table>

<h2>Dasha Period Functions</h2>

<table>
<thead><tr>
<th>Function Name</th>
<th>Line Range</th>
<th>Parameters</th>
<th>Return Type</th>
<th>Purpose</th>
<th>Dependencies</th>
</tr></thead><tbody>
<tr>
<td>---------------</td>
<td>------------</td>
<td>------------</td>
<td>-------------</td>
<td>---------</td>
<td>--------------</td>
</tr>
<tr>
<td><code>get_dasha_periods_for_planet</code></td>
<td>9867-9925</td>
<td><code>chart_data: dict, planet_name: str, dasha_type: str</code></td>
<td><code>list</code></td>
<td>Planet dasha periods</td>
<td>parse_dasha_string</td>
</tr>
<tr>
<td><code>get_house_ruling_planet_dasha_periods</code></td>
<td>9927-9950</td>
<td><code>chart_data: dict, house_number: int, dasha_type: str</code></td>
<td><code>list</code></td>
<td>House ruler dasha periods</td>
<td>get_house_sign_and_ruling_planet_from_chart</td>
</tr>
<tr>
<td><code>get_planets_with_stars_of_planet</code></td>
<td>10062-10117</td>
<td><code>chart_data: dict, star_planet: str, chart_type: str</code></td>
<td><code>list</code></td>
<td>Nakshatra-based planet search</td>
<td>get_nakshatra_lord</td>
</tr>
<tr>
<td><code>parse_dasha_string</code></td>
<td>9339-9383</td>
<td><code>dasha_str: str</code></td>
<td><code>tuple</code></td>
<td>Parse MongoDB dasha format</td>
<td>String parsing</td>
</tr>
</tbody></table>

<h2>Age and Time Filtering</h2>

<table>
<thead><tr>
<th>Function Name</th>
<th>Line Range</th>
<th>Parameters</th>
<th>Return Type</th>
<th>Purpose</th>
<th>Dependencies</th>
</tr></thead><tbody>
<tr>
<td>---------------</td>
<td>------------</td>
<td>------------</td>
<td>-------------</td>
<td>---------</td>
<td>--------------</td>
</tr>
<tr>
<td><code>filter_dasha_periods_by_age</code></td>
<td>9803-9865</td>
<td><code>dasha_periods: list, chart_data: dict, min_age: int, max_age: int</code></td>
<td><code>list</code></td>
<td>Age-based filtering</td>
<td>calculate_age_during_period</td>
</tr>
<tr>
<td><code>filter_dasha_periods_within_prediction_window</code></td>
<td>10215-10258</td>
<td><code>dasha_periods: list, prediction_duration_years: int</code></td>
<td><code>list</code></td>
<td>Time-based filtering</td>
<td>Date calculations</td>
</tr>
<tr>
<td><code>calculate_age_during_period</code></td>
<td>9460-9491</td>
<td><code>birth_date: datetime, period_start_date: datetime</code></td>
<td><code>int</code></td>
<td>Age calculation</td>
<td>relativedelta</td>
</tr>
<tr>
<td><code>get_member_birth_date_from_chart_data</code></td>
<td>9426-9458</td>
<td><code>chart_data: dict</code></td>
<td><code>datetime</code></td>
<td>Birth date extraction</td>
<td>Chart data parsing</td>
</tr>
</tbody></table>

<h2>Validation and Utility Functions</h2>

<table>
<thead><tr>
<th>Function Name</th>
<th>Line Range</th>
<th>Parameters</th>
<th>Return Type</th>
<th>Purpose</th>
<th>Dependencies</th>
</tr></thead><tbody>
<tr>
<td>---------------</td>
<td>------------</td>
<td>------------</td>
<td>-------------</td>
<td>---------</td>
<td>--------------</td>
</tr>
<tr>
<td><code>validate_transit_with_chart</code></td>
<td>3516-3557</td>
<td><code>transit_date: str, planet_name: str, target_house_number: int, birth_place_data: dict</code></td>
<td><code>bool</code></td>
<td>Transit validation</td>
<td>generate_chart_for_date</td>
</tr>
<tr>
<td><code>generate_chart_for_date</code></td>
<td>2164-2215</td>
<td><code>date_str: str, birth_place_data: dict</code></td>
<td><code>dict</code></td>
<td>Chart generation with caching</td>
<td>Chart service API</td>
</tr>
<tr>
<td><code>get_birth_place_data_from_chart</code></td>
<td>2081-2162</td>
<td><code>chart_data: dict</code></td>
<td><code>dict</code></td>
<td>Birth location extraction</td>
<td>Chart data parsing</td>
</tr>
<tr>
<td><code>get_house_sign_and_ruling_planet_from_chart</code></td>
<td>5456-5536</td>
<td><code>chart_data: dict, house_number: int, chart_type: str</code></td>
<td><code>tuple</code></td>
<td>House information</td>
<td>Chart structure parsing</td>
</tr>
</tbody></table>

<h2>Response Creation Functions</h2>

<table>
<thead><tr>
<th>Function Name</th>
<th>Line Range</th>
<th>Parameters</th>
<th>Return Type</th>
<th>Purpose</th>
<th>Dependencies</th>
</tr></thead><tbody>
<tr>
<td>---------------</td>
<td>------------</td>
<td>------------</td>
<td>-------------</td>
<td>---------</td>
<td>--------------</td>
</tr>
<tr>
<td><code>create_clean_dasha_response</code></td>
<td>11620-11744</td>
<td><code>query, overall_result, filtered_dasha_dates, successful_conditions, results, prediction_duration, duration_details, user_profile_id, member_profile_id, age_constraints, kocharam_condition, kocharam_summary, age_filter_details, prediction_filter_details</code></td>
<td><code>dict</code></td>
<td>Main response formatter</td>
<td>Multiple formatting functions</td>
</tr>
<tr>
<td><code>create_enhanced_kocharam_result_new</code></td>
<td>3635-3751</td>
<td><code>planet_name, target_house_number, overlapping_transits, validation_result, dasha_period, enhanced_transit_objects, query_type, calculated_transit_date</code></td>
<td><code>dict</code></td>
<td>KOCHARAM result formatter</td>
<td>Transit detail formatting</td>
</tr>
<tr>
<td><code>format_dasha_periods_clean</code></td>
<td>12711-12762</td>
<td><code>dasha_dates: list</code></td>
<td><code>list</code></td>
<td>Clean dasha formatting</td>
<td>Period detail formatting</td>
</tr>
<tr>
<td><code>create_comprehensive_kocharam_summary</code></td>
<td>3559-3633</td>
<td><code>dasha_periods: list, kocharam_condition: str</code></td>
<td><code>list</code></td>
<td>KOCHARAM summary creation</td>
<td>Summary formatting</td>
</tr>
</tbody></table>

<h2>Database and Caching Functions</h2>

<table>
<thead><tr>
<th>Function Name</th>
<th>Line Range</th>
<th>Parameters</th>
<th>Return Type</th>
<th>Purpose</th>
<th>Dependencies</th>
</tr></thead><tbody>
<tr>
<td>---------------</td>
<td>------------</td>
<td>------------</td>
<td>-------------</td>
<td>---------</td>
<td>--------------</td>
</tr>
<tr>
<td><code>get_planetary_rotation_periods_cached</code></td>
<td>26-28</td>
<td>None</td>
<td><code>dict</code></td>
<td>Cached rotation periods</td>
<td>Database constants</td>
</tr>
<tr>
<td><code>get_planetary_aspect_houses_cached</code></td>
<td>32-34</td>
<td>None</td>
<td><code>dict</code></td>
<td>Cached aspect rules</td>
<td>Database constants</td>
</tr>
<tr>
<td><code>get_standardized_sign_names</code></td>
<td>1740-1745</td>
<td>None</td>
<td><code>list</code></td>
<td>Standard sign names</td>
<td>None</td>
</tr>
<tr>
<td><code>get_standardized_sign_map</code></td>
<td>1748-1755</td>
<td>None</td>
<td><code>dict</code></td>
<td>Sign to number mapping</td>
<td>None</td>
</tr>
</tbody></table>

<h2>Performance Metrics</h2>

<table>
<thead><tr>
<th>Category</th>
<th>Function Count</th>
<th>Average Processing Time</th>
<th>Memory Usage</th>
</tr></thead><tbody>
<tr>
<td>----------</td>
<td>----------------</td>
<td>------------------------</td>
<td>--------------</td>
</tr>
<tr>
<td>Entry Point</td>
<td>3</td>
<td>0.01-0.05 sec</td>
<td>1-2 MB</td>
</tr>
<tr>
<td>Query Processing</td>
<td>15</td>
<td>0.1-0.5 sec</td>
<td>2-10 MB</td>
</tr>
<tr>
<td>KOCHARAM Functions</td>
<td>25</td>
<td>0.5-2.0 sec</td>
<td>5-20 MB</td>
</tr>
<tr>
<td>Calculation Functions</td>
<td>20</td>
<td>0.001-0.1 sec</td>
<td>1-5 MB</td>
</tr>
<tr>
<td>Validation Functions</td>
<td>10</td>
<td>0.01-0.2 sec</td>
<td>1-3 MB</td>
</tr>
<tr>
<td><strong>Total Functions</strong></td>
<td><strong>212+</strong></td>
<td><strong>0.1-4.0 sec</strong></td>
<td><strong>2-35 MB</strong></td>
</tr>
</tbody></table>

<h2>Error Handling Coverage</h2>

<table>
<thead><tr>
<th>Function Category</th>
<th>Error Types Handled</th>
<th>Fallback Strategy</th>
</tr></thead><tbody>
<tr>
<td>------------------</td>
<td>-------------------</td>
<td>------------------</td>
</tr>
<tr>
<td>Entry Point</td>
<td>Validation, Authentication, Data Access</td>
<td>Detailed error responses</td>
</tr>
<tr>
<td>KOCHARAM Processing</td>
<td>Calculation, Validation, Timeout</td>
<td>Graceful degradation</td>
</tr>
<tr>
<td>Database Access</td>
<td>Connection, Query, Data Format</td>
<td>Retry with exponential backoff</td>
</tr>
<tr>
<td>Chart Generation</td>
<td>API Failure, Invalid Data</td>
<td>Cached fallback values</td>
</tr>
<tr>
<td>Age Calculations</td>
<td>Invalid Dates, Missing Data</td>
<td>Default age ranges</td>
</tr>
</tbody></table>
    
    <div class="print-footer">
        <p>Fortune Lens Rule Engine - Technical Documentation</p>
        <p>For best printing results: Use Chrome/Safari → Print → More Settings → Paper Size: A4 → Margins: Default</p>
    </div>
</body>
</html>