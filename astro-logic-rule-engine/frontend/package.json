{"name": "astro-logic-rule-engine-frontend", "version": "1.0.0", "description": "React frontend for AstroLogic Rule Engine - Standalone astrological query processing application", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "axios": "^1.5.0", "react-router-dom": "^6.15.0", "styled-components": "^6.0.7", "react-icons": "^4.11.0", "react-syntax-highlighter": "^15.5.0", "react-toastify": "^9.1.3", "lodash": "^4.17.21", "date-fns": "^2.30.0"}, "devDependencies": {"@types/react": "^18.2.22", "@types/react-dom": "^18.2.7", "@types/lodash": "^4.14.198", "eslint": "^8.49.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"no-unused-vars": "warn", "no-console": "warn", "react-hooks/exhaustive-deps": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000", "homepage": ".", "keywords": ["astrology", "rule-engine", "vedic-astrology", "planetary-relationships", "react", "frontend"], "author": "AstroLogic Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-repo/astro-logic-rule-engine.git"}, "bugs": {"url": "https://github.com/your-repo/astro-logic-rule-engine/issues"}}