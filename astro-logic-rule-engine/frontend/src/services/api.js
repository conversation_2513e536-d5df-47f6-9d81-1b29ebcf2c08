/**
 * AstroLogic Rule Engine - API Service
 * 
 * Handles all API communication with the backend Flask server.
 */

import axios from 'axios';
import { toast } from 'react-toastify';

// Create axios instance with default configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add timestamp to prevent caching
    config.params = {
      ...config.params,
      _t: Date.now()
    };
    
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', error);
    
    // Handle different error types
    if (error.code === 'ECONNABORTED') {
      toast.error('Request timeout. Please try again.');
    } else if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      const message = data?.error || data?.message || `Server error (${status})`;
      
      if (status >= 500) {
        toast.error('Server error. Please try again later.');
      } else if (status === 404) {
        toast.error('API endpoint not found.');
      } else {
        toast.error(message);
      }
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.');
    } else {
      toast.error('An unexpected error occurred.');
    }
    
    return Promise.reject(error);
  }
);

/**
 * Rule Engine API Service
 */
export const ruleEngineAPI = {
  /**
   * Evaluate an astrological query
   */
  evaluateQuery: async (queryData) => {
    try {
      const response = await api.post('/rule-engine/', queryData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || 'Failed to evaluate query');
    }
  },

  /**
   * Validate query syntax
   */
  validateQuery: async (query) => {
    try {
      const response = await api.post('/rule-engine/validate', { query });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || 'Failed to validate query');
    }
  },

  /**
   * Get query examples
   */
  getExamples: async () => {
    try {
      const response = await api.get('/rule-engine/examples');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || 'Failed to fetch examples');
    }
  },

  /**
   * Health check
   */
  healthCheck: async () => {
    try {
      const response = await api.get('/rule-engine/health');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || 'Health check failed');
    }
  }
};

/**
 * General API utilities
 */
export const apiUtils = {
  /**
   * Check if API is available
   */
  checkConnection: async () => {
    try {
      await ruleEngineAPI.healthCheck();
      return true;
    } catch (error) {
      return false;
    }
  },

  /**
   * Format API error for display
   */
  formatError: (error) => {
    if (error.response?.data?.error) {
      return error.response.data.error;
    }
    if (error.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  },

  /**
   * Retry API call with exponential backoff
   */
  retryCall: async (apiCall, maxRetries = 3, baseDelay = 1000) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        if (attempt === maxRetries) {
          throw error;
        }
        
        const delay = baseDelay * Math.pow(2, attempt - 1);
        console.log(`Retry attempt ${attempt} in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
};

export default api;
