/**
 * AstroLogic Rule Engine - Header Component
 * 
 * Navigation header with branding and main navigation links.
 */

import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { FiMenu, FiX, FiStar, FiBook, FiCode, FiInfo } from 'react-icons/fi';

const HeaderContainer = styled.header`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
`;

const HeaderContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Logo = styled(Link)`
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
  text-decoration: none;
  font-size: 1.5rem;
  font-weight: 700;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
    text-decoration: none;
  }
`;

const LogoIcon = styled(FiStar)`
  font-size: 2rem;
  color: #feca57;
  animation: rotate 20s linear infinite;
  
  @keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const Navigation = styled.nav`
  display: flex;
  gap: 2rem;
  align-items: center;
  
  @media (max-width: 768px) {
    display: ${props => props.isOpen ? 'flex' : 'none'};
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    flex-direction: column;
    padding: 2rem;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
`;

const NavLink = styled(Link)`
  color: white;
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    text-decoration: none;
    transform: translateY(-2px);
  }
  
  &.active {
    background: rgba(255, 255, 255, 0.3);
    
    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 50%;
      transform: translateX(-50%);
      width: 20px;
      height: 2px;
      background: #feca57;
      border-radius: 1px;
    }
  }
  
  @media (max-width: 768px) {
    color: #333;
    width: 100%;
    justify-content: center;
    
    &:hover {
      background: rgba(102, 126, 234, 0.1);
    }
    
    &.active {
      background: rgba(102, 126, 234, 0.2);
      color: #667eea;
    }
  }
`;

const MobileMenuButton = styled.button`
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
  
  @media (max-width: 768px) {
    display: block;
  }
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid rgba(34, 197, 94, 0.3);
  
  @media (max-width: 768px) {
    display: none;
  }
`;

const StatusDot = styled.div`
  width: 8px;
  height: 8px;
  background: #22c55e;
  border-radius: 50%;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
`;

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const isActive = (path) => {
    return location.pathname === path ? 'active' : '';
  };

  return (
    <HeaderContainer>
      <HeaderContent>
        <Logo to="/" onClick={closeMobileMenu}>
          <LogoIcon />
          AstroLogic
        </Logo>

        <Navigation isOpen={isMobileMenuOpen}>
          <NavLink 
            to="/" 
            className={isActive('/')}
            onClick={closeMobileMenu}
          >
            <FiStar />
            Home
          </NavLink>
          
          <NavLink 
            to="/query" 
            className={isActive('/query')}
            onClick={closeMobileMenu}
          >
            <FiCode />
            Query Builder
          </NavLink>
          
          <NavLink 
            to="/examples" 
            className={isActive('/examples')}
            onClick={closeMobileMenu}
          >
            <FiBook />
            Examples
          </NavLink>
          
          <NavLink 
            to="/docs" 
            className={isActive('/docs')}
            onClick={closeMobileMenu}
          >
            <FiBook />
            Documentation
          </NavLink>
          
          <NavLink 
            to="/about" 
            className={isActive('/about')}
            onClick={closeMobileMenu}
          >
            <FiInfo />
            About
          </NavLink>
        </Navigation>

        <StatusIndicator>
          <StatusDot />
          Online
        </StatusIndicator>

        <MobileMenuButton onClick={toggleMobileMenu}>
          {isMobileMenuOpen ? <FiX /> : <FiMenu />}
        </MobileMenuButton>
      </HeaderContent>
    </HeaderContainer>
  );
};

export default Header;
