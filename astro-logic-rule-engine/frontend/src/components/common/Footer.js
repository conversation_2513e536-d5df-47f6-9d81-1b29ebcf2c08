/**
 * AstroLogic Rule Engine - Footer Component
 * 
 * Application footer with links, information, and branding.
 */

import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { 
  FiStar, FiGithub, FiMail, FiHeart, FiCode, 
  FiBook, FiInfo, FiExternalLink 
} from 'react-icons/fi';

const FooterContainer = styled.footer`
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 2rem 0 1rem;
  margin-top: 4rem;
`;

const FooterContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
`;

const FooterGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
`;

const FooterSection = styled.div`
  h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #feca57;
  }
`;

const FooterLinks = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const FooterLink = styled(Link)`
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  
  &:hover {
    color: white;
    text-decoration: none;
    transform: translateX(4px);
  }
`;

const ExternalLink = styled.a`
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  
  &:hover {
    color: white;
    text-decoration: none;
    transform: translateX(4px);
  }
`;

const FooterDescription = styled.p`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: 1rem;
`;

const TechStack = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
`;

const TechBadge = styled.span`
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const FooterBottom = styled.div`
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
  }
`;

const Copyright = styled.div`
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 6px;
`;

const SocialLinks = styled.div`
  display: flex;
  gap: 1rem;
`;

const SocialLink = styled.a`
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.2rem;
  transition: all 0.3s ease;
  
  &:hover {
    color: #feca57;
    transform: translateY(-2px);
  }
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
`;

const StatusDot = styled.div`
  width: 8px;
  height: 8px;
  background: #22c55e;
  border-radius: 50%;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
`;

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <FooterContainer>
      <FooterContent>
        <FooterGrid>
          {/* About Section */}
          <FooterSection>
            <h3>
              <FiStar />
              AstroLogic Rule Engine
            </h3>
            <FooterDescription>
              Professional astrological query processing with comprehensive planetary 
              relationship analysis. Built with modern web technologies and traditional 
              Vedic astrology principles.
            </FooterDescription>
            <TechStack>
              <TechBadge>React</TechBadge>
              <TechBadge>Python Flask</TechBadge>
              <TechBadge>Vedic Astrology</TechBadge>
              <TechBadge>REST API</TechBadge>
            </TechStack>
          </FooterSection>

          {/* Navigation Links */}
          <FooterSection>
            <h3>
              <FiBook />
              Navigation
            </h3>
            <FooterLinks>
              <FooterLink to="/">
                <FiStar />
                Home
              </FooterLink>
              <FooterLink to="/query">
                <FiCode />
                Query Builder
              </FooterLink>
              <FooterLink to="/examples">
                <FiBook />
                Examples
              </FooterLink>
              <FooterLink to="/docs">
                <FiBook />
                Documentation
              </FooterLink>
              <FooterLink to="/about">
                <FiInfo />
                About
              </FooterLink>
            </FooterLinks>
          </FooterSection>

          {/* Resources */}
          <FooterSection>
            <h3>
              <FiCode />
              Resources
            </h3>
            <FooterLinks>
              <ExternalLink 
                href="https://github.com/your-repo/astro-logic-rule-engine" 
                target="_blank" 
                rel="noopener noreferrer"
              >
                <FiGithub />
                Source Code
                <FiExternalLink />
              </ExternalLink>
              <ExternalLink 
                href="/api/rule-engine/examples" 
                target="_blank"
              >
                <FiCode />
                API Examples
                <FiExternalLink />
              </ExternalLink>
              <ExternalLink 
                href="/api/rule-engine/health" 
                target="_blank"
              >
                <FiInfo />
                API Health
                <FiExternalLink />
              </ExternalLink>
              <ExternalLink 
                href="mailto:<EMAIL>" 
              >
                <FiMail />
                Support
              </ExternalLink>
            </FooterLinks>
          </FooterSection>

          {/* Features */}
          <FooterSection>
            <h3>
              <FiStar />
              Features
            </h3>
            <FooterLinks>
              <div style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '0.9rem' }}>
                ✓ 10 Query Patterns<br />
                ✓ Logical Operators (OR, AND, NOT)<br />
                ✓ 5-Rule Relationship System<br />
                ✓ Planetary Aspects Analysis<br />
                ✓ Nakshatra Relationships<br />
                ✓ Real-time Validation<br />
                ✓ Professional UI/UX<br />
                ✓ Comprehensive Documentation
              </div>
            </FooterLinks>
          </FooterSection>
        </FooterGrid>

        <FooterBottom>
          <Copyright>
            <FiHeart style={{ color: '#ef4444' }} />
            © {currentYear} AstroLogic Rule Engine. Made with passion for astrology.
          </Copyright>

          <StatusIndicator>
            <StatusDot />
            System Online
          </StatusIndicator>

          <SocialLinks>
            <SocialLink 
              href="https://github.com/your-repo/astro-logic-rule-engine" 
              target="_blank" 
              rel="noopener noreferrer"
              title="GitHub Repository"
            >
              <FiGithub />
            </SocialLink>
            <SocialLink 
              href="mailto:<EMAIL>"
              title="Contact Support"
            >
              <FiMail />
            </SocialLink>
          </SocialLinks>
        </FooterBottom>
      </FooterContent>
    </FooterContainer>
  );
};

export default Footer;
