/**
 * AstroLogic Rule Engine - Error Boundary Component
 * 
 * React Error Boundary to catch and handle JavaScript errors gracefully.
 */

import React from 'react';
import styled from 'styled-components';
import { FiAlertTriangle, FiRefreshCw, FiHome } from 'react-icons/fi';

const ErrorContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

const ErrorCard = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 3rem;
  max-width: 600px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
`;

const ErrorIcon = styled(FiAlertTriangle)`
  font-size: 4rem;
  color: #ef4444;
  margin-bottom: 1.5rem;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }
`;

const ErrorTitle = styled.h1`
  color: #1f2937;
  font-size: 2rem;
  margin-bottom: 1rem;
  font-weight: 700;
`;

const ErrorMessage = styled.p`
  color: #6b7280;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
`;

const ErrorDetails = styled.details`
  text-align: left;
  background: #f9fafb;
  border-radius: 8px;
  padding: 1rem;
  margin: 1.5rem 0;
  border: 1px solid #e5e7eb;
`;

const ErrorSummary = styled.summary`
  cursor: pointer;
  font-weight: 600;
  color: #374151;
  padding: 0.5rem;
  
  &:hover {
    color: #1f2937;
  }
`;

const ErrorStack = styled.pre`
  background: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 0.875rem;
  margin-top: 1rem;
  white-space: pre-wrap;
  word-break: break-word;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
`;

const ActionButton = styled.button`
  background: ${props => props.primary ? 'linear-gradient(45deg, #667eea, #764ba2)' : 'transparent'};
  color: ${props => props.primary ? 'white' : '#667eea'};
  border: 2px solid #667eea;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    background: ${props => props.primary ? 'linear-gradient(45deg, #5a67d8, #6b46c1)' : 'rgba(102, 126, 234, 0.1)'};
  }
  
  &:active {
    transform: translateY(0);
  }
`;

const ContactInfo = styled.div`
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
  color: #6b7280;
  font-size: 0.9rem;
`;

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null 
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // In production, you might want to log this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: logErrorToService(error, errorInfo);
    }
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo } = this.state;
      
      return (
        <ErrorContainer>
          <ErrorCard>
            <ErrorIcon />
            <ErrorTitle>Oops! Something went wrong</ErrorTitle>
            <ErrorMessage>
              We encountered an unexpected error in the AstroLogic Rule Engine. 
              Don't worry - this has been logged and our team will investigate.
            </ErrorMessage>

            {process.env.NODE_ENV === 'development' && error && (
              <ErrorDetails>
                <ErrorSummary>Technical Details (Development Mode)</ErrorSummary>
                <div>
                  <strong>Error:</strong> {error.toString()}
                  {errorInfo && (
                    <ErrorStack>
                      {errorInfo.componentStack}
                    </ErrorStack>
                  )}
                </div>
              </ErrorDetails>
            )}

            <ButtonGroup>
              <ActionButton primary onClick={this.handleReload}>
                <FiRefreshCw />
                Reload Page
              </ActionButton>
              <ActionButton onClick={this.handleGoHome}>
                <FiHome />
                Go Home
              </ActionButton>
            </ButtonGroup>

            <ContactInfo>
              <p>
                If this problem persists, please contact our support team with the error details above.
              </p>
              <p>
                <strong>Error ID:</strong> {Date.now().toString(36)}
              </p>
            </ContactInfo>
          </ErrorCard>
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
