/**
 * AstroLogic Rule Engine - Query Input Component
 * 
 * Advanced text input for astrological queries with syntax highlighting and validation.
 */

import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { FiAlertCircle, FiCheck, FiInfo } from 'react-icons/fi';

const InputContainer = styled.div`
  position: relative;
  margin-bottom: 1rem;
`;

const InputWrapper = styled.div`
  position: relative;
  border: 2px solid ${props => {
    if (props.validationStatus === 'success') return '#10b981';
    if (props.validationStatus === 'error') return '#ef4444';
    return '#d1d5db';
  }};
  border-radius: 8px;
  background: white;
  transition: all 0.3s ease;
  
  &:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 120px;
  padding: 1rem;
  border: none;
  border-radius: 6px;
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  font-size: 1rem;
  line-height: 1.5;
  resize: vertical;
  background: transparent;
  color: #1f2937;
  
  &:focus {
    outline: none;
  }
  
  &::placeholder {
    color: #9ca3af;
    font-style: italic;
  }
`;

const ValidationPanel = styled.div`
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 8px;
  background: ${props => {
    if (props.status === 'success') return 'rgba(16, 185, 129, 0.05)';
    if (props.status === 'error') return 'rgba(239, 68, 68, 0.05)';
    return 'rgba(59, 130, 246, 0.05)';
  }};
  border: 1px solid ${props => {
    if (props.status === 'success') return 'rgba(16, 185, 129, 0.2)';
    if (props.status === 'error') return 'rgba(239, 68, 68, 0.2)';
    return 'rgba(59, 130, 246, 0.2)';
  }};
`;

const ValidationHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: ${props => {
    if (props.status === 'success') return '#059669';
    if (props.status === 'error') return '#dc2626';
    return '#2563eb';
  }};
`;

const ValidationList = styled.ul`
  margin: 0;
  padding-left: 1.5rem;
  
  li {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
    color: #6b7280;
  }
`;

const ParsedConditions = styled.div`
  margin-top: 1rem;
`;

const ConditionTag = styled.span`
  display: inline-block;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  margin: 2px;
  border: 1px solid rgba(102, 126, 234, 0.2);
`;

const CharacterCount = styled.div`
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 0.75rem;
  color: #9ca3af;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 4px;
`;

const SyntaxHelp = styled.div`
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: #6b7280;
  
  .syntax-example {
    font-family: 'JetBrains Mono', monospace;
    background: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
    margin: 0 2px;
  }
`;

const QueryInput = ({ value, onChange, onValidate, validationStatus }) => {
  const textareaRef = useRef(null);
  const [isFocused, setIsFocused] = useState(false);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.max(120, textarea.scrollHeight) + 'px';
    }
  }, [value]);

  const handleChange = (e) => {
    onChange(e.target.value);
  };

  const handleKeyDown = (e) => {
    // Handle Tab key for indentation
    if (e.key === 'Tab') {
      e.preventDefault();
      const start = e.target.selectionStart;
      const end = e.target.selectionEnd;
      const newValue = value.substring(0, start) + '  ' + value.substring(end);
      onChange(newValue);
      
      // Set cursor position after the inserted spaces
      setTimeout(() => {
        e.target.selectionStart = e.target.selectionEnd = start + 2;
      }, 0);
    }
    
    // Handle Ctrl+Enter for quick validation
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      onValidate();
    }
  };

  const getValidationStatusType = () => {
    if (!validationStatus) return null;
    return validationStatus.validation?.is_valid ? 'success' : 'error';
  };

  const renderValidationResults = () => {
    if (!validationStatus) return null;

    const { validation } = validationStatus;
    const status = validation?.is_valid ? 'success' : 'error';

    return (
      <ValidationPanel status={status}>
        <ValidationHeader status={status}>
          {status === 'success' ? <FiCheck /> : <FiAlertCircle />}
          {status === 'success' ? 'Query is valid' : 'Validation Issues'}
        </ValidationHeader>

        {validation?.errors && validation.errors.length > 0 && (
          <div>
            <strong>Errors:</strong>
            <ValidationList>
              {validation.errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ValidationList>
          </div>
        )}

        {validation?.warnings && validation.warnings.length > 0 && (
          <div>
            <strong>Warnings:</strong>
            <ValidationList>
              {validation.warnings.map((warning, index) => (
                <li key={index}>{warning}</li>
              ))}
            </ValidationList>
          </div>
        )}

        {validation?.parsed_conditions && validation.parsed_conditions.length > 0 && (
          <ParsedConditions>
            <strong>Parsed Conditions:</strong>
            <div style={{ marginTop: '0.5rem' }}>
              {validation.parsed_conditions.map((condition, index) => (
                <ConditionTag key={index}>
                  {condition.type}: {condition.condition}
                </ConditionTag>
              ))}
            </div>
          </ParsedConditions>
        )}
      </ValidationPanel>
    );
  };

  return (
    <InputContainer>
      <InputWrapper validationStatus={getValidationStatusType()}>
        <TextArea
          ref={textareaRef}
          value={value}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder="Enter your astrological query here...
Example: JUPITER IN 7 OR VENUS WITH_RULING_PLANET 7th_House_Ruling_Planet"
          spellCheck={false}
        />
        
        <CharacterCount>
          {value.length} / 1000
        </CharacterCount>
      </InputWrapper>

      {isFocused && (
        <SyntaxHelp>
          <div style={{ marginBottom: '0.5rem' }}>
            <FiInfo style={{ marginRight: '4px' }} />
            <strong>Quick Tips:</strong>
          </div>
          <div>
            • Use <span className="syntax-example">PLANET IN HOUSE</span> for basic conditions
          </div>
          <div>
            • Combine with <span className="syntax-example">OR</span>, <span className="syntax-example">AND</span>, <span className="syntax-example">NOT</span>
          </div>
          <div>
            • Press <span className="syntax-example">Ctrl+Enter</span> to validate
          </div>
        </SyntaxHelp>
      )}

      {renderValidationResults()}
    </InputContainer>
  );
};

export default QueryInput;
