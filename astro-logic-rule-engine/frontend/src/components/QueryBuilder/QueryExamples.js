/**
 * AstroLogic Rule Engine - Query Examples Component
 * 
 * Interactive examples panel with categorized query samples.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { FiCopy, FiPlay, FiChevronDown, FiChevronRight } from 'react-icons/fi';
import { toast } from 'react-toastify';

const ExamplesContainer = styled.div`
  max-height: 400px;
  overflow-y: auto;
`;

const CategorySection = styled.div`
  margin-bottom: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
`;

const CategoryHeader = styled.div`
  background: #f9fafb;
  padding: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
  
  &:hover {
    background: #f3f4f6;
  }
`;

const CategoryContent = styled.div`
  display: ${props => props.isOpen ? 'block' : 'none'};
`;

const ExampleItem = styled.div`
  padding: 0.75rem;
  border-bottom: 1px solid #f3f4f6;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: #f9fafb;
  }
`;

const ExampleQuery = styled.div`
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.8rem;
  background: rgba(0, 0, 0, 0.05);
  padding: 0.5rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  word-break: break-all;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(102, 126, 234, 0.1);
  }
`;

const ExampleDescription = styled.div`
  font-size: 0.8rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
`;

const ExampleActions = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const ActionButton = styled.button`
  background: none;
  border: 1px solid #d1d5db;
  color: #6b7280;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  
  &:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
  }
`;

const CategoryBadge = styled.span`
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 500;
`;

const QueryExamples = ({ examples, onSelectExample }) => {
  const [openCategories, setOpenCategories] = useState({
    basic_queries: true,
    logical_operators: false,
    relationship_queries: false,
    nakshatra_queries: false,
    complex_queries: false
  });

  if (!examples || !examples.examples) {
    return (
      <div style={{ textAlign: 'center', color: '#6b7280', padding: '1rem' }}>
        Loading examples...
      </div>
    );
  }

  const toggleCategory = (category) => {
    setOpenCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  const handleCopyQuery = (query) => {
    navigator.clipboard.writeText(query);
    toast.success('Query copied to clipboard!');
  };

  const handleSelectQuery = (query) => {
    onSelectExample(query);
    toast.info('Query loaded into editor');
  };

  const categoryLabels = {
    basic_queries: 'Basic Planet-House',
    logical_operators: 'Logical Operators',
    relationship_queries: 'Relationships',
    nakshatra_queries: 'Nakshatra Queries',
    complex_queries: 'Complex Queries'
  };

  const categoryDescriptions = {
    basic_queries: 'Simple planet position queries',
    logical_operators: 'Queries with OR, AND, NOT logic',
    relationship_queries: 'Planetary relationship analysis',
    nakshatra_queries: 'Star constellation based queries',
    complex_queries: 'Advanced multi-condition queries'
  };

  return (
    <ExamplesContainer>
      {Object.entries(examples.examples).map(([categoryKey, categoryExamples]) => (
        <CategorySection key={categoryKey}>
          <CategoryHeader onClick={() => toggleCategory(categoryKey)}>
            <div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {categoryLabels[categoryKey] || categoryKey}
                <CategoryBadge>{categoryExamples.length}</CategoryBadge>
              </div>
              <div style={{ fontSize: '0.75rem', color: '#9ca3af', marginTop: '2px' }}>
                {categoryDescriptions[categoryKey]}
              </div>
            </div>
            {openCategories[categoryKey] ? <FiChevronDown /> : <FiChevronRight />}
          </CategoryHeader>
          
          <CategoryContent isOpen={openCategories[categoryKey]}>
            {categoryExamples.map((example, index) => (
              <ExampleItem key={index}>
                <ExampleQuery onClick={() => handleSelectQuery(example.query)}>
                  {example.query}
                </ExampleQuery>
                
                <ExampleDescription>
                  {example.description}
                </ExampleDescription>
                
                <ExampleActions>
                  <ActionButton onClick={() => handleSelectQuery(example.query)}>
                    <FiPlay />
                    Use
                  </ActionButton>
                  <ActionButton onClick={() => handleCopyQuery(example.query)}>
                    <FiCopy />
                    Copy
                  </ActionButton>
                </ExampleActions>
              </ExampleItem>
            ))}
          </CategoryContent>
        </CategorySection>
      ))}
      
      {/* Usage Notes */}
      <div style={{ 
        marginTop: '1rem', 
        padding: '0.75rem', 
        background: 'rgba(59, 130, 246, 0.05)',
        border: '1px solid rgba(59, 130, 246, 0.1)',
        borderRadius: '6px',
        fontSize: '0.8rem',
        color: '#1e40af'
      }}>
        <strong>Usage Notes:</strong>
        <ul style={{ margin: '0.5rem 0 0 1rem', padding: 0 }}>
          <li>Click on any query to load it into the editor</li>
          <li>Use the Copy button to copy queries to clipboard</li>
          <li>Modify examples to create your own queries</li>
          <li>Combine multiple conditions with logical operators</li>
        </ul>
      </div>
      
      {/* Supported Features */}
      {examples.supported_operators && (
        <div style={{ 
          marginTop: '1rem', 
          padding: '0.75rem', 
          background: 'rgba(16, 185, 129, 0.05)',
          border: '1px solid rgba(16, 185, 129, 0.1)',
          borderRadius: '6px',
          fontSize: '0.8rem',
          color: '#059669'
        }}>
          <strong>Supported Features:</strong>
          <div style={{ marginTop: '0.5rem' }}>
            <div><strong>Operators:</strong> {examples.supported_operators.join(', ')}</div>
            <div><strong>Relationships:</strong> {examples.supported_relationships.join(', ')}</div>
          </div>
        </div>
      )}
    </ExamplesContainer>
  );
};

export default QueryExamples;
