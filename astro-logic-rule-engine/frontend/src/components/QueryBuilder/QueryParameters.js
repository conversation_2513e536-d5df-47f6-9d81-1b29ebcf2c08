/**
 * AstroLogic Rule Engine - Query Parameters Component
 * 
 * Configuration panel for query execution parameters.
 */

import React from 'react';
import styled from 'styled-components';
import { FiUser, FiUsers, FiLayers } from 'react-icons/fi';

const ParametersContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
`;

const ParameterGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
`;

const Input = styled.input`
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: white;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
  
  &:invalid {
    border-color: #ef4444;
  }
`;

const Select = styled.select`
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: white;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`;

const HelpText = styled.div`
  font-size: 0.8rem;
  color: #6b7280;
  margin-top: 0.25rem;
`;

const ParameterInfo = styled.div`
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
  font-size: 0.85rem;
  color: #1e40af;
`;

const QueryParameters = ({ parameters, onChange }) => {
  const handleParameterChange = (key, value) => {
    onChange({
      ...parameters,
      [key]: value
    });
  };

  const chartTypes = [
    { value: 'D1', label: 'D1 - Rasi Chart (Main Birth Chart)' },
    { value: 'D2', label: 'D2 - Hora Chart' },
    { value: 'D3', label: 'D3 - Drekkana Chart' },
    { value: 'D4', label: 'D4 - Chaturthamsa Chart' },
    { value: 'D7', label: 'D7 - Saptamsa Chart' },
    { value: 'D9', label: 'D9 - Navamsa Chart' },
    { value: 'D10', label: 'D10 - Dasamsa Chart' },
    { value: 'D12', label: 'D12 - Dwadasamsa Chart' },
    { value: 'D16', label: 'D16 - Shodasamsa Chart' },
    { value: 'D20', label: 'D20 - Vimsamsa Chart' },
    { value: 'D24', label: 'D24 - Chaturvimsamsa Chart' },
    { value: 'D27', label: 'D27 - Nakshatramsa Chart' },
    { value: 'D30', label: 'D30 - Trimsamsa Chart' }
  ];

  const userProfiles = [
    { value: '1', label: 'User Profile 1' },
    { value: '2', label: 'User Profile 2' },
    { value: '3', label: 'User Profile 3' }
  ];

  const memberProfiles = [
    { value: '1', label: 'Member 1 (Self)' },
    { value: '2', label: 'Member 2' },
    { value: '3', label: 'Member 3' }
  ];

  return (
    <>
      <ParametersContainer>
        <ParameterGroup>
          <Label>
            <FiUser />
            User Profile ID
          </Label>
          <Select
            value={parameters.user_profile_id}
            onChange={(e) => handleParameterChange('user_profile_id', e.target.value)}
          >
            {userProfiles.map(profile => (
              <option key={profile.value} value={profile.value}>
                {profile.label}
              </option>
            ))}
          </Select>
          <HelpText>
            Select the user profile for chart analysis
          </HelpText>
        </ParameterGroup>

        <ParameterGroup>
          <Label>
            <FiUsers />
            Member Profile ID
          </Label>
          <Select
            value={parameters.member_profile_id}
            onChange={(e) => handleParameterChange('member_profile_id', e.target.value)}
          >
            {memberProfiles.map(member => (
              <option key={member.value} value={member.value}>
                {member.label}
              </option>
            ))}
          </Select>
          <HelpText>
            Select the member whose chart to analyze
          </HelpText>
        </ParameterGroup>

        <ParameterGroup>
          <Label>
            <FiLayers />
            Chart Type
          </Label>
          <Select
            value={parameters.chart_type}
            onChange={(e) => handleParameterChange('chart_type', e.target.value)}
          >
            {chartTypes.map(chart => (
              <option key={chart.value} value={chart.value}>
                {chart.label}
              </option>
            ))}
          </Select>
          <HelpText>
            Select the divisional chart for analysis
          </HelpText>
        </ParameterGroup>
      </ParametersContainer>

      <ParameterInfo>
        <strong>Parameter Information:</strong>
        <ul style={{ margin: '0.5rem 0 0 1rem', padding: 0 }}>
          <li>
            <strong>User Profile:</strong> Identifies the user account in the system
          </li>
          <li>
            <strong>Member Profile:</strong> Specifies which family member's chart to analyze
          </li>
          <li>
            <strong>Chart Type:</strong> Determines which divisional chart to use for calculations
          </li>
        </ul>
        
        <div style={{ marginTop: '0.75rem', fontSize: '0.8rem' }}>
          <strong>Note:</strong> This demo uses sample chart data. In a production system, 
          these parameters would reference actual user birth charts stored in the database.
        </div>
      </ParameterInfo>
    </>
  );
};

export default QueryParameters;
