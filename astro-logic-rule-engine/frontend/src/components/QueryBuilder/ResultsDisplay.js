/**
 * AstroLogic Rule Engine - Results Display Component
 * 
 * Comprehensive display of query evaluation results with detailed analysis.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  FiCheck, FiX, FiInfo, FiTrendingUp, FiClock, 
  FiChevronDown, FiChevronRight, FiStar, FiTarget 
} from 'react-icons/fi';

const ResultsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const ResultHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-radius: 8px;
  background: ${props => props.success ? 
    'linear-gradient(45deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1))' : 
    'linear-gradient(45deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1))'
  };
  border: 1px solid ${props => props.success ? 
    'rgba(16, 185, 129, 0.2)' : 
    'rgba(239, 68, 68, 0.2)'
  };
`;

const ResultStatus = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  color: ${props => props.success ? '#059669' : '#dc2626'};
`;

const ResultIcon = styled.div`
  font-size: 1.5rem;
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const MetricCard = styled.div`
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
`;

const MetricValue = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.25rem;
`;

const MetricLabel = styled.div`
  font-size: 0.85rem;
  color: #6b7280;
  font-weight: 500;
`;

const Section = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
`;

const SectionHeader = styled.div`
  background: #f9fafb;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  color: #374151;
  
  &:hover {
    background: #f3f4f6;
  }
`;

const SectionContent = styled.div`
  padding: 1rem;
  display: ${props => props.isOpen ? 'block' : 'none'};
`;

const ConditionsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const ConditionItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 0.75rem;
  border-radius: 6px;
  background: ${props => props.success ? 
    'rgba(16, 185, 129, 0.05)' : 
    'rgba(239, 68, 68, 0.05)'
  };
  border: 1px solid ${props => props.success ? 
    'rgba(16, 185, 129, 0.1)' : 
    'rgba(239, 68, 68, 0.1)'
  };
`;

const ConditionIcon = styled.div`
  color: ${props => props.success ? '#059669' : '#dc2626'};
  font-size: 1.1rem;
  margin-top: 2px;
`;

const ConditionContent = styled.div`
  flex: 1;
`;

const ConditionText = styled.div`
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
`;

const ConditionDetails = styled.div`
  font-size: 0.85rem;
  color: #6b7280;
`;

const ErrorMessage = styled.div`
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 8px;
  padding: 1rem;
  color: #dc2626;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ProcessingTime = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
  color: #6b7280;
`;

const ResultsDisplay = ({ results }) => {
  const [openSections, setOpenSections] = useState({
    analysis: true,
    conditions: true,
    details: false
  });

  const toggleSection = (section) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  if (!results) {
    return (
      <div style={{ textAlign: 'center', color: '#6b7280', padding: '2rem' }}>
        No results to display. Execute a query to see results here.
      </div>
    );
  }

  if (!results.success) {
    return (
      <ErrorMessage>
        <FiX />
        <div>
          <strong>Query Failed:</strong> {results.error || 'Unknown error occurred'}
          {results.details && (
            <div style={{ marginTop: '0.5rem', fontSize: '0.8rem' }}>
              {Array.isArray(results.details) ? results.details.join(', ') : results.details}
            </div>
          )}
        </div>
      </ErrorMessage>
    );
  }

  const { query_analysis, detailed_results, metadata } = results;

  return (
    <ResultsContainer>
      {/* Main Result Header */}
      <ResultHeader success={results.result}>
        <ResultStatus success={results.result}>
          <ResultIcon>
            {results.result ? <FiCheck /> : <FiX />}
          </ResultIcon>
          <div>
            <div>Query Result: {results.result ? 'TRUE' : 'FALSE'}</div>
            <div style={{ fontSize: '0.9rem', fontWeight: 'normal', opacity: 0.8 }}>
              {results.success_rating} ({results.success_rate}%)
            </div>
          </div>
        </ResultStatus>
        
        {metadata?.processing_time_ms && (
          <ProcessingTime>
            <FiClock />
            {metadata.processing_time_ms}ms
          </ProcessingTime>
        )}
      </ResultHeader>

      {/* Metrics Overview */}
      {query_analysis && (
        <MetricsGrid>
          <MetricCard>
            <MetricValue>{query_analysis.total_conditions}</MetricValue>
            <MetricLabel>Total Conditions</MetricLabel>
          </MetricCard>
          
          <MetricCard>
            <MetricValue>{query_analysis.successful_conditions}</MetricValue>
            <MetricLabel>Successful</MetricLabel>
          </MetricCard>
          
          <MetricCard>
            <MetricValue>{results.success_rate}%</MetricValue>
            <MetricLabel>Success Rate</MetricLabel>
          </MetricCard>
          
          <MetricCard>
            <MetricValue>{results.success_rating}</MetricValue>
            <MetricLabel>Rating</MetricLabel>
          </MetricCard>
        </MetricsGrid>
      )}

      {/* Query Analysis Section */}
      {query_analysis && (
        <Section>
          <SectionHeader onClick={() => toggleSection('analysis')}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <FiTrendingUp />
              Query Analysis
            </div>
            {openSections.analysis ? <FiChevronDown /> : <FiChevronRight />}
          </SectionHeader>
          
          <SectionContent isOpen={openSections.analysis}>
            <div style={{ display: 'grid', gap: '1rem' }}>
              {query_analysis.logical_structure && (
                <div>
                  <strong>Logical Structure:</strong>
                  <div style={{ marginTop: '0.5rem', fontSize: '0.9rem', color: '#6b7280' }}>
                    Complexity: {query_analysis.logical_structure.complexity || 'Simple'}
                    {query_analysis.logical_structure.operators_used?.length > 0 && (
                      <div>
                        Operators: {query_analysis.logical_structure.operators_used.join(', ')}
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              {query_analysis.condition_breakdown && (
                <div>
                  <strong>Condition Breakdown:</strong>
                  <div style={{ marginTop: '0.5rem', fontSize: '0.9rem' }}>
                    {Object.entries(query_analysis.condition_breakdown.by_type || {}).map(([type, stats]) => (
                      <div key={type} style={{ color: '#6b7280' }}>
                        {type}: {stats.successful}/{stats.total} successful
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </SectionContent>
        </Section>
      )}

      {/* Individual Conditions Section */}
      {detailed_results?.individual_conditions && (
        <Section>
          <SectionHeader onClick={() => toggleSection('conditions')}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <FiTarget />
              Individual Conditions ({detailed_results.individual_conditions.length})
            </div>
            {openSections.conditions ? <FiChevronDown /> : <FiChevronRight />}
          </SectionHeader>
          
          <SectionContent isOpen={openSections.conditions}>
            <ConditionsList>
              {detailed_results.individual_conditions.map((condition, index) => (
                <ConditionItem key={index} success={condition.success}>
                  <ConditionIcon success={condition.success}>
                    {condition.success ? <FiCheck /> : <FiX />}
                  </ConditionIcon>
                  
                  <ConditionContent>
                    <ConditionText>{condition.condition}</ConditionText>
                    <ConditionDetails>
                      Type: {condition.type}
                      {condition.relationship_type && ` | Relationship: ${condition.relationship_type}`}
                      {condition.error && (
                        <div style={{ color: '#dc2626', marginTop: '0.25rem' }}>
                          Error: {condition.error}
                        </div>
                      )}
                      {condition.details && (
                        <div style={{ marginTop: '0.25rem' }}>
                          {typeof condition.details === 'string' ? 
                            condition.details : 
                            JSON.stringify(condition.details, null, 2)
                          }
                        </div>
                      )}
                    </ConditionDetails>
                  </ConditionContent>
                </ConditionItem>
              ))}
            </ConditionsList>
          </SectionContent>
        </Section>
      )}

      {/* Detailed Results Section */}
      {detailed_results && (
        <Section>
          <SectionHeader onClick={() => toggleSection('details')}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <FiInfo />
              Technical Details
            </div>
            {openSections.details ? <FiChevronDown /> : <FiChevronRight />}
          </SectionHeader>
          
          <SectionContent isOpen={openSections.details}>
            <div style={{ fontSize: '0.85rem' }}>
              <div style={{ marginBottom: '1rem' }}>
                <strong>Chart Information:</strong>
                <div style={{ marginTop: '0.5rem', color: '#6b7280' }}>
                  Chart Type: {results.chart_type}
                  {detailed_results.chart_summary && (
                    <div>
                      Data Source: {detailed_results.chart_summary.data_source || 'Unknown'}
                    </div>
                  )}
                </div>
              </div>
              
              {detailed_results.evaluation_summary && (
                <div style={{ marginBottom: '1rem' }}>
                  <strong>Evaluation Summary:</strong>
                  <pre style={{ 
                    background: '#f9fafb', 
                    padding: '0.75rem', 
                    borderRadius: '4px',
                    marginTop: '0.5rem',
                    fontSize: '0.8rem',
                    overflow: 'auto'
                  }}>
                    {JSON.stringify(detailed_results.evaluation_summary, null, 2)}
                  </pre>
                </div>
              )}
              
              {metadata && (
                <div>
                  <strong>Metadata:</strong>
                  <div style={{ marginTop: '0.5rem', color: '#6b7280' }}>
                    API Version: {metadata.api_version}
                    <br />
                    Engine Version: {metadata.engine_version}
                    <br />
                    Processing Time: {metadata.processing_time_ms}ms
                  </div>
                </div>
              )}
            </div>
          </SectionContent>
        </Section>
      )}
    </ResultsContainer>
  );
};

export default ResultsDisplay;
