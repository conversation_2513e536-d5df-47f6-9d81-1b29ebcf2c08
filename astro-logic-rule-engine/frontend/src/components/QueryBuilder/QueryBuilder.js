/**
 * AstroLogic Rule Engine - Query Builder Component
 * 
 * Main interface for building and executing astrological queries.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { 
  FiPlay, FiCheck, FiAlertCircle, FiCode, FiSettings, 
  FiHelpCircle, FiCopy, FiDownload 
} from 'react-icons/fi';
import { ruleEngineAPI } from '../../services/api';
import QueryInput from './QueryInput';
import QueryParameters from './QueryParameters';
import ResultsDisplay from './ResultsDisplay';
import QueryExamples from './QueryExamples';

const QueryBuilderContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 0;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;
  color: white;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #feca57, #ff9ff3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;

const Subtitle = styled.p`
  font-size: 1.1rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
`;

const MainContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const QuerySection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const Card = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const CardHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: between;
  margin-bottom: 1rem;
  
  h3 {
    margin: 0;
    color: #1f2937;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
`;

const ActionButton = styled.button`
  background: ${props => {
    if (props.primary) return 'linear-gradient(45deg, #667eea, #764ba2)';
    if (props.success) return 'linear-gradient(45deg, #10b981, #059669)';
    if (props.warning) return 'linear-gradient(45deg, #f59e0b, #d97706)';
    return 'rgba(107, 114, 128, 0.1)';
  }};
  color: ${props => props.primary || props.success || props.warning ? 'white' : '#374151'};
  border: ${props => props.primary || props.success || props.warning ? 'none' : '1px solid #d1d5db'};
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  background: ${props => {
    if (props.status === 'success') return 'rgba(16, 185, 129, 0.1)';
    if (props.status === 'error') return 'rgba(239, 68, 68, 0.1)';
    if (props.status === 'loading') return 'rgba(59, 130, 246, 0.1)';
    return 'rgba(107, 114, 128, 0.1)';
  }};
  color: ${props => {
    if (props.status === 'success') return '#059669';
    if (props.status === 'error') return '#dc2626';
    if (props.status === 'loading') return '#2563eb';
    return '#6b7280';
  }};
  border: 1px solid ${props => {
    if (props.status === 'success') return 'rgba(16, 185, 129, 0.2)';
    if (props.status === 'error') return 'rgba(239, 68, 68, 0.2)';
    if (props.status === 'loading') return 'rgba(59, 130, 246, 0.2)';
    return 'rgba(107, 114, 128, 0.2)';
  }};
`;

const Sidebar = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const QueryBuilder = () => {
  const [query, setQuery] = useState('JUPITER IN 7 OR VENUS WITH_RULING_PLANET 7th_House_Ruling_Planet');
  const [parameters, setParameters] = useState({
    user_profile_id: '1',
    member_profile_id: '1',
    chart_type: 'D1'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [validationStatus, setValidationStatus] = useState(null);
  const [examples, setExamples] = useState(null);

  // Load examples on component mount
  useEffect(() => {
    loadExamples();
  }, []);

  const loadExamples = async () => {
    try {
      const examplesData = await ruleEngineAPI.getExamples();
      setExamples(examplesData);
    } catch (error) {
      console.error('Failed to load examples:', error);
    }
  };

  const handleExecuteQuery = async () => {
    if (!query.trim()) {
      toast.error('Please enter a query');
      return;
    }

    setIsLoading(true);
    setResults(null);

    try {
      const queryData = {
        query: query.trim(),
        ...parameters
      };

      const result = await ruleEngineAPI.evaluateQuery(queryData);
      setResults(result);
      
      if (result.success) {
        toast.success(`Query executed successfully! Result: ${result.result ? 'True' : 'False'}`);
      }
    } catch (error) {
      toast.error(`Query execution failed: ${error.message}`);
      setResults({
        success: false,
        error: error.message,
        query: query
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleValidateQuery = async () => {
    if (!query.trim()) {
      toast.warning('Please enter a query to validate');
      return;
    }

    try {
      const validation = await ruleEngineAPI.validateQuery(query.trim());
      setValidationStatus(validation);
      
      if (validation.validation?.is_valid) {
        toast.success('Query syntax is valid!');
      } else {
        toast.warning('Query has validation issues');
      }
    } catch (error) {
      toast.error(`Validation failed: ${error.message}`);
      setValidationStatus({
        validation: {
          is_valid: false,
          errors: [error.message]
        }
      });
    }
  };

  const handleCopyQuery = () => {
    navigator.clipboard.writeText(query);
    toast.success('Query copied to clipboard!');
  };

  const handleExportResults = () => {
    if (!results) {
      toast.warning('No results to export');
      return;
    }

    const dataStr = JSON.stringify(results, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `astro-query-results-${Date.now()}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
    toast.success('Results exported successfully!');
  };

  const getValidationStatus = () => {
    if (!validationStatus) return null;
    
    const isValid = validationStatus.validation?.is_valid;
    return isValid ? 'success' : 'error';
  };

  return (
    <QueryBuilderContainer>
      <Header>
        <Title>Query Builder</Title>
        <Subtitle>
          Build and execute powerful astrological queries with real-time validation and comprehensive results.
        </Subtitle>
      </Header>

      <MainContent>
        <QuerySection>
          {/* Query Input Section */}
          <Card>
            <CardHeader>
              <h3>
                <FiCode />
                Query Input
              </h3>
            </CardHeader>
            
            <QueryInput
              value={query}
              onChange={setQuery}
              onValidate={handleValidateQuery}
              validationStatus={validationStatus}
            />
            
            <ActionButtons>
              <ActionButton primary onClick={handleExecuteQuery} disabled={isLoading}>
                {isLoading ? <div className="loading-spinner" /> : <FiPlay />}
                {isLoading ? 'Executing...' : 'Execute Query'}
              </ActionButton>
              
              <ActionButton onClick={handleValidateQuery}>
                <FiCheck />
                Validate
              </ActionButton>
              
              <ActionButton onClick={handleCopyQuery}>
                <FiCopy />
                Copy
              </ActionButton>
              
              {results && (
                <ActionButton onClick={handleExportResults}>
                  <FiDownload />
                  Export
                </ActionButton>
              )}
            </ActionButtons>

            {validationStatus && (
              <StatusIndicator status={getValidationStatus()}>
                {getValidationStatus() === 'success' ? <FiCheck /> : <FiAlertCircle />}
                {getValidationStatus() === 'success' ? 'Valid Query' : 'Validation Issues'}
              </StatusIndicator>
            )}
          </Card>

          {/* Parameters Section */}
          <Card>
            <CardHeader>
              <h3>
                <FiSettings />
                Query Parameters
              </h3>
            </CardHeader>
            
            <QueryParameters
              parameters={parameters}
              onChange={setParameters}
            />
          </Card>

          {/* Results Section */}
          {results && (
            <Card>
              <CardHeader>
                <h3>
                  <FiCheck />
                  Query Results
                </h3>
              </CardHeader>
              
              <ResultsDisplay results={results} />
            </Card>
          )}
        </QuerySection>

        {/* Sidebar */}
        <Sidebar>
          <Card>
            <CardHeader>
              <h3>
                <FiHelpCircle />
                Quick Help
              </h3>
            </CardHeader>
            
            <div style={{ fontSize: '0.9rem', color: '#6b7280' }}>
              <p><strong>Basic Syntax:</strong></p>
              <ul style={{ marginLeft: '1rem', marginBottom: '1rem' }}>
                <li>PLANET IN HOUSE</li>
                <li>PLANET NOT_IN HOUSE</li>
                <li>PLANET WITH_RULING_PLANET TARGET</li>
                <li>PLANET TOGETHER_WITH PLANET</li>
              </ul>
              
              <p><strong>Logical Operators:</strong></p>
              <ul style={{ marginLeft: '1rem' }}>
                <li>OR, AND, NOT</li>
                <li>Use parentheses for grouping</li>
              </ul>
            </div>
          </Card>

          {examples && (
            <Card>
              <CardHeader>
                <h3>
                  <FiCode />
                  Examples
                </h3>
              </CardHeader>
              
              <QueryExamples 
                examples={examples} 
                onSelectExample={setQuery}
              />
            </Card>
          )}
        </Sidebar>
      </MainContent>
    </QueryBuilderContainer>
  );
};

export default QueryBuilder;
