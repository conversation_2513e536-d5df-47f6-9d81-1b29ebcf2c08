/**
 * AstroLogic Rule Engine - Examples Page
 * 
 * Comprehensive examples and tutorials for using the query language.
 */

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { 
  FiCode, FiBook, FiPlay, FiCopy, FiArrowRight, 
  FiStar, FiTarget, FiZap, FiLayers 
} from 'react-icons/fi';
import { toast } from 'react-toastify';
import { ruleEngineAPI } from '../../services/api';

const ExamplesContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  color: white;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #feca57, #ff9ff3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;

const Subtitle = styled.p`
  font-size: 1.1rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
`;

const Section = styled.section`
  margin-bottom: 3rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const CategoryGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const CategoryCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  }
`;

const CategoryHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 1rem;
  
  h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
  }
`;

const CategoryIcon = styled.div`
  font-size: 1.5rem;
  color: #feca57;
`;

const ExamplesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const ExampleItem = styled.div`
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const ExampleQuery = styled.div`
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.9rem;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  color: #f8f8f2;
  word-break: break-all;
  position: relative;
  
  &:hover .copy-button {
    opacity: 1;
  }
`;

const CopyButton = styled.button`
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }
`;

const ExampleDescription = styled.div`
  font-size: 0.9rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
`;

const ExampleActions = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
`;

const ActionButton = styled.button`
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }
`;

const TutorialSection = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 2rem;
`;

const StepsList = styled.ol`
  list-style: none;
  counter-reset: step-counter;
  padding: 0;
`;

const Step = styled.li`
  counter-increment: step-counter;
  margin-bottom: 1.5rem;
  display: flex;
  gap: 1rem;
  
  &::before {
    content: counter(step-counter);
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    flex-shrink: 0;
  }
`;

const StepContent = styled.div`
  flex: 1;
  
  h4 {
    margin: 0 0 0.5rem 0;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    opacity: 0.9;
  }
`;

const Examples = () => {
  const [examples, setExamples] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadExamples();
  }, []);

  const loadExamples = async () => {
    try {
      const data = await ruleEngineAPI.getExamples();
      setExamples(data);
    } catch (error) {
      toast.error('Failed to load examples');
      console.error('Error loading examples:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyQuery = (query) => {
    navigator.clipboard.writeText(query);
    toast.success('Query copied to clipboard!');
  };

  const handleTryQuery = (query) => {
    // Store query in localStorage and navigate to query builder
    localStorage.setItem('astro_query_draft', query);
    window.location.href = '/query';
  };

  if (loading) {
    return (
      <ExamplesContainer>
        <div style={{ textAlign: 'center', padding: '4rem 0' }}>
          <div className="loading-spinner" style={{ margin: '0 auto 1rem' }} />
          <p>Loading examples...</p>
        </div>
      </ExamplesContainer>
    );
  }

  const categoryIcons = {
    basic_queries: <FiTarget />,
    logical_operators: <FiZap />,
    relationship_queries: <FiStar />,
    nakshatra_queries: <FiLayers />,
    complex_queries: <FiCode />
  };

  const categoryTitles = {
    basic_queries: 'Basic Planet-House Queries',
    logical_operators: 'Logical Operators',
    relationship_queries: 'Relationship Queries',
    nakshatra_queries: 'Nakshatra Queries',
    complex_queries: 'Complex Queries'
  };

  return (
    <ExamplesContainer>
      <Header>
        <Title>Query Examples & Tutorials</Title>
        <Subtitle>
          Learn the AstroLogic query language with comprehensive examples and step-by-step tutorials.
        </Subtitle>
      </Header>

      {/* Quick Tutorial */}
      <Section>
        <SectionTitle>
          <FiBook />
          Quick Start Tutorial
        </SectionTitle>
        
        <TutorialSection>
          <h3 style={{ marginBottom: '1.5rem' }}>Building Your First Query</h3>
          
          <StepsList>
            <Step>
              <StepContent>
                <h4>Start with a Basic Condition</h4>
                <p>Begin with a simple planet-house relationship like "JUPITER IN 7"</p>
              </StepContent>
            </Step>
            
            <Step>
              <StepContent>
                <h4>Add Logical Operators</h4>
                <p>Combine conditions using OR, AND, NOT: "JUPITER IN 7 OR VENUS IN 1"</p>
              </StepContent>
            </Step>
            
            <Step>
              <StepContent>
                <h4>Include Relationships</h4>
                <p>Add planetary relationships: "JUPITER WITH_RULING_PLANET 7th_House_Ruling_Planet"</p>
              </StepContent>
            </Step>
            
            <Step>
              <StepContent>
                <h4>Test and Refine</h4>
                <p>Execute your query and analyze the results to refine your conditions</p>
              </StepContent>
            </Step>
          </StepsList>
          
          <div style={{ marginTop: '2rem', textAlign: 'center' }}>
            <Link to="/query" style={{ textDecoration: 'none' }}>
              <ActionButton style={{ background: 'linear-gradient(45deg, #667eea, #764ba2)' }}>
                <FiPlay />
                Try Query Builder
                <FiArrowRight />
              </ActionButton>
            </Link>
          </div>
        </TutorialSection>
      </Section>

      {/* Examples by Category */}
      {examples && examples.examples && (
        <Section>
          <SectionTitle>
            <FiCode />
            Query Examples by Category
          </SectionTitle>
          
          <CategoryGrid>
            {Object.entries(examples.examples).map(([categoryKey, categoryExamples]) => (
              <CategoryCard key={categoryKey}>
                <CategoryHeader>
                  <CategoryIcon>
                    {categoryIcons[categoryKey] || <FiCode />}
                  </CategoryIcon>
                  <h3>{categoryTitles[categoryKey] || categoryKey}</h3>
                </CategoryHeader>
                
                <ExamplesList>
                  {categoryExamples.slice(0, 3).map((example, index) => (
                    <ExampleItem key={index}>
                      <ExampleQuery>
                        {example.query}
                        <CopyButton 
                          className="copy-button"
                          onClick={() => handleCopyQuery(example.query)}
                        >
                          <FiCopy />
                        </CopyButton>
                      </ExampleQuery>
                      
                      <ExampleDescription>
                        {example.description}
                      </ExampleDescription>
                      
                      <ExampleActions>
                        <ActionButton onClick={() => handleTryQuery(example.query)}>
                          <FiPlay />
                          Try
                        </ActionButton>
                        <ActionButton onClick={() => handleCopyQuery(example.query)}>
                          <FiCopy />
                          Copy
                        </ActionButton>
                      </ExampleActions>
                    </ExampleItem>
                  ))}
                  
                  {categoryExamples.length > 3 && (
                    <div style={{ 
                      textAlign: 'center', 
                      padding: '0.5rem',
                      opacity: 0.7,
                      fontSize: '0.9rem'
                    }}>
                      +{categoryExamples.length - 3} more examples
                    </div>
                  )}
                </ExamplesList>
              </CategoryCard>
            ))}
          </CategoryGrid>
        </Section>
      )}

      {/* Usage Notes */}
      {examples && examples.usage_notes && (
        <Section>
          <SectionTitle>
            <FiBook />
            Usage Notes
          </SectionTitle>
          
          <TutorialSection>
            <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
              {examples.usage_notes.map((note, index) => (
                <li key={index} style={{ marginBottom: '0.5rem', opacity: 0.9 }}>
                  {note}
                </li>
              ))}
            </ul>
          </TutorialSection>
        </Section>
      )}
    </ExamplesContainer>
  );
};

export default Examples;
