/**
 * AstroLogic Rule Engine - Home Page Component
 * 
 * Landing page with overview, features, and quick start guide.
 */

import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { 
  FiStar, FiCode, FiZap, FiShield, FiTrendingUp, 
  FiBook, FiPlay, FiArrowRight, FiCheck 
} from 'react-icons/fi';

const HomeContainer = styled.div`
  color: white;
  text-align: center;
`;

const HeroSection = styled.section`
  padding: 4rem 0;
  margin-bottom: 4rem;
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: linear-gradient(45deg, #feca57, #ff9ff3, #54a0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
  
  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.3rem;
  opacity: 0.9;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  
  @media (max-width: 768px) {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }
`;

const CTAButtons = styled.div`
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 4rem;
`;

const CTAButton = styled(Link)`
  background: ${props => props.primary ? 'rgba(255, 255, 255, 0.2)' : 'transparent'};
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  backdrop-filter: blur(10px);
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    text-decoration: none;
  }
`;

const FeaturesSection = styled.section`
  margin-bottom: 4rem;
`;

const SectionTitle = styled.h2`
  font-size: 2.5rem;
  margin-bottom: 3rem;
  font-weight: 700;
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
`;

const FeatureCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  text-align: left;
  
  &:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  }
`;

const FeatureIcon = styled.div`
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #feca57;
`;

const FeatureTitle = styled.h3`
  font-size: 1.4rem;
  margin-bottom: 1rem;
  font-weight: 600;
`;

const FeatureDescription = styled.p`
  opacity: 0.9;
  line-height: 1.6;
`;

const QuickStartSection = styled.section`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 3rem;
  margin-bottom: 4rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const StepsList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
`;

const Step = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  text-align: left;
`;

const StepNumber = styled.div`
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  flex-shrink: 0;
`;

const StepContent = styled.div`
  h4 {
    margin-bottom: 0.5rem;
    font-weight: 600;
  }
  
  p {
    opacity: 0.9;
    margin: 0;
  }
`;

const ExampleQuery = styled.div`
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 2rem 0;
  font-family: 'JetBrains Mono', monospace;
  text-align: left;
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const QueryLabel = styled.div`
  color: #feca57;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
`;

const QueryText = styled.div`
  color: #f8f8f2;
  font-size: 1.1rem;
  font-weight: 500;
`;

const HomePage = () => {
  const features = [
    {
      icon: <FiCode />,
      title: "Advanced Query Language",
      description: "Powerful query syntax supporting complex logical operations, planetary relationships, and house-based conditions."
    },
    {
      icon: <FiZap />,
      title: "Real-time Processing",
      description: "Lightning-fast evaluation of astrological conditions with comprehensive relationship analysis and instant results."
    },
    {
      icon: <FiShield />,
      title: "Professional Accuracy",
      description: "Built on traditional Vedic astrology principles with modern computational precision and validation."
    },
    {
      icon: <FiTrendingUp />,
      title: "5-Rule Relationship System",
      description: "Comprehensive relationship checking including position, conjunction, aspects, and nakshatra relationships."
    }
  ];

  const quickStartSteps = [
    {
      title: "Enter Query",
      description: "Type your astrological query using our intuitive syntax"
    },
    {
      title: "Set Parameters",
      description: "Configure user and member profile IDs and chart type"
    },
    {
      title: "Execute & Analyze",
      description: "Get instant results with detailed relationship analysis"
    }
  ];

  return (
    <HomeContainer>
      <HeroSection>
        <HeroTitle>
          AstroLogic Rule Engine
        </HeroTitle>
        <HeroSubtitle>
          Professional astrological query processing with comprehensive planetary 
          relationship analysis and advanced logical operations.
        </HeroSubtitle>
        
        <CTAButtons>
          <CTAButton to="/query" primary>
            <FiPlay />
            Try Query Builder
          </CTAButton>
          <CTAButton to="/examples">
            <FiBook />
            View Examples
          </CTAButton>
        </CTAButtons>

        <ExampleQuery>
          <QueryLabel>Example Query:</QueryLabel>
          <QueryText>
            JUPITER IN 7 OR VENUS WITH_RULING_PLANET 7th_House_Ruling_Planet
          </QueryText>
        </ExampleQuery>
      </HeroSection>

      <FeaturesSection>
        <SectionTitle>Powerful Features</SectionTitle>
        <FeaturesGrid>
          {features.map((feature, index) => (
            <FeatureCard key={index}>
              <FeatureIcon>{feature.icon}</FeatureIcon>
              <FeatureTitle>{feature.title}</FeatureTitle>
              <FeatureDescription>{feature.description}</FeatureDescription>
            </FeatureCard>
          ))}
        </FeaturesGrid>
      </FeaturesSection>

      <QuickStartSection>
        <SectionTitle>Quick Start</SectionTitle>
        <p style={{ fontSize: '1.1rem', opacity: 0.9, marginBottom: '2rem' }}>
          Get started with AstroLogic Rule Engine in three simple steps
        </p>
        
        <StepsList>
          {quickStartSteps.map((step, index) => (
            <Step key={index}>
              <StepNumber>{index + 1}</StepNumber>
              <StepContent>
                <h4>{step.title}</h4>
                <p>{step.description}</p>
              </StepContent>
            </Step>
          ))}
        </StepsList>

        <div style={{ marginTop: '3rem' }}>
          <CTAButton to="/query" primary>
            <FiArrowRight />
            Start Building Queries
          </CTAButton>
        </div>
      </QuickStartSection>
    </HomeContainer>
  );
};

export default HomePage;
