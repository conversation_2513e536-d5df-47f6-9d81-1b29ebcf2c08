/**
 * AstroLogic Rule Engine - About Page
 * 
 * Information about the project, technology stack, and team.
 */

import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { 
  FiStar, FiCode, FiHeart, FiUsers, FiTarget, FiZap,
  FiShield, FiTrendingUp, FiGithub, FiMail, FiExternalLink
} from 'react-icons/fi';

const AboutContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  color: white;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 4rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #feca57, #ff9ff3, #54a0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
`;

const Section = styled.section`
  margin-bottom: 4rem;
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #feca57;
`;

const Card = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 2rem;
`;

const Grid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
`;

const FeatureCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  }
`;

const FeatureIcon = styled.div`
  font-size: 2rem;
  color: #feca57;
  margin-bottom: 1rem;
`;

const FeatureTitle = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
`;

const FeatureDescription = styled.p`
  opacity: 0.9;
  line-height: 1.6;
  margin: 0;
`;

const TechStack = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const TechCategory = styled.div`
  h4 {
    color: #feca57;
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }
`;

const TechList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
  
  li {
    padding: 0.5rem 0;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &::before {
      content: "✓";
      color: #22c55e;
      font-weight: bold;
    }
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
`;

const StatCard = styled.div`
  text-align: center;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const StatNumber = styled.div`
  font-size: 2.5rem;
  font-weight: 700;
  color: #feca57;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  opacity: 0.8;
`;

const ContactSection = styled.div`
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 3rem 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const ContactButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 2rem;
`;

const ContactButton = styled.a`
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    text-decoration: none;
  }
`;

const About = () => {
  const features = [
    {
      icon: <FiCode />,
      title: "Advanced Query Language",
      description: "Powerful syntax supporting complex logical operations, planetary relationships, and house-based conditions with real-time validation."
    },
    {
      icon: <FiZap />,
      title: "Lightning Fast Processing",
      description: "Optimized algorithms provide instant query evaluation with comprehensive relationship analysis and detailed results."
    },
    {
      icon: <FiShield />,
      title: "Professional Accuracy",
      description: "Built on traditional Vedic astrology principles with modern computational precision and extensive validation."
    },
    {
      icon: <FiTrendingUp />,
      title: "5-Rule Relationship System",
      description: "Comprehensive analysis including position, conjunction, aspects, nakshatra relationships, and planetary exchanges."
    },
    {
      icon: <FiTarget />,
      title: "10 Query Patterns",
      description: "Support for diverse astrological analysis patterns from basic planet positions to complex multi-condition evaluations."
    },
    {
      icon: <FiUsers />,
      title: "User-Friendly Interface",
      description: "Intuitive React-based interface with syntax highlighting, auto-completion, and comprehensive error reporting."
    }
  ];

  const stats = [
    { number: "10+", label: "Query Patterns" },
    { number: "5", label: "Relationship Rules" },
    { number: "23", label: "Chart Types Supported" },
    { number: "100%", label: "Vedic Accuracy" }
  ];

  return (
    <AboutContainer>
      <Header>
        <Title>About AstroLogic</Title>
        <Subtitle>
          A professional-grade astrological rule engine that bridges ancient Vedic wisdom 
          with modern computational power, providing accurate and comprehensive planetary 
          relationship analysis for astrologers and enthusiasts worldwide.
        </Subtitle>
      </Header>

      <Section>
        <SectionTitle>
          <FiStar />
          Project Vision
        </SectionTitle>
        
        <Card>
          <p style={{ fontSize: '1.1rem', lineHeight: '1.7', marginBottom: '1.5rem' }}>
            AstroLogic Rule Engine was born from the need to create a standalone, professional-grade 
            astrological analysis tool that preserves the depth and accuracy of traditional Vedic 
            astrology while leveraging modern technology for enhanced accessibility and precision.
          </p>
          
          <p style={{ fontSize: '1.1rem', lineHeight: '1.7', marginBottom: '1.5rem' }}>
            Our mission is to democratize advanced astrological analysis by providing a powerful, 
            yet intuitive platform that serves both professional astrologers and curious learners. 
            We believe that ancient wisdom should be preserved and made accessible through modern tools.
          </p>
          
          <p style={{ fontSize: '1.1rem', lineHeight: '1.7', margin: 0 }}>
            This project represents a complete extraction and enhancement of the rule engine from 
            the Fortune Lens application, redesigned as a standalone system with improved architecture, 
            comprehensive documentation, and professional-grade user experience.
          </p>
        </Card>
      </Section>

      <Section>
        <SectionTitle>
          <FiZap />
          Key Features
        </SectionTitle>
        
        <Grid>
          {features.map((feature, index) => (
            <FeatureCard key={index}>
              <FeatureIcon>{feature.icon}</FeatureIcon>
              <FeatureTitle>{feature.title}</FeatureTitle>
              <FeatureDescription>{feature.description}</FeatureDescription>
            </FeatureCard>
          ))}
        </Grid>
      </Section>

      <Section>
        <SectionTitle>
          <FiCode />
          Technology Stack
        </SectionTitle>
        
        <Card>
          <TechStack>
            <TechCategory>
              <h4>Frontend</h4>
              <TechList>
                <li>React 18 with Hooks</li>
                <li>Styled Components</li>
                <li>React Router</li>
                <li>Axios for API calls</li>
                <li>React Icons</li>
                <li>React Toastify</li>
              </TechList>
            </TechCategory>
            
            <TechCategory>
              <h4>Backend</h4>
              <TechList>
                <li>Python Flask</li>
                <li>Flask-CORS</li>
                <li>Flask-JWT-Extended</li>
                <li>NumPy & SciPy</li>
                <li>Python-dateutil</li>
                <li>PyEphem (Optional)</li>
              </TechList>
            </TechCategory>
            
            <TechCategory>
              <h4>Architecture</h4>
              <TechList>
                <li>RESTful API Design</li>
                <li>Modular Component Structure</li>
                <li>Error Boundary Protection</li>
                <li>Responsive Design</li>
                <li>Professional Validation</li>
                <li>Comprehensive Testing</li>
              </TechList>
            </TechCategory>
            
            <TechCategory>
              <h4>Astrology Engine</h4>
              <TechList>
                <li>Vedic Astrology Principles</li>
                <li>Planetary Relationships</li>
                <li>House Systems</li>
                <li>Nakshatra Calculations</li>
                <li>Aspect Analysis</li>
                <li>Dasha Calculations</li>
              </TechList>
            </TechCategory>
          </TechStack>
        </Card>
      </Section>

      <Section>
        <SectionTitle>
          <FiTrendingUp />
          Project Statistics
        </SectionTitle>
        
        <StatsGrid>
          {stats.map((stat, index) => (
            <StatCard key={index}>
              <StatNumber>{stat.number}</StatNumber>
              <StatLabel>{stat.label}</StatLabel>
            </StatCard>
          ))}
        </StatsGrid>
      </Section>

      <Section>
        <SectionTitle>
          <FiHeart />
          Open Source & Community
        </SectionTitle>
        
        <Card>
          <p style={{ fontSize: '1.1rem', lineHeight: '1.7', marginBottom: '1.5rem' }}>
            AstroLogic Rule Engine is built with love for the astrological community. We believe 
            in the power of open collaboration and welcome contributions from developers, astrologers, 
            and enthusiasts who share our vision.
          </p>
          
          <p style={{ fontSize: '1.1rem', lineHeight: '1.7', marginBottom: '1.5rem' }}>
            Whether you're interested in contributing code, improving documentation, adding new 
            features, or simply providing feedback, we'd love to hear from you. Together, we can 
            build the most comprehensive and accurate astrological analysis platform available.
          </p>
          
          <p style={{ fontSize: '1.1rem', lineHeight: '1.7', margin: 0 }}>
            The project follows modern software development practices with clean architecture, 
            comprehensive testing, and detailed documentation to ensure maintainability and 
            extensibility for future enhancements.
          </p>
        </Card>
      </Section>

      <Section>
        <ContactSection>
          <SectionTitle style={{ justifyContent: 'center', marginBottom: '1rem' }}>
            <FiUsers />
            Get Involved
          </SectionTitle>
          
          <p style={{ fontSize: '1.1rem', marginBottom: '2rem', opacity: 0.9 }}>
            Join our community and help shape the future of astrological technology
          </p>
          
          <ContactButtons>
            <ContactButton 
              href="https://github.com/your-repo/astro-logic-rule-engine" 
              target="_blank" 
              rel="noopener noreferrer"
            >
              <FiGithub />
              View on GitHub
              <FiExternalLink />
            </ContactButton>
            
            <ContactButton href="mailto:<EMAIL>">
              <FiMail />
              Contact Us
            </ContactButton>
            
            <ContactButton as={Link} to="/query">
              <FiCode />
              Try the Engine
            </ContactButton>
          </ContactButtons>
        </ContactSection>
      </Section>
    </AboutContainer>
  );
};

export default About;
