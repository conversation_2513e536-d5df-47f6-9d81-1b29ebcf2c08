/**
 * AstroLogic Rule Engine - Documentation Page
 * 
 * Comprehensive documentation for the query language and API.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  FiBook, FiCode, FiStar, FiTarget, FiZap, FiLayers,
  FiChevronDown, FiChevronRight, FiCopy, FiExternalLink
} from 'react-icons/fi';
import { toast } from 'react-toastify';

const DocsContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  color: white;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #feca57, #ff9ff3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;

const Subtitle = styled.p`
  font-size: 1.1rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
`;

const DocsContent = styled.div`
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 2rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const Sidebar = styled.nav`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: fit-content;
  position: sticky;
  top: 2rem;
  
  @media (max-width: 1024px) {
    position: static;
  }
`;

const SidebarTitle = styled.h3`
  margin-bottom: 1rem;
  font-size: 1.1rem;
  color: #feca57;
`;

const NavList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const NavItem = styled.li`
  margin-bottom: 0.5rem;
`;

const NavLink = styled.a`
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.9rem;
  display: block;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
  }
  
  &.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
  }
`;

const MainContent = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const Section = styled.section`
  margin-bottom: 3rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #feca57;
  border-bottom: 2px solid rgba(254, 202, 87, 0.3);
  padding-bottom: 0.5rem;
`;

const SubSection = styled.div`
  margin-bottom: 2rem;
`;

const SubTitle = styled.h3`
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: white;
`;

const CodeBlock = styled.pre`
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 1rem;
  overflow-x: auto;
  margin: 1rem 0;
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.9rem;
  color: #f8f8f2;
  position: relative;
  
  &:hover .copy-button {
    opacity: 1;
  }
`;

const CopyButton = styled.button`
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  overflow: hidden;
  
  th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  th {
    background: rgba(0, 0, 0, 0.3);
    font-weight: 600;
    color: #feca57;
  }
  
  td {
    color: rgba(255, 255, 255, 0.9);
  }
  
  tr:last-child td {
    border-bottom: none;
  }
`;

const InfoBox = styled.div`
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  color: #93c5fd;
`;

const WarningBox = styled.div`
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  color: #fbbf24;
`;

const Documentation = () => {
  const [activeSection, setActiveSection] = useState('overview');

  const handleCopyCode = (code) => {
    navigator.clipboard.writeText(code);
    toast.success('Code copied to clipboard!');
  };

  const scrollToSection = (sectionId) => {
    setActiveSection(sectionId);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <DocsContainer>
      <Header>
        <Title>Documentation</Title>
        <Subtitle>
          Complete guide to the AstroLogic Rule Engine query language, API, and features.
        </Subtitle>
      </Header>

      <DocsContent>
        <Sidebar>
          <SidebarTitle>Contents</SidebarTitle>
          <NavList>
            <NavItem>
              <NavLink 
                href="#overview" 
                className={activeSection === 'overview' ? 'active' : ''}
                onClick={(e) => { e.preventDefault(); scrollToSection('overview'); }}
              >
                Overview
              </NavLink>
            </NavItem>
            <NavItem>
              <NavLink 
                href="#syntax" 
                className={activeSection === 'syntax' ? 'active' : ''}
                onClick={(e) => { e.preventDefault(); scrollToSection('syntax'); }}
              >
                Query Syntax
              </NavLink>
            </NavItem>
            <NavItem>
              <NavLink 
                href="#operators" 
                className={activeSection === 'operators' ? 'active' : ''}
                onClick={(e) => { e.preventDefault(); scrollToSection('operators'); }}
              >
                Logical Operators
              </NavLink>
            </NavItem>
            <NavItem>
              <NavLink 
                href="#relationships" 
                className={activeSection === 'relationships' ? 'active' : ''}
                onClick={(e) => { e.preventDefault(); scrollToSection('relationships'); }}
              >
                Relationships
              </NavLink>
            </NavItem>
            <NavItem>
              <NavLink 
                href="#api" 
                className={activeSection === 'api' ? 'active' : ''}
                onClick={(e) => { e.preventDefault(); scrollToSection('api'); }}
              >
                API Reference
              </NavLink>
            </NavItem>
            <NavItem>
              <NavLink 
                href="#examples" 
                className={activeSection === 'examples' ? 'active' : ''}
                onClick={(e) => { e.preventDefault(); scrollToSection('examples'); }}
              >
                Examples
              </NavLink>
            </NavItem>
          </NavList>
        </Sidebar>

        <MainContent>
          <Section id="overview">
            <SectionTitle>
              <FiBook />
              Overview
            </SectionTitle>
            
            <p>
              The AstroLogic Rule Engine provides a powerful query language for evaluating 
              astrological conditions and relationships. It supports complex logical operations, 
              planetary relationships, and comprehensive chart analysis.
            </p>

            <SubSection>
              <SubTitle>Key Features</SubTitle>
              <ul>
                <li>10 different query patterns for comprehensive analysis</li>
                <li>Logical operators (OR, AND, NOT) with proper precedence</li>
                <li>5-rule relationship system for detailed planetary analysis</li>
                <li>Nakshatra (star constellation) based queries</li>
                <li>Real-time query validation and error reporting</li>
                <li>Professional success rating system</li>
              </ul>
            </SubSection>

            <InfoBox>
              <strong>Note:</strong> This engine is based on traditional Vedic astrology principles 
              and uses Tamil house names in English transliteration.
            </InfoBox>
          </Section>

          <Section id="syntax">
            <SectionTitle>
              <FiCode />
              Query Syntax
            </SectionTitle>

            <SubSection>
              <SubTitle>Basic Planet-House Queries</SubTitle>
              <p>The simplest form of query checks if a planet is in a specific house:</p>
              
              <CodeBlock>
{`JUPITER IN 7
VENUS IN 1st_House
MARS NOT_IN 8`}
                <CopyButton 
                  className="copy-button"
                  onClick={() => handleCopyCode('JUPITER IN 7\nVENUS IN 1st_House\nMARS NOT_IN 8')}
                >
                  <FiCopy />
                </CopyButton>
              </CodeBlock>

              <Table>
                <thead>
                  <tr>
                    <th>Format</th>
                    <th>Description</th>
                    <th>Example</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>PLANET IN HOUSE</td>
                    <td>Planet is in specified house</td>
                    <td>JUPITER IN 7</td>
                  </tr>
                  <tr>
                    <td>PLANET NOT_IN HOUSE</td>
                    <td>Planet is not in specified house</td>
                    <td>MARS NOT_IN 8</td>
                  </tr>
                  <tr>
                    <td>PLANET IN Nth_House</td>
                    <td>Alternative house format</td>
                    <td>VENUS IN 1st_House</td>
                  </tr>
                </tbody>
              </Table>
            </SubSection>

            <SubSection>
              <SubTitle>Valid Planets</SubTitle>
              <CodeBlock>
{`SUN, MOON, MARS, MERCURY, JUPITER, VENUS, SATURN, RAHU, KETU`}
                <CopyButton 
                  className="copy-button"
                  onClick={() => handleCopyCode('SUN, MOON, MARS, MERCURY, JUPITER, VENUS, SATURN, RAHU, KETU')}
                >
                  <FiCopy />
                </CopyButton>
              </CodeBlock>
            </SubSection>

            <SubSection>
              <SubTitle>Valid Houses</SubTitle>
              <p>Houses can be specified as numbers (1-12) or with ordinal suffixes:</p>
              <CodeBlock>
{`1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12
1st_House, 2nd_House, 3rd_House, etc.
7th_House_Ruling_Planet (for relationship queries)`}
                <CopyButton 
                  className="copy-button"
                  onClick={() => handleCopyCode('1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12')}
                >
                  <FiCopy />
                </CopyButton>
              </CodeBlock>
            </SubSection>
          </Section>

          <Section id="operators">
            <SectionTitle>
              <FiZap />
              Logical Operators
            </SectionTitle>

            <SubSection>
              <SubTitle>Operator Precedence</SubTitle>
              <p>Operators are evaluated in the following order (highest to lowest precedence):</p>
              
              <Table>
                <thead>
                  <tr>
                    <th>Precedence</th>
                    <th>Operator</th>
                    <th>Description</th>
                    <th>Example</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>1 (Highest)</td>
                    <td>NOT</td>
                    <td>Logical negation</td>
                    <td>NOT (JUPITER IN 12)</td>
                  </tr>
                  <tr>
                    <td>2</td>
                    <td>AND</td>
                    <td>Logical conjunction</td>
                    <td>MARS IN 1 AND SATURN NOT_IN 8</td>
                  </tr>
                  <tr>
                    <td>3 (Lowest)</td>
                    <td>OR</td>
                    <td>Logical disjunction</td>
                    <td>JUPITER IN 7 OR VENUS IN 1</td>
                  </tr>
                </tbody>
              </Table>
            </SubSection>

            <SubSection>
              <SubTitle>Using Parentheses</SubTitle>
              <p>Use parentheses to override default precedence:</p>
              
              <CodeBlock>
{`(JUPITER IN 7 OR VENUS IN 1) AND MARS NOT_IN 8
NOT (JUPITER IN 12 AND SATURN IN 8)
(MARS IN 1 OR MARS IN 8) AND (VENUS IN 2 OR VENUS IN 7)`}
                <CopyButton 
                  className="copy-button"
                  onClick={() => handleCopyCode('(JUPITER IN 7 OR VENUS IN 1) AND MARS NOT_IN 8')}
                >
                  <FiCopy />
                </CopyButton>
              </CodeBlock>
            </SubSection>
          </Section>

          <Section id="relationships">
            <SectionTitle>
              <FiStar />
              Planetary Relationships
            </SectionTitle>

            <SubSection>
              <SubTitle>5-Rule Relationship System</SubTitle>
              <p>The engine evaluates relationships using five comprehensive rules:</p>

              <Table>
                <thead>
                  <tr>
                    <th>Rule</th>
                    <th>Operator</th>
                    <th>Description</th>
                    <th>Example</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>1. Basic Position</td>
                    <td>WITH_RULING_PLANET</td>
                    <td>Planet with house ruling planet</td>
                    <td>JUPITER WITH_RULING_PLANET 7th_House_Ruling_Planet</td>
                  </tr>
                  <tr>
                    <td>2. Conjunction</td>
                    <td>TOGETHER_WITH</td>
                    <td>Planets in same house</td>
                    <td>VENUS TOGETHER_WITH MARS</td>
                  </tr>
                  <tr>
                    <td>3. Aspects</td>
                    <td>IS_ASPECTING_BIRTH</td>
                    <td>Planet aspecting another</td>
                    <td>SUN IS_ASPECTING_BIRTH MOON</td>
                  </tr>
                  <tr>
                    <td>4. Nakshatra</td>
                    <td>WITH_STARS_OF</td>
                    <td>Planet in nakshatra of another</td>
                    <td>JUPITER WITH_STARS_OF VENUS</td>
                  </tr>
                  <tr>
                    <td>5. Exchange</td>
                    <td>-</td>
                    <td>Mutual house exchange</td>
                    <td>Automatically checked</td>
                  </tr>
                </tbody>
              </Table>
            </SubSection>

            <SubSection>
              <SubTitle>Relationship Examples</SubTitle>
              <CodeBlock>
{`# Ruling planet relationship
JUPITER WITH_RULING_PLANET 7th_House_Ruling_Planet

# Conjunction
VENUS TOGETHER_WITH MARS

# Aspect relationship  
SUN IS_ASPECTING_BIRTH MOON

# Nakshatra relationship
MARS WITH_STARS_OF 7th_House_Ruling_Planet

# Complex relationship query
JUPITER WITH_RULING_PLANET 7th_House_Ruling_Planet OR VENUS TOGETHER_WITH MARS`}
                <CopyButton 
                  className="copy-button"
                  onClick={() => handleCopyCode('JUPITER WITH_RULING_PLANET 7th_House_Ruling_Planet')}
                >
                  <FiCopy />
                </CopyButton>
              </CodeBlock>
            </SubSection>
          </Section>

          <Section id="api">
            <SectionTitle>
              <FiTarget />
              API Reference
            </SectionTitle>

            <SubSection>
              <SubTitle>Main Evaluation Endpoint</SubTitle>
              <CodeBlock>
{`POST /api/rule-engine/

Request Body:
{
  "user_profile_id": "1",
  "member_profile_id": "1", 
  "query": "JUPITER IN 7 OR VENUS WITH_RULING_PLANET 7th_House_Ruling_Planet",
  "chart_type": "D1"
}

Response:
{
  "success": true,
  "result": true,
  "success_rate": 85.5,
  "success_rating": "Very Good",
  "query_analysis": {
    "total_conditions": 2,
    "successful_conditions": 1,
    "logical_operator": "OR"
  }
}`}
                <CopyButton 
                  className="copy-button"
                  onClick={() => handleCopyCode('POST /api/rule-engine/')}
                >
                  <FiCopy />
                </CopyButton>
              </CodeBlock>
            </SubSection>

            <SubSection>
              <SubTitle>Other Endpoints</SubTitle>
              <Table>
                <thead>
                  <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>/api/rule-engine/validate</td>
                    <td>POST</td>
                    <td>Validate query syntax</td>
                  </tr>
                  <tr>
                    <td>/api/rule-engine/examples</td>
                    <td>GET</td>
                    <td>Get query examples</td>
                  </tr>
                  <tr>
                    <td>/api/rule-engine/health</td>
                    <td>GET</td>
                    <td>Health check</td>
                  </tr>
                </tbody>
              </Table>
            </SubSection>
          </Section>

          <Section id="examples">
            <SectionTitle>
              <FiLayers />
              Complete Examples
            </SectionTitle>

            <SubSection>
              <SubTitle>Marriage Analysis Query</SubTitle>
              <CodeBlock>
{`(JUPITER IN 7 OR VENUS IN 7) AND 
(JUPITER WITH_RULING_PLANET 7th_House_Ruling_Planet OR 
 VENUS WITH_RULING_PLANET 7th_House_Ruling_Planet) AND
NOT (MARS IN 7 OR SATURN IN 7)`}
                <CopyButton 
                  className="copy-button"
                  onClick={() => handleCopyCode('(JUPITER IN 7 OR VENUS IN 7) AND (JUPITER WITH_RULING_PLANET 7th_House_Ruling_Planet OR VENUS WITH_RULING_PLANET 7th_House_Ruling_Planet) AND NOT (MARS IN 7 OR SATURN IN 7)')}
                >
                  <FiCopy />
                </CopyButton>
              </CodeBlock>
            </SubSection>

            <SubSection>
              <SubTitle>Career Analysis Query</SubTitle>
              <CodeBlock>
{`(SUN IN 10 OR MARS IN 10 OR SATURN IN 10) AND
(SUN WITH_RULING_PLANET 10th_House_Ruling_Planet OR
 MARS WITH_RULING_PLANET 10th_House_Ruling_Planet) AND
JUPITER IS_ASPECTING_BIRTH SUN`}
                <CopyButton 
                  className="copy-button"
                  onClick={() => handleCopyCode('(SUN IN 10 OR MARS IN 10 OR SATURN IN 10) AND (SUN WITH_RULING_PLANET 10th_House_Ruling_Planet OR MARS WITH_RULING_PLANET 10th_House_Ruling_Planet) AND JUPITER IS_ASPECTING_BIRTH SUN')}
                >
                  <FiCopy />
                </CopyButton>
              </CodeBlock>
            </SubSection>

            <WarningBox>
              <strong>Important:</strong> This documentation covers the core features. 
              For the most up-to-date examples and advanced usage patterns, 
              visit the Examples page or check the API examples endpoint.
            </WarningBox>
          </Section>
        </MainContent>
      </DocsContent>
    </DocsContainer>
  );
};

export default Documentation;
