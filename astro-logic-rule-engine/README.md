# AstroLogic Rule Engine

A standalone astrological rule engine application with React frontend and Python backend, extracted from the Fortune Lens project. This application provides comprehensive astrological query processing with support for complex logical operations and planetary relationship analysis.

## 🌟 Features

### Core Rule Engine Capabilities
- **10 Query Patterns**: Support for all major astrological query types
- **Logical Operators**: Full OR, AND, NOT logic support
- **5-Rule Relationship System**: 
  - Position exchange
  - WITH ruling planet
  - TOGETHER_WITH
  - Nakshatra relationships
  - Aspecting relationships
- **WITH_STARS_OF Functionality**: Nakshatra-based queries
- **Professional Error Handling**: Consistent response formats

### Astrological Features
- **Planetary Relationships**: Friends, enemies, neutral relationships
- **House Ruling Planets**: Complete house system support
- **Chart Data Processing**: Comprehensive chart analysis
- **Success Rating System**: Intelligent prediction scoring

### Technology Stack
- **Backend**: Python Flask with modular architecture
- **Frontend**: React with modern UI components
- **Architecture**: Clean separation of concerns, max 300 lines per file
- **Standards**: Professional naming conventions and code organization

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Node.js 14+
- npm or yarn

### Backend Setup
```bash
cd backend
pip install -r requirements.txt
python run.py
```

### Frontend Setup
```bash
cd frontend
npm install
npm start
```

## 📁 Project Structure

```
astro-logic-rule-engine/
├── backend/                 # Python Flask backend
│   ├── app/
│   │   ├── core/           # Core rule engine modules
│   │   ├── astrology/      # Astrological calculations
│   │   ├── services/       # Business logic services
│   │   └── api/            # API endpoints
│   └── requirements.txt
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   └── services/       # API services
│   └── package.json
└── README.md
```

## 🔧 API Endpoints

### Main Rule Engine Endpoint
```
POST /api/rule-engine/
```

**Request Body:**
```json
{
  "user_profile_id": "1",
  "member_profile_id": "1", 
  "query": "JUPITER IN 7 OR VENUS WITH_RULING_PLANET 7th_House_Ruling_Planet",
  "chart_type": "D1"
}
```

**Response:**
```json
{
  "success": true,
  "result": true,
  "success_rate": 85.5,
  "success_rating": "Very Good",
  "query_analysis": {
    "total_conditions": 2,
    "successful_conditions": 1,
    "logical_operator": "OR"
  }
}
```

## 📖 Query Examples

### Basic Planet-House Queries
```
JUPITER IN 7
VENUS IN 1st_House
MARS NOT_IN 8
```

### Logical Operators
```
JUPITER IN 7 OR VENUS IN 1
MARS IN 1 AND SATURN NOT_IN 8
NOT (JUPITER IN 12)
```

### Relationship Queries
```
JUPITER WITH_RULING_PLANET 7th_House_Ruling_Planet
VENUS TOGETHER_WITH MARS
SUN IS_ASPECTING_BIRTH MOON
```

### Nakshatra Queries
```
JUPITER WITH_STARS_OF VENUS
MARS WITH_STARS_OF 7th_House_Ruling_Planet
```

## 🏗️ Architecture

### Modular Design
- **Core Engine**: Query parsing and evaluation logic
- **Astrology Module**: Planetary calculations and relationships
- **Services Layer**: Business logic and data processing
- **API Layer**: RESTful endpoints with validation

### Design Principles
- Single Responsibility: Each module handles one specific aspect
- Clean Interfaces: Clear function signatures and return types
- Error Handling: Comprehensive error management
- Testability: Unit tests for all core functions

## 🧪 Testing

```bash
# Backend tests
cd backend
python -m pytest tests/

# Frontend tests  
cd frontend
npm test
```

## 📚 Documentation

- [API Documentation](./docs/api.md)
- [Query Language Guide](./docs/queries.md)
- [Astrological Concepts](./docs/astrology.md)
- [Development Guide](./docs/development.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Extracted from the Fortune Lens project
- Based on Vedic astrology principles
- Inspired by professional astrological software
