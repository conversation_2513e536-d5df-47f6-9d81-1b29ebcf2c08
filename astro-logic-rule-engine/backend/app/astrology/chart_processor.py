"""
AstroLogic Rule Engine - Chart Processor

This module handles chart data processing, planet-house mapping extraction,
and chart-related calculations for the rule engine.
"""

from typing import Dict, Any, Optional, List
from .house_systems import get_house_ruling_planet, get_house_sign_name
from .constants import PLANET_NAMES

class ChartProcessor:
    """
    Processes astrological chart data for rule engine evaluation.
    
    This class handles:
    - Chart data loading and validation
    - Planet-house mapping extraction
    - House ruling planet calculations
    - Chart data normalization
    """
    
    def __init__(self):
        self.sample_chart_data = self._create_sample_chart_data()
    
    def get_chart_data(self, user_profile_id: str, member_profile_id: str, 
                      chart_type: str = "D1") -> Optional[Dict[str, Any]]:
        """
        Get chart data for a specific user and member profile.
        
        Args:
            user_profile_id: User profile identifier
            member_profile_id: Member profile identifier
            chart_type: Chart type (D1, D2, etc.)
            
        Returns:
            Chart data dictionary or None if not found
        """
        # In a real implementation, this would fetch from database
        # For now, return sample data
        return self.sample_chart_data.get(f"{user_profile_id}_{member_profile_id}_{chart_type}")
    
    def get_planet_house_mapping(self, chart_data: Dict[str, Any], 
                                chart_type: str = "D1") -> Dict[str, int]:
        """
        Extract planet to house mapping from chart data.
        
        Args:
            chart_data: Chart data dictionary
            chart_type: Chart type
            
        Returns:
            Dictionary mapping planet names to house numbers
        """
        if not chart_data:
            return {}
        
        # Try different chart data structures
        if 'planet_house_mapping' in chart_data:
            return chart_data['planet_house_mapping']
        
        if 'houses' in chart_data:
            return self._extract_from_houses_data(chart_data['houses'])
        
        if 'planets' in chart_data:
            return self._extract_from_planets_data(chart_data['planets'])
        
        # Return default mapping if no data structure matches
        return self._get_default_planet_house_mapping()
    
    def get_house_sign_and_ruling_planet(self, chart_data: Dict[str, Any], 
                                        house_number: int, 
                                        chart_type: str = "D1") -> tuple:
        """
        Get the sign name and ruling planet for a specific house.
        
        Args:
            chart_data: Chart data dictionary
            house_number: House number (1-12)
            chart_type: Chart type
            
        Returns:
            Tuple of (sign_name, ruling_planet)
        """
        if not chart_data or not (1 <= house_number <= 12):
            return None, None
        
        # Try to get from chart data
        if 'houses' in chart_data and isinstance(chart_data['houses'], list):
            for house_data in chart_data['houses']:
                if house_data.get('house_number') == house_number:
                    sign_name = house_data.get('house_name') or house_data.get('sign')
                    ruling_planet = house_data.get('ruling_planet')
                    
                    if not ruling_planet:
                        ruling_planet = get_house_ruling_planet(house_number)
                    
                    return sign_name, ruling_planet
        
        # Fallback to default system
        sign_name = get_house_sign_name(house_number)
        ruling_planet = get_house_ruling_planet(house_number)
        
        return sign_name, ruling_planet
    
    def _extract_from_houses_data(self, houses_data: List[Dict[str, Any]]) -> Dict[str, int]:
        """Extract planet-house mapping from houses data structure."""
        planet_house_mapping = {}
        
        for house_data in houses_data:
            house_number = house_data.get('house_number')
            planets = house_data.get('planets', [])
            
            if house_number and planets:
                for planet in planets:
                    planet_name = planet.upper() if isinstance(planet, str) else str(planet).upper()
                    if planet_name in PLANET_NAMES:
                        planet_house_mapping[planet_name] = house_number
        
        return planet_house_mapping
    
    def _extract_from_planets_data(self, planets_data: Dict[str, Any]) -> Dict[str, int]:
        """Extract planet-house mapping from planets data structure."""
        planet_house_mapping = {}
        
        for planet, planet_info in planets_data.items():
            planet_name = planet.upper()
            
            if planet_name in PLANET_NAMES:
                if isinstance(planet_info, dict):
                    house_number = planet_info.get('house') or planet_info.get('house_number')
                elif isinstance(planet_info, (int, str)):
                    try:
                        house_number = int(planet_info)
                    except (ValueError, TypeError):
                        continue
                else:
                    continue
                
                if house_number and 1 <= house_number <= 12:
                    planet_house_mapping[planet_name] = house_number
        
        return planet_house_mapping
    
    def _get_default_planet_house_mapping(self) -> Dict[str, int]:
        """Get default planet-house mapping for demonstration."""
        return {
            'SUN': 5,      # Leo's natural house
            'MOON': 4,     # Cancer's natural house
            'MARS': 1,     # Aries' natural house
            'MERCURY': 6,  # Virgo's natural house
            'JUPITER': 7,  # Placed in 7th house for marriage analysis
            'VENUS': 2,    # Taurus' natural house
            'SATURN': 10,  # Capricorn's natural house
            'RAHU': 8,     # Placed in 8th house
            'KETU': 2      # Opposite to Rahu
        }
    
    def _create_sample_chart_data(self) -> Dict[str, Dict[str, Any]]:
        """Create sample chart data for demonstration."""
        sample_data = {}
        
        # Sample chart for user 1, member 1, D1
        sample_data['1_1_D1'] = {
            'chart_type': 'D1',
            'planet_house_mapping': {
                'SUN': 5,
                'MOON': 4,
                'MARS': 1,
                'MERCURY': 6,
                'JUPITER': 7,
                'VENUS': 2,
                'SATURN': 10,
                'RAHU': 8,
                'KETU': 2
            },
            'houses': [
                {
                    'house_number': 1,
                    'house_name': 'Mesham',
                    'planets': ['MARS'],
                    'ruling_planet': 'MARS'
                },
                {
                    'house_number': 2,
                    'house_name': 'Rishabam',
                    'planets': ['VENUS', 'KETU'],
                    'ruling_planet': 'VENUS'
                },
                {
                    'house_number': 3,
                    'house_name': 'Midunam',
                    'planets': [],
                    'ruling_planet': 'MERCURY'
                },
                {
                    'house_number': 4,
                    'house_name': 'Kadagam',
                    'planets': ['MOON'],
                    'ruling_planet': 'MOON'
                },
                {
                    'house_number': 5,
                    'house_name': 'Simmam',
                    'planets': ['SUN'],
                    'ruling_planet': 'SUN'
                },
                {
                    'house_number': 6,
                    'house_name': 'Kanni',
                    'planets': ['MERCURY'],
                    'ruling_planet': 'MERCURY'
                },
                {
                    'house_number': 7,
                    'house_name': 'Thulam',
                    'planets': ['JUPITER'],
                    'ruling_planet': 'VENUS'
                },
                {
                    'house_number': 8,
                    'house_name': 'Virichigam',
                    'planets': ['RAHU'],
                    'ruling_planet': 'MARS'
                },
                {
                    'house_number': 9,
                    'house_name': 'Dhanusu',
                    'planets': [],
                    'ruling_planet': 'JUPITER'
                },
                {
                    'house_number': 10,
                    'house_name': 'Magaram',
                    'planets': ['SATURN'],
                    'ruling_planet': 'SATURN'
                },
                {
                    'house_number': 11,
                    'house_name': 'Kumbam',
                    'planets': [],
                    'ruling_planet': 'SATURN'
                },
                {
                    'house_number': 12,
                    'house_name': 'Meenam',
                    'planets': [],
                    'ruling_planet': 'JUPITER'
                }
            ],
            'birth_info': {
                'user_profile_id': '1',
                'member_profile_id': '1',
                'birth_date': '1990-01-15',
                'birth_time': '10:30:00',
                'birth_place': 'Chennai, India'
            }
        }
        
        # Add more sample data for different users/members
        sample_data['1_2_D1'] = {
            'chart_type': 'D1',
            'planet_house_mapping': {
                'SUN': 3,
                'MOON': 7,
                'MARS': 9,
                'MERCURY': 2,
                'JUPITER': 1,
                'VENUS': 4,
                'SATURN': 11,
                'RAHU': 6,
                'KETU': 12
            },
            'birth_info': {
                'user_profile_id': '1',
                'member_profile_id': '2',
                'birth_date': '1992-05-20',
                'birth_time': '14:15:00',
                'birth_place': 'Mumbai, India'
            }
        }
        
        return sample_data
    
    def validate_chart_data(self, chart_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate chart data structure and content.
        
        Args:
            chart_data: Chart data to validate
            
        Returns:
            Validation results
        """
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'data_quality': 'good'
        }
        
        if not chart_data:
            validation['is_valid'] = False
            validation['errors'].append('Chart data is empty')
            return validation
        
        # Check for planet data
        planet_mapping = self.get_planet_house_mapping(chart_data)
        if not planet_mapping:
            validation['warnings'].append('No planet-house mapping found')
            validation['data_quality'] = 'limited'
        
        # Check for all major planets
        major_planets = ['SUN', 'MOON', 'MARS', 'MERCURY', 'JUPITER', 'VENUS', 'SATURN']
        missing_planets = [p for p in major_planets if p not in planet_mapping]
        
        if missing_planets:
            validation['warnings'].append(f'Missing planets: {", ".join(missing_planets)}')
        
        # Check house number validity
        for planet, house in planet_mapping.items():
            if not (1 <= house <= 12):
                validation['errors'].append(f'Invalid house number {house} for planet {planet}')
                validation['is_valid'] = False
        
        return validation

# Convenience functions for backward compatibility
def get_planet_house_mapping(chart_data: Dict[str, Any], chart_type: str = "D1") -> Dict[str, int]:
    """Get planet-house mapping from chart data."""
    processor = ChartProcessor()
    return processor.get_planet_house_mapping(chart_data, chart_type)

def get_house_sign_and_ruling_planet(chart_data: Dict[str, Any], house_number: int, 
                                   chart_type: str = "D1") -> tuple:
    """Get house sign and ruling planet."""
    processor = ChartProcessor()
    return processor.get_house_sign_and_ruling_planet(chart_data, house_number, chart_type)
