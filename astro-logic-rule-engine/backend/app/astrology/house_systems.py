"""
AstroLogic Rule Engine - House Systems

This module contains house ruling planet mappings and house system functions
for astrological calculations. Based on traditional Vedic astrology principles.
"""

from typing import List, Optional, Dict

# House ruling planets mapping (1-based house numbers to planet names)
# Based on natural zodiac starting from Aries
HOUSE_RULING_PLANETS = {
    1: 'MARS',      # <PERSON><PERSON> (Mesham)
    2: 'VENUS',     # <PERSON><PERSON> (Rishabam)
    3: 'MERCURY',   # <PERSON> (Midunam)
    4: 'MOON',      # <PERSON> (Kadagam)
    5: 'SUN',       # <PERSON> (Simmam)
    6: 'MERCURY',   # <PERSON><PERSON><PERSON> (Kanni)
    7: 'VENUS',     # <PERSON><PERSON> (Thulam)
    8: 'MARS',      # <PERSON><PERSON><PERSON> (Virichigam)
    9: 'JUPITER',   # <PERSON><PERSON><PERSON><PERSON> (Dhanusu)
    10: 'SATURN',   # Capricorn (Magaram)
    11: 'SATURN',   # A<PERSON><PERSON> (Kumbam)
    12: 'JUPITER'   # Pisces (Meenam)
}

# Reverse mapping: planets to houses they rule
PLANET_RULED_HOUSES = {
    'MARS': [1, 8],
    'VENUS': [2, 7],
    'MERCURY': [3, 6],
    'MOON': [4],
    'SUN': [5],
    'JUPITER': [9, 12],
    'SATURN': [10, 11],
    'RAHU': [],  # Shadow planets don't rule houses in traditional system
    'KETU': []
}

# Sign names corresponding to houses (Tamil names in English)
HOUSE_SIGN_NAMES = {
    1: 'Mesham',      # Aries
    2: 'Rishabam',    # Taurus
    3: 'Midunam',     # Gemini
    4: 'Kadagam',     # Cancer
    5: 'Simmam',      # Leo
    6: 'Kanni',       # Virgo
    7: 'Thulam',      # Libra
    8: 'Virichigam',  # Scorpio
    9: 'Dhanusu',     # Sagittarius
    10: 'Magaram',    # Capricorn
    11: 'Kumbam',     # Aquarius
    12: 'Meenam'      # Pisces
}

def get_house_ruling_planet(house_number: int) -> Optional[str]:
    """
    Get the ruling planet for a specific house number.
    
    Args:
        house_number: House number (1-12)
        
    Returns:
        Ruling planet name or None if invalid house number
    """
    if not isinstance(house_number, int) or house_number < 1 or house_number > 12:
        return None
    
    return HOUSE_RULING_PLANETS.get(house_number)

def get_houses_ruled_by_planet(planet: str) -> List[int]:
    """
    Get the house numbers ruled by a specific planet.
    
    Args:
        planet: Planet name
        
    Returns:
        List of house numbers ruled by the planet
    """
    if not planet:
        return []
    
    planet = planet.upper().strip()
    return PLANET_RULED_HOUSES.get(planet, [])

def get_house_sign_name(house_number: int) -> Optional[str]:
    """
    Get the sign name for a specific house number.
    
    Args:
        house_number: House number (1-12)
        
    Returns:
        Sign name or None if invalid house number
    """
    if not isinstance(house_number, int) or house_number < 1 or house_number > 12:
        return None
    
    return HOUSE_SIGN_NAMES.get(house_number)

def get_house_number_from_sign(sign_name: str) -> Optional[int]:
    """
    Get the house number for a specific sign name.
    
    Args:
        sign_name: Sign name (Tamil name in English)
        
    Returns:
        House number or None if sign not found
    """
    if not sign_name:
        return None
    
    sign_name = sign_name.strip()
    
    # Search for the sign (case-insensitive)
    for house_num, sign in HOUSE_SIGN_NAMES.items():
        if sign.upper() == sign_name.upper():
            return house_num
    
    return None

def is_planet_ruling_house(planet: str, house_number: int) -> bool:
    """
    Check if a planet rules a specific house.
    
    Args:
        planet: Planet name
        house_number: House number (1-12)
        
    Returns:
        True if planet rules the house
    """
    if not planet or not isinstance(house_number, int):
        return False
    
    ruling_planet = get_house_ruling_planet(house_number)
    return ruling_planet and ruling_planet.upper() == planet.upper().strip()

def get_opposite_house(house_number: int) -> Optional[int]:
    """
    Get the opposite house number (7th house from given house).
    
    Args:
        house_number: House number (1-12)
        
    Returns:
        Opposite house number or None if invalid input
    """
    if not isinstance(house_number, int) or house_number < 1 or house_number > 12:
        return None
    
    # Calculate 7th house from given house
    opposite = ((house_number - 1 + 6) % 12) + 1
    return opposite

def get_trine_houses(house_number: int) -> List[int]:
    """
    Get the trine houses (1st, 5th, 9th from given house).
    
    Args:
        house_number: House number (1-12)
        
    Returns:
        List of trine house numbers
    """
    if not isinstance(house_number, int) or house_number < 1 or house_number > 12:
        return []
    
    trines = []
    for offset in [0, 4, 8]:  # 1st, 5th, 9th positions
        trine_house = ((house_number - 1 + offset) % 12) + 1
        trines.append(trine_house)
    
    return trines

def get_quadrant_houses(house_number: int) -> List[int]:
    """
    Get the quadrant houses (1st, 4th, 7th, 10th from given house).
    
    Args:
        house_number: House number (1-12)
        
    Returns:
        List of quadrant house numbers
    """
    if not isinstance(house_number, int) or house_number < 1 or house_number > 12:
        return []
    
    quadrants = []
    for offset in [0, 3, 6, 9]:  # 1st, 4th, 7th, 10th positions
        quadrant_house = ((house_number - 1 + offset) % 12) + 1
        quadrants.append(quadrant_house)
    
    return quadrants

def get_house_relationships() -> Dict[str, Dict[int, List[int]]]:
    """
    Get comprehensive house relationship mappings.
    
    Returns:
        Dictionary with different house relationship types
    """
    relationships = {
        'ruling_planets': HOUSE_RULING_PLANETS,
        'planet_ruled_houses': PLANET_RULED_HOUSES,
        'sign_names': HOUSE_SIGN_NAMES,
        'opposites': {},
        'trines': {},
        'quadrants': {}
    }
    
    # Calculate relationships for all houses
    for house in range(1, 13):
        relationships['opposites'][house] = get_opposite_house(house)
        relationships['trines'][house] = get_trine_houses(house)
        relationships['quadrants'][house] = get_quadrant_houses(house)
    
    return relationships

def validate_house_system() -> Dict[str, bool]:
    """
    Validate the house system for consistency.
    
    Returns:
        Dictionary with validation results
    """
    validation = {
        'all_houses_have_rulers': True,
        'all_planets_rule_houses': True,
        'no_duplicate_rulers': True,
        'valid_house_numbers': True
    }
    
    # Check if all houses have ruling planets
    for house in range(1, 13):
        if get_house_ruling_planet(house) is None:
            validation['all_houses_have_rulers'] = False
            break
    
    # Check if house numbers are valid
    for house in HOUSE_RULING_PLANETS.keys():
        if not (1 <= house <= 12):
            validation['valid_house_numbers'] = False
            break
    
    # Check if major planets rule at least one house
    major_planets = ['SUN', 'MOON', 'MARS', 'MERCURY', 'JUPITER', 'VENUS', 'SATURN']
    for planet in major_planets:
        if not get_houses_ruled_by_planet(planet):
            validation['all_planets_rule_houses'] = False
            break
    
    return validation
