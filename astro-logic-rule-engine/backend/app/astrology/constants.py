"""
AstroLogic Rule Engine - Astrological Constants

This module contains all astrological constants including planet names,
sign names, nakshatra lords, and other essential mappings used throughout
the rule engine for consistent astrological calculations.
"""

from typing import Dict, List

# Standardized planet names mapping
PLANET_NAMES = {
    'SUN': 'SUN',
    'MOON': 'MO<PERSON>',
    'MARS': 'MARS',
    'MERCURY': 'MERCURY',
    'JUPIT<PERSON>': 'JUPITER',
    'VENUS': 'VENUS',
    'SATURN': 'SATURN',
    'RAHU': 'RAHU',
    'KETU': 'KETU'
}

# Alternative planet name mappings for normalization
PLANET_NAME_VARIATIONS = {
    'JUPITER': ['GURU', 'BRIHA<PERSON>AT<PERSON>'],
    'VENUS': ['SHUKRA'],
    'SATURN': ['SHANI'],
    'MARS': ['MANGAL', 'ANGARAKA'],
    'MERCURY': ['BUDHA'],
    'SUN': ['SURYA', 'RA<PERSON>'],
    'MOON': ['CHANDRA']
}

# Standardized sign names (Tamil names in English)
SIGN_NAMES = [
    '<PERSON><PERSON>',      # Aries
    'Rishabam',    # <PERSON>rus
    'Midunam',     # <PERSON>
    'Kadagam',     # Cancer
    'Simmam',      # Leo
    'Kanni',       # Virgo
    'Thulam',      # Libra
    'Virichigam',  # Scorpio
    'Dhanusu',     # Sagittarius
    'Magaram',     # Capricorn
    'Kumbam',      # Aquarius
    'Meenam'       # Pisces
]

# Sign to number mapping (0-based for calculations)
SIGN_TO_NUMBER = {
    'Mesham': 0, 'Rishabam': 1, 'Midunam': 2, 'Kadagam': 3,
    'Simmam': 4, 'Kanni': 5, 'Thulam': 6, 'Virichigam': 7,
    'Dhanusu': 8, 'Magaram': 9, 'Kumbam': 10, 'Meenam': 11
}

# Sign to starting degree mapping
SIGN_TO_DEGREE = {
    'Mesham': 0, 'Rishabam': 30, 'Midunam': 60, 'Kadagam': 90,
    'Simmam': 120, 'Kanni': 150, 'Thulam': 180, 'Virichigam': 210,
    'Dhanusu': 240, 'Magaram': 270, 'Kumbam': 300, 'Meenam': 330
}

# Nakshatra (star) lords mapping
NAKSHATRA_LORDS = {
    'ASHWINI': 'KETU',
    'BHARANI': 'VENUS',
    'KRITTIKA': 'SUN',
    'ROHINI': 'MOON',
    'MRIGASHIRA': 'MARS',
    'ARDRA': 'RAHU',
    'PUNARVASU': 'JUPITER',
    'PUSHYA': 'SATURN',
    'ASHLESHA': 'MERCURY',
    'MAGHA': 'KETU',
    'PURVA_PHALGUNI': 'VENUS',
    'UTTARA_PHALGUNI': 'SUN',
    'HASTA': 'MOON',
    'CHITRA': 'MARS',
    'SWATI': 'RAHU',
    'VISHAKHA': 'JUPITER',
    'ANURADHA': 'SATURN',
    'JYESHTHA': 'MERCURY',
    'MULA': 'KETU',
    'PURVA_ASHADHA': 'VENUS',
    'UTTARA_ASHADHA': 'SUN',
    'SHRAVANA': 'MOON',
    'DHANISHTA': 'MARS',
    'SHATABHISHA': 'RAHU',
    'PURVA_BHADRAPADA': 'JUPITER',
    'UTTARA_BHADRAPADA': 'SATURN',
    'REVATI': 'MERCURY'
}

# Planet transit speeds (degrees per day - approximate)
PLANET_SPEEDS = {
    'SUN': 1.0,
    'MOON': 13.2,
    'MARS': 0.5,
    'MERCURY': 1.4,
    'JUPITER': 0.083,
    'VENUS': 1.2,
    'SATURN': 0.033,
    'RAHU': -0.053,  # Retrograde motion
    'KETU': -0.053   # Retrograde motion
}

# House names in traditional astrology
HOUSE_NAMES = [
    'Lagna',    # 1st house - Ascendant
    'Dhana',    # 2nd house - Wealth
    'Sahaja',   # 3rd house - Siblings
    'Sukha',    # 4th house - Happiness
    'Putra',    # 5th house - Children
    'Ari',      # 6th house - Enemies
    'Kalatra',  # 7th house - Spouse
    'Ayu',      # 8th house - Longevity
    'Bhagya',   # 9th house - Fortune
    'Karma',    # 10th house - Career
    'Labha',    # 11th house - Gains
    'Vyaya'     # 12th house - Losses
]

# Chart types supported by the system
CHART_TYPES = [
    'D1',   # Rasi Chart (Main birth chart)
    'D2',   # Hora Chart
    'D3',   # Drekkana Chart
    'D4',   # Chaturthamsa Chart
    'D7',   # Saptamsa Chart
    'D9',   # Navamsa Chart
    'D10',  # Dasamsa Chart
    'D12',  # Dwadasamsa Chart
    'D16',  # Shodasamsa Chart
    'D20',  # Vimsamsa Chart
    'D24',  # Chaturvimsamsa Chart
    'D27',  # Nakshatramsa Chart
    'D30'   # Trimsamsa Chart
]

# Success rating thresholds
SUCCESS_RATINGS = {
    (90, 100): "Excellent",
    (80, 89): "Very Good",
    (70, 79): "Good", 
    (60, 69): "Average",
    (50, 59): "Below Average",
    (0, 49): "Poor"
}

# Logical operators with precedence
LOGICAL_OPERATORS = {
    'NOT': 3,   # Highest precedence
    'AND': 2,   # Medium precedence
    'OR': 1     # Lowest precedence
}

# Relationship operators
RELATIONSHIP_OPERATORS = [
    'IS_RELATED_TO',
    'IS_ASPECTING_BIRTH',
    'WITH_RULING_PLANET',
    'TOGETHER_WITH',
    'WITH_STARS_OF',
    'IN',
    'NOT_IN'
]

# Dasha patterns for query routing
DASHA_PATTERNS = [
    'Bhukti_Dates',
    'Dasa_Dates', 
    'House_Ruling_Planet Bhukti_Dates',
    'House_Ruling_Planet Dasa_Dates',
    'VENUS Bhukti_Dates',
    'JUPITER Bhukti_Dates',
    'Member_Age',
    'PREDICTION_DURATION'
]

# Default configuration values
DEFAULT_PREDICTION_DURATION = 2  # years
DEFAULT_CHART_TYPE = 'D1'
MAX_QUERY_LENGTH = 1000
MAX_CONDITIONS_PER_QUERY = 50

def get_nakshatra_lord(nakshatra: str) -> str:
    """
    Get the ruling planet for a nakshatra.
    
    Args:
        nakshatra: Nakshatra name
        
    Returns:
        Ruling planet name or 'UNKNOWN' if not found
    """
    if not nakshatra:
        return 'UNKNOWN'
    
    nakshatra = nakshatra.upper().strip()
    return NAKSHATRA_LORDS.get(nakshatra, 'UNKNOWN')

def get_nakshatras_ruled_by_planet(planet: str) -> List[str]:
    """
    Get all nakshatras ruled by a specific planet.
    
    Args:
        planet: Planet name
        
    Returns:
        List of nakshatra names ruled by the planet
    """
    if not planet:
        return []
    
    planet = planet.upper().strip()
    return [nakshatra for nakshatra, lord in NAKSHATRA_LORDS.items() if lord == planet]

def normalize_planet_name(planet_name: str) -> str:
    """
    Normalize planet name to standard format.
    
    Args:
        planet_name: Planet name in any format
        
    Returns:
        Normalized planet name
    """
    if not planet_name:
        return ""
    
    planet_name = planet_name.strip().upper()
    
    # Check if already standard
    if planet_name in PLANET_NAMES:
        return planet_name
    
    # Check variations
    for standard_name, variants in PLANET_NAME_VARIATIONS.items():
        if planet_name in variants:
            return standard_name
    
    return planet_name

def get_sign_number(sign_name: str) -> int:
    """
    Get the number (0-11) for a sign name.
    
    Args:
        sign_name: Sign name
        
    Returns:
        Sign number or -1 if not found
    """
    if not sign_name:
        return -1
    
    return SIGN_TO_NUMBER.get(sign_name.strip(), -1)

def get_sign_degree(sign_name: str) -> int:
    """
    Get the starting degree for a sign.
    
    Args:
        sign_name: Sign name
        
    Returns:
        Starting degree or -1 if not found
    """
    if not sign_name:
        return -1
    
    return SIGN_TO_DEGREE.get(sign_name.strip(), -1)
