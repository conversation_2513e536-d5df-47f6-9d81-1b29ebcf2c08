"""
AstroLogic Rule Engine - Planetary Relationships

This module contains planetary relationships in Vedic astrology including
exaltation, debilitation, friendship, enmity, aspects, and relationship functions.
Extracted from the Fortune Lens project for standalone use.
"""

from typing import List, Dict, Optional

# Planetary Exaltation Signs
EXALTATION = {
    'SUN': 'MESHAM',
    'MOON': 'RISHABA<PERSON>', 
    'MARS': 'MAGARAM',
    'MERCURY': '<PERSON>AN<PERSON>',
    'JUPITER': 'KADAGAM',
    'VENUS': 'MEENAM',
    'SATURN': 'THULAM',
    'RAHU': 'DHANUSU',
    'KETU': 'MIDUNAM'
}

# Planetary Debilitation Signs
DEBILITATION = {
    'SUN': 'THULAM',
    'MOON': 'VIRICHIGAM',
    'MARS': 'KADAGAM', 
    'MERCURY': 'MEENAM',
    'JUPITER': 'MAGARAM',
    'VENUS': 'KANNI',
    'SATURN': 'MESHAM',
    'RAHU': 'MIDUNAM',
    'KETU': 'DHANU<PERSON>'
}

# Planetary Friendships
FRIENDS = {
    'SUN': ['MOON', 'MARS', 'J<PERSON>ITER'],
    'MOON': ['SUN', 'MERCURY'],
    'MARS': ['SUN', 'MOON', 'JUPITER'],
    'MERCURY': ['SUN', 'VENUS'],
    'JUPITER': ['SUN', 'MOON', 'MARS'],
    'VENUS': ['SATURN', 'MERCURY'],
    'SATURN': ['MERCURY', 'VENUS'],
    'RAHU': ['VENUS', 'SATURN'],
    'KETU': ['VENUS', 'SATURN']
}

# Planetary Enmities
ENEMIES = {
    'SUN': ['SATURN', 'VENUS'],
    'MOON': [],
    'MARS': ['MERCURY'],
    'MERCURY': ['MOON'],
    'JUPITER': ['VENUS', 'MERCURY'],
    'VENUS': ['SUN', 'MOON', 'MARS', 'JUPITER', 'RAHU', 'KETU'],
    'SATURN': ['SUN', 'MOON', 'MARS', 'JUPITER', 'RAHU', 'KETU'],
    'RAHU': ['SUN', 'MOON', 'MARS'],
    'KETU': ['SUN', 'MOON', 'MARS']
}

# Planetary Neutral Relationships
NEUTRAL = {
    'SUN': ['MERCURY'],
    'MOON': ['MARS', 'JUPITER', 'VENUS', 'SATURN', 'RAHU', 'KETU'],
    'MARS': ['VENUS', 'SATURN', 'RAHU', 'KETU'],
    'MERCURY': ['VENUS', 'SATURN'],
    'JUPITER': ['SATURN'],
    'VENUS': ['JUPITER', 'MARS'],
    'SATURN': ['JUPITER'],
    'RAHU': ['JUPITER', 'MERCURY'],
    'KETU': ['JUPITER', 'MERCURY']
}

# Planetary Aspects (houses aspected from current position)
ASPECTS = {
    'SUN': [7],
    'MOON': [7],
    'MERCURY': [7],
    'VENUS': [7],
    'SATURN': [3, 7, 10],
    'MARS': [4, 7, 8],
    'JUPITER': [5, 7, 9],
    'RAHU': [5, 7, 9],  # Same as Jupiter
    'KETU': [5, 7, 9]   # Same as Jupiter
}

def get_planet_relationship(planet1: str, planet2: str) -> str:
    """
    Get the relationship between two planets.
    
    Args:
        planet1: The first planet name
        planet2: The second planet name
        
    Returns:
        'FRIEND', 'ENEMY', or 'NEUTRAL'
    """
    if not planet1 or not planet2:
        return 'NEUTRAL'
    
    planet1 = planet1.upper().strip()
    planet2 = planet2.upper().strip()
    
    # Same planet
    if planet1 == planet2:
        return 'FRIEND'
    
    # Check friendship
    if planet2 in FRIENDS.get(planet1, []):
        return 'FRIEND'
    
    # Check enmity
    if planet2 in ENEMIES.get(planet1, []):
        return 'ENEMY'
    
    # Default to neutral
    return 'NEUTRAL'

def get_planet_aspects(planet: str) -> List[int]:
    """
    Get the houses that a planet aspects from its current position.
    
    Args:
        planet: The planet name
        
    Returns:
        List of house numbers that the planet aspects
    """
    if not planet:
        return []
    
    planet = planet.upper().strip()
    return ASPECTS.get(planet, [])

def is_planet_aspecting_planet(aspecting_planet: str, aspected_planet: str, 
                              aspecting_house: int, aspected_house: int) -> bool:
    """
    Check if one planet is aspecting another planet based on their house positions.
    
    Args:
        aspecting_planet: Planet doing the aspecting
        aspected_planet: Planet being aspected
        aspecting_house: House number of aspecting planet (1-12)
        aspected_house: House number of aspected planet (1-12)
        
    Returns:
        True if aspecting planet aspects the aspected planet
    """
    if not all([aspecting_planet, aspected_planet, aspecting_house, aspected_house]):
        return False
    
    try:
        aspecting_planet = aspecting_planet.upper().strip()
        aspected_house = int(aspected_house)
        aspecting_house = int(aspecting_house)
        
        # Validate house numbers
        if not (1 <= aspecting_house <= 12) or not (1 <= aspected_house <= 12):
            return False
        
        # Get aspect houses for the aspecting planet
        aspect_houses = get_planet_aspects(aspecting_planet)
        
        # Calculate which houses are aspected from the aspecting planet's position
        aspected_houses = []
        for aspect_offset in aspect_houses:
            aspected_house_num = ((aspecting_house - 1 + aspect_offset - 1) % 12) + 1
            aspected_houses.append(aspected_house_num)
        
        return aspected_house in aspected_houses
        
    except (ValueError, TypeError):
        return False

def get_exaltation_sign(planet: str) -> Optional[str]:
    """
    Get the exaltation sign for a planet.
    
    Args:
        planet: Planet name
        
    Returns:
        Exaltation sign name or None if not found
    """
    if not planet:
        return None
    
    return EXALTATION.get(planet.upper().strip())

def get_debilitation_sign(planet: str) -> Optional[str]:
    """
    Get the debilitation sign for a planet.
    
    Args:
        planet: Planet name
        
    Returns:
        Debilitation sign name or None if not found
    """
    if not planet:
        return None
    
    return DEBILITATION.get(planet.upper().strip())

def is_planet_exalted(planet: str, sign: str) -> bool:
    """
    Check if a planet is exalted in a given sign.
    
    Args:
        planet: Planet name
        sign: Sign name
        
    Returns:
        True if planet is exalted in the sign
    """
    if not planet or not sign:
        return False
    
    exaltation_sign = get_exaltation_sign(planet)
    return exaltation_sign and exaltation_sign.upper() == sign.upper().strip()

def is_planet_debilitated(planet: str, sign: str) -> bool:
    """
    Check if a planet is debilitated in a given sign.
    
    Args:
        planet: Planet name
        sign: Sign name
        
    Returns:
        True if planet is debilitated in the sign
    """
    if not planet or not sign:
        return False
    
    debilitation_sign = get_debilitation_sign(planet)
    return debilitation_sign and debilitation_sign.upper() == sign.upper().strip()

def get_planetary_strength(planet: str, sign: str) -> str:
    """
    Get the strength of a planet in a given sign.
    
    Args:
        planet: Planet name
        sign: Sign name
        
    Returns:
        'EXALTED', 'DEBILITATED', 'OWN_SIGN', 'FRIEND_SIGN', 'ENEMY_SIGN', or 'NEUTRAL'
    """
    if not planet or not sign:
        return 'NEUTRAL'
    
    planet = planet.upper().strip()
    sign = sign.upper().strip()
    
    # Check exaltation
    if is_planet_exalted(planet, sign):
        return 'EXALTED'
    
    # Check debilitation
    if is_planet_debilitated(planet, sign):
        return 'DEBILITATED'
    
    # For more detailed analysis, we would need house ruling planet information
    # This is a simplified version
    return 'NEUTRAL'
