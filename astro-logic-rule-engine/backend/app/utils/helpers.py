"""
AstroLogic Rule Engine - Helper Functions

This module contains utility helper functions used throughout the application
for common operations like success rating calculation, data normalization, etc.
"""

from typing import Dict, Any, Optional
from app.astrology.constants import SUCCESS_RATINGS, PLANET_NAME_VARIATIONS

def get_success_rating(percentage: float) -> str:
    """
    Get success rating based on percentage.
    
    Args:
        percentage: Success percentage (0-100)
        
    Returns:
        Success rating string
    """
    if not isinstance(percentage, (int, float)):
        return "Unknown"
    
    for (min_val, max_val), rating in SUCCESS_RATINGS.items():
        if min_val <= percentage <= max_val:
            return rating
    
    return "Unknown"

def normalize_planet_name(planet_name: str) -> str:
    """
    Normalize planet name to standard format.
    
    Args:
        planet_name: Planet name in any format
        
    Returns:
        Normalized planet name
    """
    if not planet_name:
        return ""
    
    planet_name = planet_name.strip().upper()
    
    # Check variations
    for standard_name, variants in PLANET_NAME_VARIATIONS.items():
        if planet_name in variants:
            return standard_name
    
    return planet_name

def normalize_house_number(house_input: Any) -> Optional[int]:
    """
    Normalize house input to integer.
    
    Args:
        house_input: House number as string or int
        
    Returns:
        House number (1-12) or None if invalid
    """
    try:
        if isinstance(house_input, str):
            # Extract number from strings like "1st_House", "7th_House_Planet"
            import re
            match = re.search(r'(\d+)', house_input)
            if match:
                house_num = int(match.group(1))
            else:
                return None
        else:
            house_num = int(house_input)
        
        # Validate house number range
        if 1 <= house_num <= 12:
            return house_num
        return None
    except (ValueError, TypeError):
        return None

def format_duration(days: int) -> Dict[str, Any]:
    """
    Format duration in days to human-readable format.
    
    Args:
        days: Number of days
        
    Returns:
        Formatted duration dictionary
    """
    if not days or days < 0:
        return {"days": 0, "months": 0, "years": 0, "formatted": "0 days"}
    
    years = days // 365
    remaining_days = days % 365
    months = remaining_days // 30
    remaining_days = remaining_days % 30
    
    return {
        "days": remaining_days,
        "months": months,
        "years": years,
        "formatted": f"{years} years, {months} months, {remaining_days} days"
    }

def validate_chart_type(chart_type: str) -> str:
    """
    Validate chart type.
    
    Args:
        chart_type: Chart type string
        
    Returns:
        Valid chart type or 'D1' as default
    """
    from app.astrology.constants import CHART_TYPES
    
    if not chart_type:
        return 'D1'
    
    chart_type = chart_type.upper()
    return chart_type if chart_type in CHART_TYPES else 'D1'

def safe_get(dictionary: Dict[str, Any], key: str, default: Any = None) -> Any:
    """
    Safely get value from dictionary with default.
    
    Args:
        dictionary: Dictionary to get value from
        key: Key to look for
        default: Default value if key not found
        
    Returns:
        Value from dictionary or default
    """
    if not isinstance(dictionary, dict):
        return default
    
    return dictionary.get(key, default)

def calculate_percentage(numerator: int, denominator: int) -> float:
    """
    Calculate percentage with safe division.
    
    Args:
        numerator: Numerator value
        denominator: Denominator value
        
    Returns:
        Percentage value (0-100)
    """
    if not denominator or denominator == 0:
        return 0.0
    
    try:
        return round((numerator / denominator) * 100, 2)
    except (TypeError, ZeroDivisionError):
        return 0.0

def format_planet_list(planets: list) -> str:
    """
    Format a list of planets into a readable string.
    
    Args:
        planets: List of planet names
        
    Returns:
        Formatted string
    """
    if not planets:
        return "None"
    
    if len(planets) == 1:
        return planets[0]
    elif len(planets) == 2:
        return f"{planets[0]} and {planets[1]}"
    else:
        return f"{', '.join(planets[:-1])}, and {planets[-1]}"

def format_house_list(houses: list) -> str:
    """
    Format a list of house numbers into a readable string.
    
    Args:
        houses: List of house numbers
        
    Returns:
        Formatted string
    """
    if not houses:
        return "None"
    
    # Sort house numbers
    sorted_houses = sorted(houses)
    house_strings = [f"{h}th" for h in sorted_houses]
    
    if len(house_strings) == 1:
        return house_strings[0]
    elif len(house_strings) == 2:
        return f"{house_strings[0]} and {house_strings[1]}"
    else:
        return f"{', '.join(house_strings[:-1])}, and {house_strings[-1]}"

def create_condition_summary(condition_type: str, planet: str, 
                           target: str, result: bool) -> str:
    """
    Create a human-readable summary of a condition evaluation.
    
    Args:
        condition_type: Type of condition
        planet: Planet name
        target: Target (house, planet, etc.)
        result: Evaluation result
        
    Returns:
        Human-readable summary
    """
    status = "✅ Satisfied" if result else "❌ Not satisfied"
    
    if condition_type == 'planet_house':
        return f"{planet} in {target}: {status}"
    elif condition_type == 'relationship':
        return f"{planet} relationship with {target}: {status}"
    else:
        return f"{condition_type}: {status}"

def extract_numbers_from_string(text: str) -> list:
    """
    Extract all numbers from a string.
    
    Args:
        text: Input string
        
    Returns:
        List of numbers found
    """
    import re
    if not text:
        return []
    
    numbers = re.findall(r'\d+', text)
    return [int(num) for num in numbers]

def is_valid_planet(planet_name: str) -> bool:
    """
    Check if a planet name is valid.
    
    Args:
        planet_name: Planet name to check
        
    Returns:
        True if valid planet name
    """
    from app.astrology.constants import PLANET_NAMES
    
    if not planet_name:
        return False
    
    normalized = normalize_planet_name(planet_name)
    return normalized in PLANET_NAMES

def is_valid_house(house_number: Any) -> bool:
    """
    Check if a house number is valid.
    
    Args:
        house_number: House number to check
        
    Returns:
        True if valid house number (1-12)
    """
    normalized = normalize_house_number(house_number)
    return normalized is not None
