"""
AstroLogic Rule Engine - Response Formatter

This module handles formatting of API responses with consistent structure
and comprehensive information for query results and errors.
"""

from typing import Dict, Any, List, Optional
from flask import jsonify
import time

def format_success_response(data: Dict[str, Any], status_code: int = 200) -> tuple:
    """
    Format a successful API response.
    
    Args:
        data: Response data
        status_code: HTTP status code
        
    Returns:
        Tuple of (response, status_code)
    """
    response = {
        'success': True,
        'timestamp': time.time(),
        **data
    }
    
    return jsonify(response), status_code

def format_error_response(message: str, status_code: int = 400, 
                         details: Optional[Any] = None) -> tuple:
    """
    Format an error API response.
    
    Args:
        message: Error message
        status_code: HTTP status code
        details: Additional error details
        
    Returns:
        Tuple of (response, status_code)
    """
    response = {
        'success': False,
        'error': message,
        'timestamp': time.time(),
        'status_code': status_code
    }
    
    if details:
        response['details'] = details
    
    return jsonify(response), status_code

def format_query_result(query: str, final_result: bool, parsed_conditions: List[Any],
                       evaluation_results: List[Dict[str, Any]], 
                       success_metrics: Dict[str, Any], chart_data: Dict[str, Any],
                       chart_type: str, processing_time: float) -> Dict[str, Any]:
    """
    Format the complete query evaluation result.
    
    Args:
        query: Original query string
        final_result: Final boolean result
        parsed_conditions: List of parsed conditions
        evaluation_results: List of evaluation results
        success_metrics: Success metrics dictionary
        chart_data: Chart data used
        chart_type: Chart type
        processing_time: Processing time in milliseconds
        
    Returns:
        Formatted result dictionary
    """
    return {
        'success': True,
        'query': query,
        'result': final_result,
        'chart_type': chart_type,
        'success_rate': success_metrics.get('success_rate', 0),
        'success_rating': success_metrics.get('success_rating', 'Unknown'),
        'query_analysis': {
            'total_conditions': success_metrics.get('total_conditions', 0),
            'successful_conditions': success_metrics.get('successful_conditions', 0),
            'final_result': final_result,
            'logical_structure': _extract_logical_structure(parsed_conditions),
            'condition_breakdown': _format_condition_breakdown(evaluation_results)
        },
        'detailed_results': {
            'individual_conditions': _format_individual_conditions(evaluation_results),
            'evaluation_summary': success_metrics.get('evaluation_summary', {}),
            'chart_summary': _format_chart_summary(chart_data, chart_type)
        },
        'metadata': {
            'processing_time_ms': processing_time,
            'api_version': '1.0.0',
            'engine_version': 'AstroLogic v1.0.0'
        }
    }

def _extract_logical_structure(parsed_conditions: List[Any]) -> Dict[str, Any]:
    """Extract logical structure from parsed conditions."""
    structure = {
        'has_logical_operators': False,
        'operators_used': [],
        'condition_count': len(parsed_conditions),
        'complexity': 'simple'
    }
    
    for condition in parsed_conditions:
        if hasattr(condition, 'condition_type') and condition.condition_type == 'logical':
            structure['has_logical_operators'] = True
            if hasattr(condition, 'logical_operator') and condition.logical_operator:
                if condition.logical_operator not in structure['operators_used']:
                    structure['operators_used'].append(condition.logical_operator)
    
    # Determine complexity
    if structure['has_logical_operators']:
        if len(structure['operators_used']) > 1:
            structure['complexity'] = 'complex'
        else:
            structure['complexity'] = 'moderate'
    
    return structure

def _format_condition_breakdown(evaluation_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Format condition breakdown summary."""
    breakdown = {
        'total_evaluated': len(evaluation_results),
        'successful': 0,
        'failed': 0,
        'errors': 0,
        'by_type': {}
    }
    
    for result in evaluation_results:
        result_value = result.get('result')
        result_type = result.get('type', 'unknown')
        
        # Count by result
        if result_value is True:
            breakdown['successful'] += 1
        elif result_value is False:
            breakdown['failed'] += 1
        
        # Count errors
        if result.get('error'):
            breakdown['errors'] += 1
        
        # Count by type
        if result_type not in breakdown['by_type']:
            breakdown['by_type'][result_type] = {
                'total': 0,
                'successful': 0,
                'failed': 0
            }
        
        breakdown['by_type'][result_type]['total'] += 1
        if result_value is True:
            breakdown['by_type'][result_type]['successful'] += 1
        elif result_value is False:
            breakdown['by_type'][result_type]['failed'] += 1
    
    return breakdown

def _format_individual_conditions(evaluation_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Format individual condition results."""
    formatted_conditions = []
    
    for i, result in enumerate(evaluation_results):
        formatted_condition = {
            'condition_number': i + 1,
            'condition': result.get('condition', 'Unknown'),
            'type': result.get('type', 'unknown'),
            'result': result.get('result'),
            'success': result.get('result') is True,
            'summary': _create_condition_summary(result)
        }
        
        # Add details if available
        if result.get('details'):
            formatted_condition['details'] = result['details']
        
        # Add error if present
        if result.get('error'):
            formatted_condition['error'] = result['error']
        
        # Add relationship type if available
        if result.get('relationship_type'):
            formatted_condition['relationship_type'] = result['relationship_type']
        
        formatted_conditions.append(formatted_condition)
    
    return formatted_conditions

def _create_condition_summary(result: Dict[str, Any]) -> str:
    """Create a human-readable summary for a condition result."""
    condition = result.get('condition', 'Unknown condition')
    result_value = result.get('result')
    condition_type = result.get('type', 'unknown')
    
    if result_value is True:
        status = "✅ Satisfied"
    elif result_value is False:
        status = "❌ Not satisfied"
    else:
        status = "⚠️ Unknown"
    
    if result.get('error'):
        status += f" (Error: {result['error']})"
    
    return f"{condition}: {status}"

def _format_chart_summary(chart_data: Dict[str, Any], chart_type: str) -> Dict[str, Any]:
    """Format chart data summary."""
    summary = {
        'chart_type': chart_type,
        'data_available': bool(chart_data),
        'chart_info': {}
    }
    
    if chart_data:
        # Extract basic chart information
        summary['chart_info'] = {
            'has_planet_positions': 'planets' in chart_data or 'houses' in chart_data,
            'data_source': 'sample_data',  # In real implementation, this would be dynamic
            'chart_system': 'vedic'
        }
        
        # Add planet count if available
        if 'planets' in chart_data:
            summary['chart_info']['planet_count'] = len(chart_data['planets'])
        
        # Add house count if available
        if 'houses' in chart_data:
            summary['chart_info']['house_count'] = len(chart_data['houses'])
    
    return summary

def format_validation_response(query: str, validation_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format query validation response.
    
    Args:
        query: Original query
        validation_result: Validation result dictionary
        
    Returns:
        Formatted validation response
    """
    return {
        'success': True,
        'query': query,
        'validation': {
            'is_valid': validation_result.get('is_valid', False),
            'errors': validation_result.get('errors', []),
            'warnings': validation_result.get('warnings', []),
            'parsed_conditions': validation_result.get('parsed_conditions', [])
        },
        'suggestions': _generate_query_suggestions(validation_result)
    }

def _generate_query_suggestions(validation_result: Dict[str, Any]) -> List[str]:
    """Generate helpful suggestions based on validation results."""
    suggestions = []
    
    errors = validation_result.get('errors', [])
    
    for error in errors:
        if 'planet' in error.lower():
            suggestions.append("Check planet names - valid planets: SUN, MOON, MARS, MERCURY, JUPITER, VENUS, SATURN, RAHU, KETU")
        elif 'house' in error.lower():
            suggestions.append("Check house numbers - valid range: 1-12")
        elif 'operator' in error.lower():
            suggestions.append("Check operators - valid operators: IN, NOT_IN, WITH_RULING_PLANET, TOGETHER_WITH, IS_ASPECTING_BIRTH, WITH_STARS_OF")
        elif 'parentheses' in error.lower():
            suggestions.append("Check parentheses - ensure they are balanced")
    
    if not suggestions:
        suggestions.append("Query structure looks good!")
    
    return suggestions
