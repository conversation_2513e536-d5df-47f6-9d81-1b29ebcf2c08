"""
AstroLogic Rule Engine - Flask Application Factory

This module contains the Flask application factory and configuration setup.
It initializes all the necessary components for the rule engine application.
"""

from flask import Flask
from flask_cors import CORS
from flask_jwt_extended import J<PERSON>TManager
import os
from datetime import <PERSON><PERSON><PERSON>

def create_app(config_name=None):
    """
    Create and configure the Flask application.
    
    Args:
        config_name (str): Configuration name ('development', 'production', 'testing')
        
    Returns:
        Flask: Configured Flask application instance
    """
    # Create Flask application instance
    app = Flask(__name__)
    
    # Load configuration
    config_name = config_name or os.environ.get('FLASK_ENV', 'development')
    app.config.from_object(f'app.config.{config_name.title()}Config')
    
    # Initialize extensions
    init_extensions(app)
    
    # Register blueprints
    register_blueprints(app)
    
    # Register error handlers
    register_error_handlers(app)
    
    # Add custom CLI commands
    register_cli_commands(app)
    
    return app

def init_extensions(app):
    """Initialize Flask extensions."""
    # Enable CORS for all routes
    CORS(app, resources={
        r"/api/*": {
            "origins": ["http://localhost:3000", "http://127.0.0.1:3000"],
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
    
    # Initialize JWT Manager
    jwt = JWTManager(app)
    
    # JWT configuration
    app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY', 'astro-logic-secret-key')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)
    
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return {'message': 'Token has expired'}, 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return {'message': 'Invalid token'}, 401

def register_blueprints(app):
    """Register application blueprints."""
    from app.api import api_bp
    
    # Register API blueprint
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # Add a simple health check route
    @app.route('/health')
    def health_check():
        return {
            'status': 'healthy',
            'service': 'AstroLogic Rule Engine',
            'version': '1.0.0'
        }
    
    # Add root route with API information
    @app.route('/')
    def root():
        return {
            'service': 'AstroLogic Rule Engine',
            'version': '1.0.0',
            'description': 'Standalone astrological rule engine with comprehensive query processing',
            'endpoints': {
                'rule_engine': '/api/rule-engine/',
                'health': '/health',
                'examples': '/api/rule-engine/examples'
            },
            'documentation': 'https://github.com/your-repo/astro-logic-rule-engine'
        }

def register_error_handlers(app):
    """Register error handlers."""
    
    @app.errorhandler(400)
    def bad_request(error):
        return {
            'error': 'Bad Request',
            'message': 'The request could not be understood by the server',
            'status_code': 400
        }, 400
    
    @app.errorhandler(404)
    def not_found(error):
        return {
            'error': 'Not Found',
            'message': 'The requested resource was not found',
            'status_code': 404
        }, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return {
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred',
            'status_code': 500
        }, 500

def register_cli_commands(app):
    """Register custom CLI commands."""
    
    @app.cli.command()
    def test():
        """Run unit tests."""
        import pytest
        pytest.main(['-v', 'tests/'])
    
    @app.cli.command()
    def init_db():
        """Initialize sample data."""
        print("Initializing sample astrological data...")
        # Add sample data initialization here
        print("Sample data initialized successfully!")
