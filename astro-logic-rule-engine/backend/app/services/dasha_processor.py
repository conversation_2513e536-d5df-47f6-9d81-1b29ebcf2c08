"""
AstroLogic Rule Engine - Dasha Processor Service

This service handles dasha (planetary period) calculations and processing
for time-based astrological predictions and analysis.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from app.astrology.constants import PLANET_NAMES

class DashaProcessor:
    """
    Processes dasha (planetary periods) for astrological analysis.
    
    This class handles:
    - Dasha period calculations
    - Bhukti (sub-period) analysis
    - Age-based dasha filtering
    - Dasha-based query evaluation
    """
    
    def __init__(self):
        # Standard Vimshottari dasha periods in years
        self.maha_dasha_periods = {
            'KETU': 7,
            'VENUS': 20,
            'SUN': 6,
            'MOON': 10,
            'MARS': 7,
            'RAHU': 18,
            'JUPITER': 16,
            'SATURN': 19,
            'MERCURY': 17
        }
        
        # Total cycle is 120 years
        self.total_cycle_years = 120
    
    def get_sample_dasha_periods(self, user_profile_id: str, member_profile_id: str) -> List[Dict[str, Any]]:
        """
        Get sample dasha periods for demonstration.
        
        Args:
            user_profile_id: User profile identifier
            member_profile_id: Member profile identifier
            
        Returns:
            List of dasha periods
        """
        # Sample birth date for calculation
        birth_date = datetime(1990, 1, 15)  # Sample birth date
        
        # Calculate dasha periods starting from birth
        dasha_periods = []
        current_date = birth_date
        
        # Sample dasha sequence (this would be calculated based on birth chart)
        dasha_sequence = ['VENUS', 'SUN', 'MOON', 'MARS', 'RAHU', 'JUPITER', 'SATURN', 'MERCURY', 'KETU']
        
        for i, planet in enumerate(dasha_sequence):
            period_years = self.maha_dasha_periods[planet]
            end_date = current_date + timedelta(days=period_years * 365.25)
            
            # Create bhukti periods within this maha dasha
            bhukti_periods = self._calculate_bhukti_periods(planet, current_date, end_date)
            
            dasha_period = {
                'maha_dasha_planet': planet,
                'start_date': current_date.strftime('%Y-%m-%d %H:%M:%S'),
                'end_date': end_date.strftime('%Y-%m-%d %H:%M:%S'),
                'duration_years': period_years,
                'bhukti_periods': bhukti_periods,
                'age_at_start': self._calculate_age(birth_date, current_date),
                'age_at_end': self._calculate_age(birth_date, end_date)
            }
            
            dasha_periods.append(dasha_period)
            current_date = end_date
            
            # Limit to reasonable number for demo
            if len(dasha_periods) >= 5:
                break
        
        return dasha_periods
    
    def _calculate_bhukti_periods(self, maha_dasha_planet: str, start_date: datetime, 
                                end_date: datetime) -> List[Dict[str, Any]]:
        """Calculate bhukti periods within a maha dasha."""
        bhukti_periods = []
        total_days = (end_date - start_date).days
        current_date = start_date
        
        # Bhukti sequence starts with the maha dasha planet
        bhukti_sequence = [maha_dasha_planet] + [
            planet for planet in self.maha_dasha_periods.keys() 
            if planet != maha_dasha_planet
        ]
        
        for planet in bhukti_sequence[:9]:  # Limit to 9 bhukti periods
            # Calculate proportional duration
            planet_years = self.maha_dasha_periods[planet]
            maha_years = self.maha_dasha_periods[maha_dasha_planet]
            
            # Bhukti duration = (Bhukti planet years / Total cycle) * Maha dasha years
            bhukti_years = (planet_years / self.total_cycle_years) * maha_years
            bhukti_days = int(bhukti_years * 365.25)
            
            bhukti_end = current_date + timedelta(days=bhukti_days)
            
            # Ensure we don't exceed the maha dasha period
            if bhukti_end > end_date:
                bhukti_end = end_date
            
            bhukti_period = {
                'bhukti_planet': planet,
                'start_date': current_date.strftime('%Y-%m-%d %H:%M:%S'),
                'end_date': bhukti_end.strftime('%Y-%m-%d %H:%M:%S'),
                'duration_days': (bhukti_end - current_date).days,
                'maha_dasha': maha_dasha_planet
            }
            
            bhukti_periods.append(bhukti_period)
            current_date = bhukti_end
            
            if current_date >= end_date:
                break
        
        return bhukti_periods
    
    def _calculate_age(self, birth_date: datetime, target_date: datetime) -> float:
        """Calculate age in years at target date."""
        age_delta = target_date - birth_date
        return round(age_delta.days / 365.25, 1)
    
    def filter_dasha_by_planet(self, dasha_periods: List[Dict[str, Any]], 
                             planet: str) -> List[Dict[str, Any]]:
        """
        Filter dasha periods by specific planet.
        
        Args:
            dasha_periods: List of dasha periods
            planet: Planet name to filter by
            
        Returns:
            Filtered dasha periods
        """
        filtered_periods = []
        
        for period in dasha_periods:
            # Check maha dasha
            if period.get('maha_dasha_planet') == planet:
                filtered_periods.append(period)
                continue
            
            # Check bhukti periods
            bhukti_periods = period.get('bhukti_periods', [])
            matching_bhuktis = [
                bhukti for bhukti in bhukti_periods 
                if bhukti.get('bhukti_planet') == planet
            ]
            
            if matching_bhuktis:
                # Create a new period with only matching bhuktis
                filtered_period = period.copy()
                filtered_period['bhukti_periods'] = matching_bhuktis
                filtered_period['filtered_by'] = f'{planet} bhukti periods'
                filtered_periods.append(filtered_period)
        
        return filtered_periods
    
    def filter_dasha_by_age_range(self, dasha_periods: List[Dict[str, Any]], 
                                 min_age: Optional[float] = None, 
                                 max_age: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Filter dasha periods by age range.
        
        Args:
            dasha_periods: List of dasha periods
            min_age: Minimum age filter
            max_age: Maximum age filter
            
        Returns:
            Filtered dasha periods
        """
        if min_age is None and max_age is None:
            return dasha_periods
        
        filtered_periods = []
        
        for period in dasha_periods:
            age_start = period.get('age_at_start', 0)
            age_end = period.get('age_at_end', 0)
            
            # Check if period overlaps with age range
            if min_age is not None and age_end < min_age:
                continue
            
            if max_age is not None and age_start > max_age:
                continue
            
            filtered_periods.append(period)
        
        return filtered_periods
    
    def get_current_dasha(self, dasha_periods: List[Dict[str, Any]], 
                         current_date: Optional[datetime] = None) -> Optional[Dict[str, Any]]:
        """
        Get the current dasha period for a given date.
        
        Args:
            dasha_periods: List of dasha periods
            current_date: Date to check (defaults to now)
            
        Returns:
            Current dasha period or None
        """
        if current_date is None:
            current_date = datetime.now()
        
        current_date_str = current_date.strftime('%Y-%m-%d %H:%M:%S')
        
        for period in dasha_periods:
            start_date = period.get('start_date', '')
            end_date = period.get('end_date', '')
            
            if start_date <= current_date_str <= end_date:
                # Also find current bhukti
                bhukti_periods = period.get('bhukti_periods', [])
                current_bhukti = None
                
                for bhukti in bhukti_periods:
                    bhukti_start = bhukti.get('start_date', '')
                    bhukti_end = bhukti.get('end_date', '')
                    
                    if bhukti_start <= current_date_str <= bhukti_end:
                        current_bhukti = bhukti
                        break
                
                result = period.copy()
                result['current_bhukti'] = current_bhukti
                return result
        
        return None
    
    def analyze_dasha_for_marriage(self, dasha_periods: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze dasha periods for marriage timing.
        
        Args:
            dasha_periods: List of dasha periods
            
        Returns:
            Marriage timing analysis
        """
        marriage_planets = ['VENUS', 'JUPITER']  # Benefic planets for marriage
        marriage_periods = []
        
        for period in dasha_periods:
            maha_planet = period.get('maha_dasha_planet')
            age_start = period.get('age_at_start', 0)
            age_end = period.get('age_at_end', 0)
            
            # Check if it's a marriage-favorable age (typically 18-35)
            if not (18 <= age_start <= 35 or 18 <= age_end <= 35):
                continue
            
            # Check maha dasha
            if maha_planet in marriage_planets:
                marriage_periods.append({
                    'type': 'maha_dasha',
                    'planet': maha_planet,
                    'period': period,
                    'favorability': 'high'
                })
            
            # Check bhukti periods
            bhukti_periods = period.get('bhukti_periods', [])
            for bhukti in bhukti_periods:
                bhukti_planet = bhukti.get('bhukti_planet')
                if bhukti_planet in marriage_planets:
                    marriage_periods.append({
                        'type': 'bhukti',
                        'planet': bhukti_planet,
                        'maha_dasha': maha_planet,
                        'period': bhukti,
                        'favorability': 'medium'
                    })
        
        return {
            'total_favorable_periods': len(marriage_periods),
            'periods': marriage_periods,
            'analysis': self._create_marriage_analysis(marriage_periods)
        }
    
    def _create_marriage_analysis(self, marriage_periods: List[Dict[str, Any]]) -> str:
        """Create marriage timing analysis summary."""
        if not marriage_periods:
            return "No particularly favorable dasha periods found for marriage in the analyzed timeframe."
        
        high_periods = [p for p in marriage_periods if p['favorability'] == 'high']
        medium_periods = [p for p in marriage_periods if p['favorability'] == 'medium']
        
        analysis = f"Found {len(marriage_periods)} favorable periods for marriage. "
        
        if high_periods:
            analysis += f"{len(high_periods)} highly favorable maha dasha periods. "
        
        if medium_periods:
            analysis += f"{len(medium_periods)} moderately favorable bhukti periods. "
        
        # Get the earliest favorable period
        if marriage_periods:
            earliest = min(marriage_periods, key=lambda p: p['period'].get('start_date', ''))
            analysis += f"Earliest favorable period starts around age {earliest['period'].get('age_at_start', 'unknown')}."
        
        return analysis
