"""
AstroLogic Rule Engine - Query Processor Service

This service handles the main query processing logic, coordinating between
condition parsing, rule evaluation, and result formatting.
"""

from typing import Dict, Any, List, Optional
import time
from app.core.condition_parser import ConditionParser, ParsedCondition
from app.core.rule_evaluator import RuleEvaluator
from app.core.logical_operators import LogicalOperatorProcessor
from app.astrology.chart_processor import ChartProcessor
from app.utils.response_formatter import format_query_result
from app.utils.helpers import get_success_rating

class QueryProcessor:
    """
    Main query processor that orchestrates the rule engine evaluation process.
    
    This class coordinates between different components to:
    1. Parse queries into structured conditions
    2. Load and process chart data
    3. Evaluate conditions using astrological rules
    4. Apply logical operators
    5. Format and return results
    """
    
    def __init__(self):
        self.condition_parser = ConditionParser()
        self.rule_evaluator = RuleEvaluator()
        self.logical_processor = LogicalOperatorProcessor()
        self.chart_processor = ChartProcessor()
        
    def process_query(self, user_profile_id: str, member_profile_id: str, 
                     query: str, chart_type: str = "D1") -> Dict[str, Any]:
        """
        Process a complete astrological query and return results.
        
        Args:
            user_profile_id: User profile identifier
            member_profile_id: Member profile identifier
            query: Astrological query string
            chart_type: Chart type (D1, D2, etc.)
            
        Returns:
            Dictionary with query results and analysis
        """
        start_time = time.time()
        
        try:
            # Step 1: Parse the query into structured conditions
            parsed_conditions = self.condition_parser.parse_query(query)
            
            # Step 2: Validate parsed conditions
            validation_result = self.condition_parser.validate_parsed_conditions(parsed_conditions)
            
            if not validation_result['is_valid']:
                return {
                    'success': False,
                    'error': 'Query parsing failed',
                    'details': validation_result['errors'],
                    'query': query
                }
            
            # Step 3: Load chart data
            chart_data = self.chart_processor.get_chart_data(
                user_profile_id, member_profile_id, chart_type
            )
            
            if not chart_data:
                return {
                    'success': False,
                    'error': 'Chart data not available',
                    'query': query,
                    'user_profile_id': user_profile_id,
                    'member_profile_id': member_profile_id
                }
            
            # Step 4: Extract planet-house mapping from chart
            planet_house_mapping = self.chart_processor.get_planet_house_mapping(
                chart_data, chart_type
            )
            
            # Step 5: Evaluate all conditions
            evaluation_results = []
            for condition in parsed_conditions:
                result = self._evaluate_single_condition(
                    condition, chart_data, planet_house_mapping, chart_type
                )
                evaluation_results.append(result)
            
            # Step 6: Apply logical operators to get final result
            final_result = self.logical_processor.process_logical_conditions(
                parsed_conditions, evaluation_results
            )
            
            # Step 7: Calculate success metrics
            success_metrics = self._calculate_success_metrics(
                evaluation_results, final_result
            )
            
            # Step 8: Format response
            processing_time = round((time.time() - start_time) * 1000, 2)
            
            return format_query_result(
                query=query,
                final_result=final_result,
                parsed_conditions=parsed_conditions,
                evaluation_results=evaluation_results,
                success_metrics=success_metrics,
                chart_data=chart_data,
                chart_type=chart_type,
                processing_time=processing_time
            )
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Query processing failed: {str(e)}',
                'query': query,
                'processing_time': round((time.time() - start_time) * 1000, 2)
            }
    
    def _evaluate_single_condition(self, condition: ParsedCondition, 
                                 chart_data: Dict[str, Any],
                                 planet_house_mapping: Dict[str, int],
                                 chart_type: str) -> Dict[str, Any]:
        """
        Evaluate a single parsed condition.
        
        Args:
            condition: Parsed condition object
            chart_data: Chart data dictionary
            planet_house_mapping: Planet to house mapping
            chart_type: Chart type
            
        Returns:
            Evaluation result dictionary
        """
        try:
            if condition.condition_type == 'planet_house':
                return self.rule_evaluator.evaluate_planet_house_condition(
                    condition, planet_house_mapping
                )
            
            elif condition.condition_type == 'relationship':
                return self.rule_evaluator.evaluate_relationship_condition(
                    condition, chart_data, chart_type
                )
            
            elif condition.condition_type == 'logical':
                # Recursively evaluate sub-conditions for logical operators
                if condition.sub_conditions:
                    sub_results = []
                    for sub_condition in condition.sub_conditions:
                        sub_result = self._evaluate_single_condition(
                            sub_condition, chart_data, planet_house_mapping, chart_type
                        )
                        sub_results.append(sub_result)

                    # Apply logical operator to sub-results
                    logical_result = self.logical_processor.process_logical_conditions(
                        condition.sub_conditions, sub_results
                    )

                    return {
                        'condition': condition.raw_condition,
                        'result': logical_result,
                        'type': 'logical',
                        'operator': condition.logical_operator,
                        'sub_results': sub_results
                    }
                else:
                    return {
                        'condition': condition.raw_condition,
                        'result': False,
                        'error': 'Logical condition has no sub-conditions',
                        'type': 'logical'
                    }
            
            else:
                return {
                    'condition': condition.raw_condition,
                    'result': False,
                    'error': f'Unknown condition type: {condition.condition_type}',
                    'type': 'error'
                }
                
        except Exception as e:
            return {
                'condition': condition.raw_condition,
                'result': False,
                'error': f'Evaluation error: {str(e)}',
                'type': 'error'
            }
    
    def _calculate_success_metrics(self, evaluation_results: List[Dict[str, Any]], 
                                 final_result: bool) -> Dict[str, Any]:
        """
        Calculate success metrics for the query evaluation.
        
        Args:
            evaluation_results: List of individual condition results
            final_result: Final boolean result
            
        Returns:
            Success metrics dictionary
        """
        # Count successful conditions
        successful_conditions = sum(
            1 for result in evaluation_results 
            if result.get('result') is True
        )
        
        total_conditions = len([
            result for result in evaluation_results 
            if result.get('type') not in ['logical', 'error']
        ])
        
        # Calculate success rate
        success_rate = 0.0
        if total_conditions > 0:
            success_rate = (successful_conditions / total_conditions) * 100
        
        # Get success rating
        success_rating = get_success_rating(success_rate)
        
        return {
            'final_result': final_result,
            'successful_conditions': successful_conditions,
            'total_conditions': total_conditions,
            'success_rate': round(success_rate, 1),
            'success_rating': success_rating,
            'evaluation_summary': self._create_evaluation_summary(evaluation_results)
        }
    
    def _create_evaluation_summary(self, evaluation_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create a summary of the evaluation results."""
        summary = {
            'condition_types': {},
            'successful_by_type': {},
            'errors': []
        }
        
        for result in evaluation_results:
            condition_type = result.get('type', 'unknown')
            
            # Count condition types
            summary['condition_types'][condition_type] = \
                summary['condition_types'].get(condition_type, 0) + 1
            
            # Count successful conditions by type
            if result.get('result') is True:
                summary['successful_by_type'][condition_type] = \
                    summary['successful_by_type'].get(condition_type, 0) + 1
            
            # Collect errors
            if result.get('error'):
                summary['errors'].append({
                    'condition': result.get('condition', 'Unknown'),
                    'error': result.get('error')
                })
        
        return summary
    
    def validate_query_syntax(self, query: str) -> Dict[str, Any]:
        """
        Validate query syntax without full evaluation.
        
        Args:
            query: Query string to validate
            
        Returns:
            Validation results
        """
        try:
            # Parse the query
            parsed_conditions = self.condition_parser.parse_query(query)
            
            # Validate parsed conditions
            validation_result = self.condition_parser.validate_parsed_conditions(parsed_conditions)
            
            return {
                'is_valid': validation_result['is_valid'],
                'parsed_conditions': [
                    {
                        'type': cond.condition_type,
                        'condition': cond.raw_condition,
                        'planet': cond.planet,
                        'operator': cond.operator,
                        'target': cond.target
                    }
                    for cond in parsed_conditions
                ],
                'errors': validation_result.get('errors', []),
                'warnings': validation_result.get('warnings', [])
            }
            
        except Exception as e:
            return {
                'is_valid': False,
                'errors': [f'Validation error: {str(e)}'],
                'warnings': []
            }
    
    def test_basic_functionality(self) -> bool:
        """
        Test basic functionality for health checks.
        
        Returns:
            True if basic functionality works
        """
        try:
            # Test query parsing
            test_query = "JUPITER IN 7"
            parsed = self.condition_parser.parse_query(test_query)
            
            if not parsed or len(parsed) == 0:
                return False
            
            # Test validation
            validation = self.condition_parser.validate_parsed_conditions(parsed)
            
            return validation.get('is_valid', False)
            
        except Exception:
            return False
