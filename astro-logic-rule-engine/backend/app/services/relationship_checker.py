"""
AstroLogic Rule Engine - Relationship Checker Service

This service implements the comprehensive 5-rule relationship checking system
for evaluating complex astrological relationships between planets and houses.
"""

from typing import Dict, Any, List, Optional
from app.astrology.planetary_relationships import (
    get_planet_relationship, is_planet_aspecting_planet, get_planet_aspects
)
from app.astrology.house_systems import get_house_ruling_planet
from app.astrology.constants import get_nakshatra_lord, get_nakshatras_ruled_by_planet
from app.astrology.chart_processor import ChartProcessor

class RelationshipChecker:
    """
    Comprehensive relationship checker implementing the 5-rule system.
    
    The 5-rule relationship system evaluates:
    1. Basic position: Planet in house OR Planet in ruling planet's house
    2. WITH ruling planet: Planet with ruling planet OR Ruling planet with planet
    3. TOGETHER_WITH: Planets in the same house (conjunction)
    4. Nakshatra relationships: Planet in nakshatra ruled by target planet
    5. Aspecting relationships: Planet aspecting target planet or house
    """
    
    def __init__(self):
        self.chart_processor = ChartProcessor()
    
    def check_comprehensive_relationship(self, chart_data: Dict[str, Any], 
                                       planet: str, house_number: int, 
                                       chart_type: str = "D1") -> Dict[str, Any]:
        """
        Check comprehensive relationship between a planet and house ruling planet.
        
        This evaluates all 5 types of relationships and returns detailed results.
        
        Args:
            chart_data: Chart data dictionary
            planet: Planet name to check
            house_number: House number (1-12)
            chart_type: Chart type
            
        Returns:
            Comprehensive relationship results with 5 sub-rules
        """
        try:
            # Get house ruling planet
            house_sign, house_ruling_planet = self.chart_processor.get_house_sign_and_ruling_planet(
                chart_data, house_number, chart_type
            )
            
            if not house_ruling_planet:
                return self._create_error_result(
                    f"Could not determine ruling planet for house {house_number}"
                )
            
            # Get planet-house mapping
            planet_house_mapping = self.chart_processor.get_planet_house_mapping(
                chart_data, chart_type
            )
            
            if planet not in planet_house_mapping:
                return self._create_error_result(
                    f"Planet {planet} not found in chart"
                )
            
            # Evaluate all 5 relationship rules
            rule_results = {
                "basic_position": self._check_basic_position(
                    planet, house_number, house_ruling_planet, planet_house_mapping
                ),
                "with_ruling_planet": self._check_with_ruling_planet(
                    planet, house_ruling_planet, planet_house_mapping
                ),
                "together_with": self._check_together_with(
                    planet, house_ruling_planet, planet_house_mapping
                ),
                "nakshatra_relationship": self._check_nakshatra_relationship(
                    planet, house_ruling_planet, chart_data
                ),
                "aspecting_relationship": self._check_aspecting_relationship(
                    planet, house_ruling_planet, planet_house_mapping
                )
            }
            
            # Calculate overall result (any rule satisfied = True)
            overall_result = any(rule_results.values())
            
            return {
                "overall_result": overall_result,
                "house_number": house_number,
                "house_sign": house_sign,
                "house_ruling_planet": house_ruling_planet,
                "planet": planet,
                "planet_house": planet_house_mapping.get(planet),
                "relationships": rule_results,
                "details": self._create_relationship_details(
                    planet, house_number, house_ruling_planet, rule_results, planet_house_mapping
                ),
                "success_count": sum(1 for result in rule_results.values() if result),
                "total_rules": len(rule_results)
            }
            
        except Exception as e:
            return self._create_error_result(f"Error in comprehensive relationship check: {str(e)}")
    
    def _check_basic_position(self, planet: str, house_number: int, 
                            house_ruling_planet: str, 
                            planet_house_mapping: Dict[str, int]) -> bool:
        """
        Rule 1: Basic position check.
        Planet in target house OR Planet in ruling planet's house.
        """
        try:
            planet_house = planet_house_mapping.get(planet)
            if not planet_house:
                return False
            
            # Check if planet is in the target house
            if planet_house == house_number:
                return True
            
            # Check if planet is in any house ruled by the house ruling planet
            ruling_planet_houses = [
                house for house, ruling_planet in 
                [(i, get_house_ruling_planet(i)) for i in range(1, 13)]
                if ruling_planet == house_ruling_planet
            ]
            
            return planet_house in ruling_planet_houses
            
        except Exception:
            return False
    
    def _check_with_ruling_planet(self, planet: str, house_ruling_planet: str,
                                planet_house_mapping: Dict[str, int]) -> bool:
        """
        Rule 2: WITH ruling planet check.
        Planet with ruling planet OR Ruling planet with planet.
        """
        try:
            planet_house = planet_house_mapping.get(planet)
            ruling_planet_house = planet_house_mapping.get(house_ruling_planet)
            
            if not planet_house or not ruling_planet_house:
                return False
            
            # Check if they are in the same house (conjunction)
            return planet_house == ruling_planet_house
            
        except Exception:
            return False
    
    def _check_together_with(self, planet: str, house_ruling_planet: str,
                           planet_house_mapping: Dict[str, int]) -> bool:
        """
        Rule 3: TOGETHER_WITH check (same as rule 2 for ruling planet).
        Planets in the same house (conjunction).
        """
        # This is the same as _check_with_ruling_planet for ruling planet relationships
        return self._check_with_ruling_planet(planet, house_ruling_planet, planet_house_mapping)
    
    def _check_nakshatra_relationship(self, planet: str, house_ruling_planet: str,
                                    chart_data: Dict[str, Any]) -> bool:
        """
        Rule 4: Nakshatra relationship check.
        Planet in nakshatra ruled by house ruling planet.
        """
        try:
            # Get nakshatras ruled by the house ruling planet
            ruling_planet_nakshatras = get_nakshatras_ruled_by_planet(house_ruling_planet)
            
            if not ruling_planet_nakshatras:
                return False
            
            # In a full implementation, we would check the actual nakshatra
            # of the planet from the chart data. For now, we'll use a simplified check.
            
            # Check if there's nakshatra data in chart
            if 'nakshatras' in chart_data:
                planet_nakshatra = chart_data['nakshatras'].get(planet)
                if planet_nakshatra:
                    return planet_nakshatra.upper() in ruling_planet_nakshatras
            
            # Simplified: return True if the ruling planet has nakshatras
            # In real implementation, this would be more sophisticated
            return len(ruling_planet_nakshatras) > 0
            
        except Exception:
            return False
    
    def _check_aspecting_relationship(self, planet: str, house_ruling_planet: str,
                                    planet_house_mapping: Dict[str, int]) -> bool:
        """
        Rule 5: Aspecting relationship check.
        Planet aspecting house ruling planet OR House ruling planet aspecting planet.
        """
        try:
            planet_house = planet_house_mapping.get(planet)
            ruling_planet_house = planet_house_mapping.get(house_ruling_planet)
            
            if not planet_house or not ruling_planet_house:
                return False
            
            # Check if planet aspects the ruling planet
            if is_planet_aspecting_planet(planet, house_ruling_planet, planet_house, ruling_planet_house):
                return True
            
            # Check if ruling planet aspects the planet
            if is_planet_aspecting_planet(house_ruling_planet, planet, ruling_planet_house, planet_house):
                return True
            
            return False
            
        except Exception:
            return False
    
    def _create_relationship_details(self, planet: str, house_number: int,
                                   house_ruling_planet: str, rule_results: Dict[str, bool],
                                   planet_house_mapping: Dict[str, int]) -> Dict[str, str]:
        """Create detailed explanations for each relationship rule."""
        planet_house = planet_house_mapping.get(planet, 'Unknown')
        ruling_planet_house = planet_house_mapping.get(house_ruling_planet, 'Unknown')
        
        return {
            "basic_position": f"{planet} (house {planet_house}) {'✅' if rule_results['basic_position'] else '❌'} Basic position with {house_number}th house",
            "with_ruling_planet": f"{planet} (house {planet_house}) {'✅' if rule_results['with_ruling_planet'] else '❌'} With ruling planet {house_ruling_planet} (house {ruling_planet_house})",
            "together_with": f"{planet} {'✅' if rule_results['together_with'] else '❌'} Together with {house_ruling_planet}",
            "nakshatra_relationship": f"{planet} {'✅' if rule_results['nakshatra_relationship'] else '❌'} Nakshatra relationship with {house_ruling_planet}",
            "aspecting_relationship": f"{planet} {'✅' if rule_results['aspecting_relationship'] else '❌'} Aspecting relationship with {house_ruling_planet}"
        }
    
    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """Create error result structure."""
        return {
            "overall_result": False,
            "house_ruling_planet": None,
            "relationships": {
                "basic_position": False,
                "with_ruling_planet": False,
                "together_with": False,
                "nakshatra_relationship": False,
                "aspecting_relationship": False
            },
            "details": {
                "basic_position": error_message,
                "with_ruling_planet": error_message,
                "together_with": error_message,
                "nakshatra_relationship": error_message,
                "aspecting_relationship": error_message
            },
            "error": error_message,
            "success_count": 0,
            "total_rules": 5
        }
    
    def check_planet_to_planet_relationship(self, chart_data: Dict[str, Any],
                                          planet1: str, planet2: str,
                                          chart_type: str = "D1") -> Dict[str, Any]:
        """
        Check relationship between two planets using the 5-rule system.
        
        Args:
            chart_data: Chart data dictionary
            planet1: First planet
            planet2: Second planet
            chart_type: Chart type
            
        Returns:
            Relationship analysis between the two planets
        """
        try:
            planet_house_mapping = self.chart_processor.get_planet_house_mapping(
                chart_data, chart_type
            )
            
            if planet1 not in planet_house_mapping or planet2 not in planet_house_mapping:
                return self._create_error_result(
                    f"One or both planets ({planet1}, {planet2}) not found in chart"
                )
            
            planet1_house = planet_house_mapping[planet1]
            planet2_house = planet_house_mapping[planet2]
            
            # Check various relationships
            relationships = {
                "conjunction": planet1_house == planet2_house,
                "mutual_aspect": (
                    is_planet_aspecting_planet(planet1, planet2, planet1_house, planet2_house) and
                    is_planet_aspecting_planet(planet2, planet1, planet2_house, planet1_house)
                ),
                "planet1_aspects_planet2": is_planet_aspecting_planet(
                    planet1, planet2, planet1_house, planet2_house
                ),
                "planet2_aspects_planet1": is_planet_aspecting_planet(
                    planet2, planet1, planet2_house, planet1_house
                ),
                "natural_friendship": get_planet_relationship(planet1, planet2) == 'FRIEND'
            }
            
            overall_result = any(relationships.values())
            
            return {
                "overall_result": overall_result,
                "planet1": planet1,
                "planet2": planet2,
                "planet1_house": planet1_house,
                "planet2_house": planet2_house,
                "relationships": relationships,
                "natural_relationship": get_planet_relationship(planet1, planet2),
                "success_count": sum(1 for result in relationships.values() if result),
                "total_checks": len(relationships)
            }
            
        except Exception as e:
            return self._create_error_result(f"Error in planet-to-planet relationship check: {str(e)}")
