"""
AstroLogic Rule Engine - API Endpoints

This module provides RESTful API endpoints for the astrological rule engine.
It handles query processing, validation, and response formatting.
"""

from flask import request, jsonify, current_app
from . import api_bp
from app.services.query_processor import QueryProcessor
from app.core.validators import validate_request_data
from app.utils.response_formatter import format_success_response, format_error_response
import time
import traceback

# Initialize query processor
query_processor = QueryProcessor()

@api_bp.route('/rule-engine/', methods=['POST'])
def evaluate_rule():
    """
    Main rule engine endpoint for evaluating astrological queries.
    
    Request Body:
        {
            "user_profile_id": "1",
            "member_profile_id": "1",
            "query": "JUPITER IN 7 OR VENUS WITH_RULING_PLANET 7th_House_Ruling_Planet",
            "chart_type": "D1"
        }
    
    Returns:
        JSON response with evaluation results
    """
    start_time = time.time()
    
    try:
        # Get request data
        data = request.get_json()
        
        if not data:
            return format_error_response(
                "No JSON data provided",
                status_code=400
            )
        
        # Validate request data
        is_valid, validated_data, error_message = validate_request_data(data)
        
        if not is_valid:
            return format_error_response(
                f"Validation error: {error_message}",
                status_code=400
            )
        
        # Process the query
        result = query_processor.process_query(
            user_profile_id=validated_data['user_profile_id'],
            member_profile_id=validated_data['member_profile_id'],
            query=validated_data['query'],
            chart_type=validated_data.get('chart_type', 'D1')
        )
        
        # Calculate processing time
        processing_time = round((time.time() - start_time) * 1000, 2)  # milliseconds
        
        # Add metadata to result
        result['metadata'] = {
            'processing_time_ms': processing_time,
            'api_version': '1.0.0',
            'timestamp': time.time()
        }
        
        return format_success_response(result)
        
    except Exception as e:
        current_app.logger.error(f"Error in rule engine evaluation: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        
        return format_error_response(
            "Internal server error during query processing",
            status_code=500,
            details=str(e) if current_app.debug else None
        )

@api_bp.route('/rule-engine/health', methods=['GET'])
def health_check():
    """
    Health check endpoint for monitoring service status.
    
    Returns:
        JSON response with service health information
    """
    try:
        # Perform basic health checks
        health_status = {
            'status': 'healthy',
            'service': 'AstroLogic Rule Engine',
            'version': '1.0.0',
            'timestamp': time.time(),
            'checks': {
                'query_processor': 'healthy',
                'validators': 'healthy',
                'astrology_engine': 'healthy'
            }
        }
        
        # Test basic functionality
        test_result = query_processor.test_basic_functionality()
        health_status['checks']['basic_test'] = 'healthy' if test_result else 'unhealthy'
        
        # Determine overall status
        all_healthy = all(status == 'healthy' for status in health_status['checks'].values())
        health_status['status'] = 'healthy' if all_healthy else 'unhealthy'
        
        status_code = 200 if all_healthy else 503
        return jsonify(health_status), status_code
        
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }), 503

@api_bp.route('/rule-engine/examples', methods=['GET'])
def get_query_examples():
    """
    Get example queries for testing and documentation.
    
    Returns:
        JSON response with query examples and descriptions
    """
    examples = {
        'basic_queries': [
            {
                'query': 'JUPITER IN 7',
                'description': 'Check if Jupiter is in the 7th house',
                'category': 'Basic Planet-House'
            },
            {
                'query': 'VENUS IN 1st_House',
                'description': 'Check if Venus is in the 1st house',
                'category': 'Basic Planet-House'
            },
            {
                'query': 'MARS NOT_IN 8',
                'description': 'Check if Mars is not in the 8th house',
                'category': 'Basic Planet-House'
            }
        ],
        'logical_operators': [
            {
                'query': 'JUPITER IN 7 OR VENUS IN 1',
                'description': 'Jupiter in 7th OR Venus in 1st house',
                'category': 'OR Logic'
            },
            {
                'query': 'MARS IN 1 AND SATURN NOT_IN 8',
                'description': 'Mars in 1st AND Saturn not in 8th house',
                'category': 'AND Logic'
            },
            {
                'query': 'NOT (JUPITER IN 12)',
                'description': 'Jupiter is not in the 12th house',
                'category': 'NOT Logic'
            }
        ],
        'relationship_queries': [
            {
                'query': 'JUPITER WITH_RULING_PLANET 7th_House_Ruling_Planet',
                'description': 'Jupiter with the ruling planet of 7th house',
                'category': 'Ruling Planet Relationship'
            },
            {
                'query': 'VENUS TOGETHER_WITH MARS',
                'description': 'Venus and Mars in the same house',
                'category': 'Conjunction'
            },
            {
                'query': 'SUN IS_ASPECTING_BIRTH MOON',
                'description': 'Sun aspecting Moon in birth chart',
                'category': 'Aspect Relationship'
            }
        ],
        'nakshatra_queries': [
            {
                'query': 'JUPITER WITH_STARS_OF VENUS',
                'description': 'Jupiter in nakshatra ruled by Venus',
                'category': 'Nakshatra Relationship'
            },
            {
                'query': 'MARS WITH_STARS_OF 7th_House_Ruling_Planet',
                'description': 'Mars in nakshatra of 7th house ruling planet',
                'category': 'Nakshatra Relationship'
            }
        ],
        'complex_queries': [
            {
                'query': '(JUPITER IN 7 OR VENUS IN 1) AND MARS NOT_IN 8',
                'description': 'Complex query with parentheses and multiple operators',
                'category': 'Complex Logic'
            },
            {
                'query': 'JUPITER WITH_RULING_PLANET 7th_House_Ruling_Planet OR VENUS TOGETHER_WITH MARS',
                'description': 'Relationship queries with OR logic',
                'category': 'Complex Relationships'
            }
        ]
    }
    
    return jsonify({
        'examples': examples,
        'total_examples': sum(len(category) for category in examples.values()),
        'supported_operators': ['IN', 'NOT_IN', 'OR', 'AND', 'NOT'],
        'supported_relationships': [
            'WITH_RULING_PLANET',
            'TOGETHER_WITH', 
            'IS_ASPECTING_BIRTH',
            'WITH_STARS_OF'
        ],
        'usage_notes': [
            'House numbers can be written as 7, 7th_House, or 7th_House_Ruling_Planet',
            'Planet names should be in uppercase (JUPITER, VENUS, MARS, etc.)',
            'Logical operators have precedence: NOT > AND > OR',
            'Use parentheses to group conditions explicitly'
        ]
    })

@api_bp.route('/rule-engine/validate', methods=['POST'])
def validate_query():
    """
    Validate a query without executing it.
    
    Request Body:
        {
            "query": "JUPITER IN 7 OR VENUS WITH_RULING_PLANET 7th_House_Ruling_Planet"
        }
    
    Returns:
        JSON response with validation results
    """
    try:
        data = request.get_json()
        
        if not data or 'query' not in data:
            return format_error_response(
                "Query parameter is required",
                status_code=400
            )
        
        query = data['query']
        
        # Validate query syntax
        validation_result = query_processor.validate_query_syntax(query)
        
        return format_success_response({
            'query': query,
            'is_valid': validation_result['is_valid'],
            'parsed_conditions': validation_result.get('parsed_conditions', []),
            'errors': validation_result.get('errors', []),
            'warnings': validation_result.get('warnings', [])
        })
        
    except Exception as e:
        return format_error_response(
            f"Error validating query: {str(e)}",
            status_code=500
        )
