"""
AstroLogic Rule Engine - Condition Parser

This module handles parsing of astrological query conditions into structured format.
It supports complex queries with logical operators and various relationship types.
"""

import re
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass

@dataclass
class ParsedCondition:
    """Represents a parsed astrological condition."""
    condition_type: str  # 'planet_house', 'relationship', 'logical'
    planet: Optional[str] = None
    operator: Optional[str] = None
    target: Optional[str] = None
    house_number: Optional[int] = None
    logical_operator: Optional[str] = None
    sub_conditions: Optional[List['ParsedCondition']] = None
    raw_condition: str = ""
    is_negated: bool = False

class ConditionParser:
    """
    Parser for astrological query conditions.
    
    Supports parsing of:
    - Basic planet-house conditions (JUPITER IN 7)
    - Relationship conditions (VENUS WITH_RULING_PLANET 7th_House_Ruling_Planet)
    - Logical operators (OR, AND, NOT)
    - Complex nested conditions with parentheses
    """
    
    def __init__(self):
        self.logical_operators = ['OR', 'AND', 'NOT']
        self.relationship_operators = [
            'WITH_RULING_PLANET', 'TOGETHER_WITH', 
            'IS_ASPECTING_BIRTH', 'WITH_STARS_OF'
        ]
        self.basic_operators = ['IN', 'NOT_IN']
        
    def parse_query(self, query: str) -> List[ParsedCondition]:
        """
        Parse a complete query into structured conditions.
        
        Args:
            query: Raw query string
            
        Returns:
            List of parsed conditions
        """
        try:
            # Clean and normalize the query
            normalized_query = self._normalize_query(query)
            
            # Handle parentheses and logical operators
            conditions = self._parse_with_precedence(normalized_query)
            
            return conditions
            
        except Exception as e:
            # Return a single error condition
            return [ParsedCondition(
                condition_type='error',
                raw_condition=query,
                target=f"Parse error: {str(e)}"
            )]
    
    def _normalize_query(self, query: str) -> str:
        """Normalize query string for consistent parsing."""
        # Remove extra whitespace
        query = ' '.join(query.split())
        
        # Ensure spaces around operators
        for op in self.logical_operators + self.relationship_operators + self.basic_operators:
            query = re.sub(f'\\b{op}\\b', f' {op} ', query)
        
        # Clean up multiple spaces
        query = ' '.join(query.split())
        
        return query
    
    def _parse_with_precedence(self, query: str) -> List[ParsedCondition]:
        """Parse query respecting operator precedence: NOT > AND > OR."""
        # Handle parentheses first
        if '(' in query:
            return self._parse_parentheses(query)
        
        # Split by OR (lowest precedence)
        or_parts = self._split_by_operator(query, 'OR')
        if len(or_parts) > 1:
            return [ParsedCondition(
                condition_type='logical',
                logical_operator='OR',
                sub_conditions=[
                    cond for part in or_parts 
                    for cond in self._parse_with_precedence(part)
                ],
                raw_condition=query
            )]
        
        # Split by AND (medium precedence)
        and_parts = self._split_by_operator(query, 'AND')
        if len(and_parts) > 1:
            return [ParsedCondition(
                condition_type='logical',
                logical_operator='AND',
                sub_conditions=[
                    cond for part in and_parts 
                    for cond in self._parse_with_precedence(part)
                ],
                raw_condition=query
            )]
        
        # Handle NOT (highest precedence)
        if query.strip().startswith('NOT '):
            not_condition = query.strip()[4:].strip()
            sub_conditions = self._parse_with_precedence(not_condition)
            return [ParsedCondition(
                condition_type='logical',
                logical_operator='NOT',
                sub_conditions=sub_conditions,
                raw_condition=query,
                is_negated=True
            )]
        
        # Parse single condition
        return [self._parse_single_condition(query)]
    
    def _parse_parentheses(self, query: str) -> List[ParsedCondition]:
        """Handle parentheses in queries."""
        # Find innermost parentheses
        while '(' in query:
            # Find the innermost parentheses
            start = -1
            for i, char in enumerate(query):
                if char == '(':
                    start = i
                elif char == ')' and start != -1:
                    # Found a complete parentheses group
                    inner_query = query[start+1:i]
                    inner_result = self._parse_with_precedence(inner_query)
                    
                    # Replace the parentheses group with a placeholder
                    placeholder = f"__PARSED_GROUP_{len(inner_result)}__"
                    query = query[:start] + placeholder + query[i+1:]
                    break
        
        # Now parse the query without parentheses
        return self._parse_with_precedence(query)
    
    def _split_by_operator(self, query: str, operator: str) -> List[str]:
        """Split query by a specific logical operator."""
        # Use regex to split while preserving operator boundaries
        pattern = f'\\b{operator}\\b'
        parts = re.split(pattern, query)
        
        # Clean up parts
        return [part.strip() for part in parts if part.strip()]
    
    def _parse_single_condition(self, condition: str) -> ParsedCondition:
        """Parse a single condition without logical operators."""
        condition = condition.strip()
        
        try:
            # Check for relationship conditions
            for rel_op in self.relationship_operators:
                if rel_op in condition:
                    return self._parse_relationship_condition(condition, rel_op)
            
            # Check for basic planet-house conditions
            for basic_op in self.basic_operators:
                if basic_op in condition:
                    return self._parse_planet_house_condition(condition, basic_op)
            
            # If no recognized pattern, return as unknown
            return ParsedCondition(
                condition_type='unknown',
                raw_condition=condition,
                target="Unrecognized condition format"
            )
            
        except Exception as e:
            return ParsedCondition(
                condition_type='error',
                raw_condition=condition,
                target=f"Error parsing condition: {str(e)}"
            )
    
    def _parse_relationship_condition(self, condition: str, operator: str) -> ParsedCondition:
        """Parse relationship conditions like 'JUPITER WITH_RULING_PLANET 7th_House_Ruling_Planet'."""
        parts = condition.split(operator)
        
        if len(parts) != 2:
            return ParsedCondition(
                condition_type='error',
                raw_condition=condition,
                target=f"Invalid {operator} condition format"
            )
        
        planet = parts[0].strip().upper()
        target = parts[1].strip()
        
        # Extract house number if target refers to a house
        house_number = self._extract_house_number(target)
        
        return ParsedCondition(
            condition_type='relationship',
            planet=planet,
            operator=operator,
            target=target,
            house_number=house_number,
            raw_condition=condition
        )
    
    def _parse_planet_house_condition(self, condition: str, operator: str) -> ParsedCondition:
        """Parse basic planet-house conditions like 'JUPITER IN 7'."""
        parts = condition.split(operator)
        
        if len(parts) != 2:
            return ParsedCondition(
                condition_type='error',
                raw_condition=condition,
                target=f"Invalid {operator} condition format"
            )
        
        planet = parts[0].strip().upper()
        house_target = parts[1].strip()
        
        # Extract house number
        house_number = self._extract_house_number(house_target)
        
        return ParsedCondition(
            condition_type='planet_house',
            planet=planet,
            operator=operator,
            target=house_target,
            house_number=house_number,
            raw_condition=condition,
            is_negated=(operator == 'NOT_IN')
        )
    
    def _extract_house_number(self, target: str) -> Optional[int]:
        """Extract house number from various house reference formats."""
        # Handle formats like: 7, 7th_House, 7th_House_Ruling_Planet
        match = re.search(r'(\d+)', target)
        if match:
            try:
                house_num = int(match.group(1))
                if 1 <= house_num <= 12:
                    return house_num
            except ValueError:
                pass
        
        return None
    
    def validate_parsed_conditions(self, conditions: List[ParsedCondition]) -> Dict[str, Any]:
        """
        Validate parsed conditions and return validation results.
        
        Args:
            conditions: List of parsed conditions
            
        Returns:
            Validation results dictionary
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'condition_count': len(conditions),
            'condition_types': {}
        }
        
        # Count condition types
        for condition in conditions:
            condition_type = condition.condition_type
            validation_result['condition_types'][condition_type] = \
                validation_result['condition_types'].get(condition_type, 0) + 1
        
        # Check for errors
        error_conditions = [c for c in conditions if c.condition_type == 'error']
        if error_conditions:
            validation_result['is_valid'] = False
            for error_cond in error_conditions:
                validation_result['errors'].append(
                    f"Error in '{error_cond.raw_condition}': {error_cond.target}"
                )
        
        # Check for unknown conditions
        unknown_conditions = [c for c in conditions if c.condition_type == 'unknown']
        if unknown_conditions:
            for unknown_cond in unknown_conditions:
                validation_result['warnings'].append(
                    f"Unknown condition format: '{unknown_cond.raw_condition}'"
                )
        
        return validation_result
