"""
AstroLogic Rule Engine - Logical Operators Processor

This module handles the processing of logical operators (OR, AND, NOT) in
astrological queries, applying proper precedence and evaluation rules.
"""

from typing import List, Dict, Any, Optional
from app.core.condition_parser import ParsedCondition

class LogicalOperatorProcessor:
    """
    Processes logical operators in astrological queries.
    
    Handles:
    - OR logic (lowest precedence)
    - AND logic (medium precedence) 
    - NOT logic (highest precedence)
    - Nested logical conditions
    - Complex boolean expressions
    """
    
    def __init__(self):
        self.operator_precedence = {
            'NOT': 3,  # Highest precedence
            'AND': 2,  # Medium precedence
            'OR': 1    # Lowest precedence
        }
    
    def process_logical_conditions(self, parsed_conditions: List[ParsedCondition],
                                 evaluation_results: List[Dict[str, Any]]) -> bool:
        """
        Process logical conditions and return the final boolean result.
        
        Args:
            parsed_conditions: List of parsed conditions
            evaluation_results: List of evaluation results for each condition
            
        Returns:
            Final boolean result after applying all logical operators
        """
        try:
            # If only one condition, return its result
            if len(parsed_conditions) == 1:
                condition = parsed_conditions[0]
                if condition.condition_type == 'logical':
                    return self._process_single_logical_condition(condition, evaluation_results)
                else:
                    # Single non-logical condition
                    result = evaluation_results[0] if evaluation_results else {}
                    return result.get('result', False)
            
            # Multiple conditions - apply logical operators
            return self._process_multiple_conditions(parsed_conditions, evaluation_results)
            
        except Exception as e:
            print(f"Error processing logical conditions: {e}")
            return False
    
    def _process_single_logical_condition(self, condition: ParsedCondition,
                                        evaluation_results: List[Dict[str, Any]]) -> bool:
        """Process a single logical condition with sub-conditions."""
        if not condition.sub_conditions:
            return False
        
        operator = condition.logical_operator
        
        if operator == 'OR':
            return self._process_or_logic(condition.sub_conditions, evaluation_results)
        elif operator == 'AND':
            return self._process_and_logic(condition.sub_conditions, evaluation_results)
        elif operator == 'NOT':
            return self._process_not_logic(condition.sub_conditions, evaluation_results)
        else:
            return False
    
    def _process_multiple_conditions(self, parsed_conditions: List[ParsedCondition],
                                   evaluation_results: List[Dict[str, Any]]) -> bool:
        """Process multiple conditions with implicit OR logic."""
        # Default behavior: OR logic between top-level conditions
        results = []
        
        for i, condition in enumerate(parsed_conditions):
            if condition.condition_type == 'logical':
                result = self._process_single_logical_condition(condition, evaluation_results)
            else:
                # Get result from evaluation_results
                if i < len(evaluation_results):
                    result = evaluation_results[i].get('result', False)
                else:
                    result = False
            
            results.append(result)
        
        # Apply OR logic to all results
        return any(results)
    
    def _process_or_logic(self, sub_conditions: List[ParsedCondition],
                         evaluation_results: List[Dict[str, Any]]) -> bool:
        """
        Process OR logic: returns True if ANY sub-condition is True.
        
        Args:
            sub_conditions: List of sub-conditions
            evaluation_results: Evaluation results
            
        Returns:
            True if any sub-condition evaluates to True
        """
        for sub_condition in sub_conditions:
            if sub_condition.condition_type == 'logical':
                # Recursive processing for nested logical conditions
                result = self._process_single_logical_condition(sub_condition, evaluation_results)
            else:
                # Find matching evaluation result
                result = self._find_condition_result(sub_condition, evaluation_results)
            
            if result:
                return True  # OR logic: return True on first True result
        
        return False
    
    def _process_and_logic(self, sub_conditions: List[ParsedCondition],
                          evaluation_results: List[Dict[str, Any]]) -> bool:
        """
        Process AND logic: returns True if ALL sub-conditions are True.
        
        Args:
            sub_conditions: List of sub-conditions
            evaluation_results: Evaluation results
            
        Returns:
            True if all sub-conditions evaluate to True
        """
        for sub_condition in sub_conditions:
            if sub_condition.condition_type == 'logical':
                # Recursive processing for nested logical conditions
                result = self._process_single_logical_condition(sub_condition, evaluation_results)
            else:
                # Find matching evaluation result
                result = self._find_condition_result(sub_condition, evaluation_results)
            
            if not result:
                return False  # AND logic: return False on first False result
        
        return True
    
    def _process_not_logic(self, sub_conditions: List[ParsedCondition],
                          evaluation_results: List[Dict[str, Any]]) -> bool:
        """
        Process NOT logic: returns the negation of the sub-condition.
        
        Args:
            sub_conditions: List of sub-conditions (should be only one for NOT)
            evaluation_results: Evaluation results
            
        Returns:
            Negation of the sub-condition result
        """
        if not sub_conditions:
            return False
        
        # NOT should only have one sub-condition
        sub_condition = sub_conditions[0]
        
        if sub_condition.condition_type == 'logical':
            # Recursive processing for nested logical conditions
            result = self._process_single_logical_condition(sub_condition, evaluation_results)
        else:
            # Find matching evaluation result
            result = self._find_condition_result(sub_condition, evaluation_results)
        
        return not result  # NOT logic: return negation
    
    def _find_condition_result(self, condition: ParsedCondition,
                             evaluation_results: List[Dict[str, Any]]) -> bool:
        """
        Find the evaluation result for a specific condition.
        
        Args:
            condition: Condition to find result for
            evaluation_results: List of evaluation results
            
        Returns:
            Boolean result for the condition
        """
        # Try to match by raw condition text
        for result in evaluation_results:
            if result.get('condition') == condition.raw_condition:
                return result.get('result', False)
        
        # If no exact match, try to match by condition components
        for result in evaluation_results:
            if (result.get('type') == condition.condition_type and
                self._conditions_match(condition, result)):
                return result.get('result', False)
        
        # Default to False if no match found
        return False
    
    def _conditions_match(self, condition: ParsedCondition, result: Dict[str, Any]) -> bool:
        """
        Check if a condition matches an evaluation result.
        
        Args:
            condition: Parsed condition
            result: Evaluation result
            
        Returns:
            True if they match
        """
        # Check if details match
        details = result.get('details', {})
        
        # Match planet if available
        if condition.planet and details.get('planet'):
            if condition.planet != details.get('planet'):
                return False
        
        # Match house number if available
        if condition.house_number and details.get('target_house'):
            if condition.house_number != details.get('target_house'):
                return False
        
        # Match operator if available
        if condition.operator and details.get('operator'):
            if condition.operator != details.get('operator'):
                return False
        
        return True
    
    def create_logical_analysis(self, parsed_conditions: List[ParsedCondition],
                              evaluation_results: List[Dict[str, Any]],
                              final_result: bool) -> Dict[str, Any]:
        """
        Create a detailed analysis of the logical operations performed.
        
        Args:
            parsed_conditions: List of parsed conditions
            evaluation_results: List of evaluation results
            final_result: Final boolean result
            
        Returns:
            Detailed logical analysis
        """
        analysis = {
            'final_result': final_result,
            'total_conditions': len(parsed_conditions),
            'logical_structure': [],
            'operator_summary': {},
            'evaluation_flow': []
        }
        
        # Analyze logical structure
        for condition in parsed_conditions:
            if condition.condition_type == 'logical':
                analysis['logical_structure'].append({
                    'operator': condition.logical_operator,
                    'sub_condition_count': len(condition.sub_conditions) if condition.sub_conditions else 0,
                    'condition': condition.raw_condition
                })
                
                # Count operators
                operator = condition.logical_operator
                analysis['operator_summary'][operator] = \
                    analysis['operator_summary'].get(operator, 0) + 1
        
        # Create evaluation flow
        for i, (condition, result) in enumerate(zip(parsed_conditions, evaluation_results)):
            analysis['evaluation_flow'].append({
                'step': i + 1,
                'condition': condition.raw_condition,
                'type': condition.condition_type,
                'result': result.get('result'),
                'operator': condition.logical_operator if condition.condition_type == 'logical' else None
            })
        
        return analysis
