"""
AstroLogic Rule Engine - Input Validation

This module provides comprehensive validation for API requests and query syntax.
It ensures data integrity and provides meaningful error messages.
"""

import re
from typing import Dict, Any, Tuple, List, Optional

# Valid planet names
VALID_PLANETS = {
    'SUN', 'MOON', 'MARS', 'MERCURY', 'J<PERSON>IT<PERSON>', 'VENUS', 'SATURN', 'RAHU', 'KETU'
}

# Valid house numbers
VALID_HOUSES = set(range(1, 13))  # 1-12

# Valid chart types
VALID_CHART_TYPES = {
    'D1', 'D2', 'D3', 'D4', 'D7', 'D9', 'D10', 'D12', 'D16', 'D20', 'D24', 'D27', 'D30'
}

# Valid operators
VALID_OPERATORS = {
    'IN', 'NOT_IN', 'WITH_RULING_PLANET', 'TOGETHER_WITH', 
    'IS_ASPECTING_BIRTH', 'WITH_STARS_OF', 'OR', 'AND', 'NOT'
}

def validate_request_data(data: Dict[str, Any]) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
    """
    Validate API request data.
    
    Args:
        data: Request data dictionary
        
    Returns:
        Tuple of (is_valid, validated_data, error_message)
    """
    if not isinstance(data, dict):
        return False, None, "Request data must be a JSON object"
    
    # Required fields
    required_fields = ['user_profile_id', 'member_profile_id', 'query']
    
    for field in required_fields:
        if field not in data:
            return False, None, f"Missing required field: {field}"
        
        if not data[field] or not str(data[field]).strip():
            return False, None, f"Field '{field}' cannot be empty"
    
    # Validate user_profile_id
    try:
        user_profile_id = str(data['user_profile_id']).strip()
        if not user_profile_id:
            return False, None, "user_profile_id cannot be empty"
    except (ValueError, TypeError):
        return False, None, "user_profile_id must be a valid string or number"
    
    # Validate member_profile_id
    try:
        member_profile_id = str(data['member_profile_id']).strip()
        if not member_profile_id:
            return False, None, "member_profile_id cannot be empty"
    except (ValueError, TypeError):
        return False, None, "member_profile_id must be a valid string or number"
    
    # Validate query
    query = str(data['query']).strip()
    if len(query) > 1000:
        return False, None, "Query is too long (maximum 1000 characters)"
    
    if len(query) < 3:
        return False, None, "Query is too short (minimum 3 characters)"
    
    # Validate chart_type (optional)
    chart_type = data.get('chart_type', 'D1')
    if chart_type and chart_type.upper() not in VALID_CHART_TYPES:
        return False, None, f"Invalid chart_type. Must be one of: {', '.join(VALID_CHART_TYPES)}"
    
    # Return validated data
    validated_data = {
        'user_profile_id': user_profile_id,
        'member_profile_id': member_profile_id,
        'query': query,
        'chart_type': chart_type.upper() if chart_type else 'D1'
    }
    
    return True, validated_data, None

def validate_query_syntax(query: str) -> Dict[str, Any]:
    """
    Validate query syntax and structure.
    
    Args:
        query: Query string to validate
        
    Returns:
        Dictionary with validation results
    """
    result = {
        'is_valid': True,
        'errors': [],
        'warnings': [],
        'parsed_conditions': []
    }
    
    try:
        # Basic syntax checks
        if not query or not query.strip():
            result['is_valid'] = False
            result['errors'].append("Query cannot be empty")
            return result
        
        query = query.strip()
        
        # Check for balanced parentheses
        if not _check_balanced_parentheses(query):
            result['is_valid'] = False
            result['errors'].append("Unbalanced parentheses in query")
        
        # Check for valid planet names
        planet_errors = _validate_planets_in_query(query)
        if planet_errors:
            result['errors'].extend(planet_errors)
            result['is_valid'] = False
        
        # Check for valid house numbers
        house_errors = _validate_houses_in_query(query)
        if house_errors:
            result['errors'].extend(house_errors)
            result['is_valid'] = False
        
        # Check for valid operators
        operator_errors = _validate_operators_in_query(query)
        if operator_errors:
            result['errors'].extend(operator_errors)
            result['is_valid'] = False
        
        # Parse basic conditions if syntax is valid
        if result['is_valid']:
            try:
                conditions = _parse_basic_conditions(query)
                result['parsed_conditions'] = conditions
            except Exception as e:
                result['warnings'].append(f"Could not parse all conditions: {str(e)}")
        
    except Exception as e:
        result['is_valid'] = False
        result['errors'].append(f"Unexpected error during validation: {str(e)}")
    
    return result

def _check_balanced_parentheses(query: str) -> bool:
    """Check if parentheses are balanced in the query."""
    count = 0
    for char in query:
        if char == '(':
            count += 1
        elif char == ')':
            count -= 1
            if count < 0:
                return False
    return count == 0

def _validate_planets_in_query(query: str) -> List[str]:
    """Validate planet names in the query."""
    errors = []
    
    # Find potential planet names (uppercase words)
    potential_planets = re.findall(r'\b[A-Z]{2,}\b', query)
    
    for planet in potential_planets:
        # Skip operators and house references
        if planet in VALID_OPERATORS:
            continue
        if planet.endswith('_HOUSE') or planet.endswith('_HOUSE_RULING_PLANET'):
            continue
        if planet in ['BIRTH', 'STARS_OF', 'RULING_PLANET']:
            continue
        
        # Check if it's a valid planet
        if planet not in VALID_PLANETS:
            # Check if it might be a typo
            suggestions = _get_planet_suggestions(planet)
            if suggestions:
                errors.append(f"Unknown planet '{planet}'. Did you mean: {', '.join(suggestions)}?")
            else:
                errors.append(f"Unknown planet '{planet}'. Valid planets: {', '.join(sorted(VALID_PLANETS))}")
    
    return errors

def _validate_houses_in_query(query: str) -> List[str]:
    """Validate house numbers in the query."""
    errors = []
    
    # Find house number patterns
    house_patterns = [
        r'\b(\d+)(?:st|nd|rd|th)?_House\b',  # 7th_House format
        r'\b(\d+)\b(?!\w)'  # Standalone numbers
    ]
    
    for pattern in house_patterns:
        matches = re.findall(pattern, query)
        for match in matches:
            try:
                house_num = int(match)
                if house_num not in VALID_HOUSES:
                    errors.append(f"Invalid house number '{house_num}'. Must be between 1 and 12")
            except ValueError:
                errors.append(f"Invalid house number format: '{match}'")
    
    return errors

def _validate_operators_in_query(query: str) -> List[str]:
    """Validate operators in the query."""
    errors = []
    
    # Check for unknown operators (sequences of uppercase letters with underscores)
    potential_operators = re.findall(r'\b[A-Z_]{2,}\b', query)
    
    for operator in potential_operators:
        # Skip planet names and house references
        if operator in VALID_PLANETS:
            continue
        if operator.endswith('_HOUSE') or operator.endswith('_HOUSE_RULING_PLANET'):
            continue
        
        # Check if it's a valid operator
        if operator not in VALID_OPERATORS and operator not in ['BIRTH', 'STARS_OF', 'RULING_PLANET']:
            suggestions = _get_operator_suggestions(operator)
            if suggestions:
                errors.append(f"Unknown operator '{operator}'. Did you mean: {', '.join(suggestions)}?")
            else:
                errors.append(f"Unknown operator '{operator}'. Valid operators: {', '.join(sorted(VALID_OPERATORS))}")
    
    return errors

def _get_planet_suggestions(planet: str) -> List[str]:
    """Get suggestions for misspelled planet names."""
    suggestions = []
    planet_lower = planet.lower()
    
    for valid_planet in VALID_PLANETS:
        # Simple similarity check
        if _calculate_similarity(planet_lower, valid_planet.lower()) > 0.6:
            suggestions.append(valid_planet)
    
    return suggestions[:3]  # Return top 3 suggestions

def _get_operator_suggestions(operator: str) -> List[str]:
    """Get suggestions for misspelled operators."""
    suggestions = []
    operator_lower = operator.lower()
    
    for valid_operator in VALID_OPERATORS:
        if _calculate_similarity(operator_lower, valid_operator.lower()) > 0.6:
            suggestions.append(valid_operator)
    
    return suggestions[:3]

def _calculate_similarity(s1: str, s2: str) -> float:
    """Calculate simple similarity between two strings."""
    if not s1 or not s2:
        return 0.0
    
    # Simple character overlap ratio
    common_chars = set(s1) & set(s2)
    total_chars = set(s1) | set(s2)
    
    return len(common_chars) / len(total_chars) if total_chars else 0.0

def _parse_basic_conditions(query: str) -> List[Dict[str, Any]]:
    """Parse basic conditions from the query for validation."""
    conditions = []
    
    # Simple condition extraction (this is basic - full parsing is in condition_parser.py)
    # Split by logical operators
    parts = re.split(r'\b(OR|AND|NOT)\b', query)
    
    for part in parts:
        part = part.strip()
        if part and part not in ['OR', 'AND', 'NOT']:
            # Remove parentheses for basic parsing
            clean_part = part.strip('()')
            
            # Try to identify condition type
            if 'IN' in clean_part:
                conditions.append({
                    'type': 'planet_house',
                    'condition': clean_part,
                    'valid': True
                })
            elif any(rel in clean_part for rel in ['WITH_RULING_PLANET', 'TOGETHER_WITH', 'IS_ASPECTING_BIRTH', 'WITH_STARS_OF']):
                conditions.append({
                    'type': 'relationship',
                    'condition': clean_part,
                    'valid': True
                })
    
    return conditions
