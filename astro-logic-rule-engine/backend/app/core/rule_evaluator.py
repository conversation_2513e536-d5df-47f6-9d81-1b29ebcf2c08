"""
AstroLogic Rule Engine - Rule Evaluator

This module handles the evaluation of individual astrological conditions
including planet-house relationships and complex astrological relationships.
"""

from typing import Dict, Any, Optional
from app.core.condition_parser import ParsedCondition
from app.astrology.planetary_relationships import (
    get_planet_relationship, is_planet_aspecting_planet
)
from app.astrology.house_systems import get_house_ruling_planet
from app.astrology.constants import get_nakshatra_lord, get_nakshatras_ruled_by_planet
from app.services.relationship_checker import RelationshipChecker

class RuleEvaluator:
    """
    Evaluates individual astrological conditions and rules.
    
    This class handles the evaluation of:
    - Basic planet-house conditions (JUPITER IN 7)
    - Relationship conditions (VENUS WITH_RULING_PLANET 7th_House_Ruling_Planet)
    - Complex astrological relationships using the 5-rule system
    """
    
    def __init__(self):
        self.relationship_checker = RelationshipChecker()
    
    def evaluate_planet_house_condition(self, condition: ParsedCondition, 
                                      planet_house_mapping: Dict[str, int]) -> Dict[str, Any]:
        """
        Evaluate basic planet-house conditions like 'J<PERSON>ITER IN 7'.
        
        Args:
            condition: Parsed condition object
            planet_house_mapping: Mapping of planets to their house positions
            
        Returns:
            Evaluation result dictionary
        """
        try:
            planet = condition.planet
            house_number = condition.house_number
            operator = condition.operator
            
            if not planet or house_number is None:
                return {
                    'condition': condition.raw_condition,
                    'result': False,
                    'error': 'Invalid planet or house number',
                    'type': 'planet_house'
                }
            
            # Get planet's current house position
            current_house = planet_house_mapping.get(planet)
            
            if current_house is None:
                return {
                    'condition': condition.raw_condition,
                    'result': False,
                    'error': f'Planet {planet} not found in chart',
                    'type': 'planet_house'
                }
            
            # Evaluate based on operator
            if operator == 'IN':
                result = current_house == house_number
            elif operator == 'NOT_IN':
                result = current_house != house_number
            else:
                return {
                    'condition': condition.raw_condition,
                    'result': False,
                    'error': f'Unknown operator: {operator}',
                    'type': 'planet_house'
                }
            
            return {
                'condition': condition.raw_condition,
                'result': result,
                'details': {
                    'planet': planet,
                    'current_house': current_house,
                    'target_house': house_number,
                    'operator': operator,
                    'evaluation': f'{planet} is in house {current_house}, target is {house_number}'
                },
                'type': 'planet_house'
            }
            
        except Exception as e:
            return {
                'condition': condition.raw_condition,
                'result': False,
                'error': f'Evaluation error: {str(e)}',
                'type': 'planet_house'
            }
    
    def evaluate_relationship_condition(self, condition: ParsedCondition,
                                      chart_data: Dict[str, Any],
                                      chart_type: str) -> Dict[str, Any]:
        """
        Evaluate relationship conditions using the 5-rule relationship system.
        
        Args:
            condition: Parsed condition object
            chart_data: Complete chart data
            chart_type: Chart type (D1, D2, etc.)
            
        Returns:
            Evaluation result dictionary
        """
        try:
            planet = condition.planet
            operator = condition.operator
            target = condition.target
            house_number = condition.house_number
            
            if not planet or not operator:
                return {
                    'condition': condition.raw_condition,
                    'result': False,
                    'error': 'Invalid planet or operator',
                    'type': 'relationship'
                }
            
            # Route to appropriate relationship evaluation method
            if operator == 'WITH_RULING_PLANET':
                return self._evaluate_with_ruling_planet(
                    planet, house_number, chart_data, chart_type, condition.raw_condition
                )
            
            elif operator == 'TOGETHER_WITH':
                return self._evaluate_together_with(
                    planet, target, chart_data, chart_type, condition.raw_condition
                )
            
            elif operator == 'IS_ASPECTING_BIRTH':
                return self._evaluate_aspecting_relationship(
                    planet, target, chart_data, chart_type, condition.raw_condition
                )
            
            elif operator == 'WITH_STARS_OF':
                return self._evaluate_nakshatra_relationship(
                    planet, target, chart_data, chart_type, condition.raw_condition
                )
            
            else:
                return {
                    'condition': condition.raw_condition,
                    'result': False,
                    'error': f'Unknown relationship operator: {operator}',
                    'type': 'relationship'
                }
                
        except Exception as e:
            return {
                'condition': condition.raw_condition,
                'result': False,
                'error': f'Relationship evaluation error: {str(e)}',
                'type': 'relationship'
            }
    
    def _evaluate_with_ruling_planet(self, planet: str, house_number: int,
                                   chart_data: Dict[str, Any], chart_type: str,
                                   raw_condition: str) -> Dict[str, Any]:
        """Evaluate WITH_RULING_PLANET relationships using 5-rule system."""
        if house_number is None:
            return {
                'condition': raw_condition,
                'result': False,
                'error': 'Invalid house number for ruling planet relationship',
                'type': 'relationship'
            }
        
        # Use comprehensive relationship checker
        relationship_result = self.relationship_checker.check_comprehensive_relationship(
            chart_data, planet, house_number, chart_type
        )
        
        return {
            'condition': raw_condition,
            'result': relationship_result['overall_result'],
            'details': relationship_result,
            'type': 'relationship',
            'relationship_type': 'with_ruling_planet'
        }
    
    def _evaluate_together_with(self, planet1: str, planet2: str,
                              chart_data: Dict[str, Any], chart_type: str,
                              raw_condition: str) -> Dict[str, Any]:
        """Evaluate TOGETHER_WITH (conjunction) relationships."""
        try:
            # Get planet house mapping
            planet_house_mapping = self._get_planet_house_mapping(chart_data, chart_type)
            
            house1 = planet_house_mapping.get(planet1)
            house2 = planet_house_mapping.get(planet2)
            
            if house1 is None or house2 is None:
                return {
                    'condition': raw_condition,
                    'result': False,
                    'error': f'Could not find house positions for {planet1} or {planet2}',
                    'type': 'relationship'
                }
            
            result = house1 == house2
            
            return {
                'condition': raw_condition,
                'result': result,
                'details': {
                    'planet1': planet1,
                    'planet2': planet2,
                    'house1': house1,
                    'house2': house2,
                    'evaluation': f'{planet1} in house {house1}, {planet2} in house {house2}'
                },
                'type': 'relationship',
                'relationship_type': 'conjunction'
            }
            
        except Exception as e:
            return {
                'condition': raw_condition,
                'result': False,
                'error': f'Conjunction evaluation error: {str(e)}',
                'type': 'relationship'
            }
    
    def _evaluate_aspecting_relationship(self, aspecting_planet: str, aspected_planet: str,
                                       chart_data: Dict[str, Any], chart_type: str,
                                       raw_condition: str) -> Dict[str, Any]:
        """Evaluate IS_ASPECTING_BIRTH relationships."""
        try:
            # Get planet house mapping
            planet_house_mapping = self._get_planet_house_mapping(chart_data, chart_type)
            
            aspecting_house = planet_house_mapping.get(aspecting_planet)
            aspected_house = planet_house_mapping.get(aspected_planet)
            
            if aspecting_house is None or aspected_house is None:
                return {
                    'condition': raw_condition,
                    'result': False,
                    'error': f'Could not find house positions for {aspecting_planet} or {aspected_planet}',
                    'type': 'relationship'
                }
            
            # Check if aspecting planet aspects the aspected planet
            result = is_planet_aspecting_planet(
                aspecting_planet, aspected_planet, aspecting_house, aspected_house
            )
            
            return {
                'condition': raw_condition,
                'result': result,
                'details': {
                    'aspecting_planet': aspecting_planet,
                    'aspected_planet': aspected_planet,
                    'aspecting_house': aspecting_house,
                    'aspected_house': aspected_house,
                    'evaluation': f'{aspecting_planet} (house {aspecting_house}) aspecting {aspected_planet} (house {aspected_house})'
                },
                'type': 'relationship',
                'relationship_type': 'aspect'
            }
            
        except Exception as e:
            return {
                'condition': raw_condition,
                'result': False,
                'error': f'Aspect evaluation error: {str(e)}',
                'type': 'relationship'
            }
    
    def _evaluate_nakshatra_relationship(self, planet: str, target: str,
                                       chart_data: Dict[str, Any], chart_type: str,
                                       raw_condition: str) -> Dict[str, Any]:
        """Evaluate WITH_STARS_OF (nakshatra) relationships."""
        try:
            # This is a simplified implementation
            # In a full system, we would need nakshatra data from the chart
            
            # For now, we'll use the nakshatra lord mapping
            if target.endswith('_House_Ruling_Planet'):
                # Extract house number and get ruling planet
                house_num = self._extract_house_number(target)
                if house_num:
                    ruling_planet = get_house_ruling_planet(house_num)
                    if ruling_planet:
                        target = ruling_planet
            
            # Get nakshatras ruled by the target planet
            target_nakshatras = get_nakshatras_ruled_by_planet(target)
            
            if not target_nakshatras:
                return {
                    'condition': raw_condition,
                    'result': False,
                    'error': f'No nakshatras found for {target}',
                    'type': 'relationship'
                }
            
            # In a full implementation, we would check if the planet is actually
            # in one of these nakshatras. For now, we'll return a simplified result.
            
            return {
                'condition': raw_condition,
                'result': True,  # Simplified - always true for demo
                'details': {
                    'planet': planet,
                    'target': target,
                    'target_nakshatras': target_nakshatras,
                    'evaluation': f'{planet} with stars of {target} (nakshatras: {", ".join(target_nakshatras[:3])})'
                },
                'type': 'relationship',
                'relationship_type': 'nakshatra'
            }
            
        except Exception as e:
            return {
                'condition': raw_condition,
                'result': False,
                'error': f'Nakshatra evaluation error: {str(e)}',
                'type': 'relationship'
            }
    
    def _get_planet_house_mapping(self, chart_data: Dict[str, Any], chart_type: str) -> Dict[str, int]:
        """Extract planet-house mapping from chart data."""
        # This would be implemented based on the actual chart data structure
        # For now, return a sample mapping for demonstration
        return {
            'SUN': 5,
            'MOON': 4,
            'MARS': 1,
            'MERCURY': 6,
            'JUPITER': 7,
            'VENUS': 2,
            'SATURN': 10,
            'RAHU': 8,
            'KETU': 2
        }
    
    def _extract_house_number(self, target: str) -> Optional[int]:
        """Extract house number from target string."""
        import re
        match = re.search(r'(\d+)', target)
        if match:
            try:
                return int(match.group(1))
            except ValueError:
                pass
        return None
