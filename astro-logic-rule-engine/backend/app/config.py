"""
AstroLogic Rule Engine - Configuration Settings

This module contains configuration classes for different environments.
Each configuration class defines settings specific to its environment.
"""

import os
from datetime import timedelta

class BaseConfig:
    """Base configuration class with common settings."""
    
    # Application settings
    SECRET_KEY = os.environ.get('SECRET_KEY', 'astro-logic-development-key')
    
    # JWT settings
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'astro-logic-jwt-secret')
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    
    # CORS settings
    CORS_ORIGINS = ['http://localhost:3000', 'http://127.0.0.1:3000']
    
    # Astrological calculation settings
    DEFAULT_CHART_TYPE = 'D1'
    SUPPORTED_CHART_TYPES = ['D1', 'D2', 'D3', 'D4', 'D7', 'D9', 'D10', 'D12', 'D16', 'D20', 'D24', 'D27', 'D30']
    
    # Rule engine settings
    MAX_QUERY_LENGTH = 1000
    MAX_CONDITIONS_PER_QUERY = 50
    DEFAULT_SUCCESS_THRESHOLD = 70.0
    
    # Logging settings
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Performance settings
    QUERY_TIMEOUT_SECONDS = 30
    MAX_CONCURRENT_REQUESTS = 100
    
    # Sample data settings
    USE_SAMPLE_DATA = True
    SAMPLE_DATA_PATH = os.path.join(os.path.dirname(__file__), 'data', 'samples')

class DevelopmentConfig(BaseConfig):
    """Development environment configuration."""
    
    DEBUG = True
    TESTING = False
    
    # Development-specific settings
    LOG_LEVEL = 'DEBUG'
    
    # Enable detailed error messages
    PROPAGATE_EXCEPTIONS = True
    
    # Development database (if needed)
    DATABASE_URL = os.environ.get('DEV_DATABASE_URL', 'sqlite:///dev_astro_logic.db')

class ProductionConfig(BaseConfig):
    """Production environment configuration."""
    
    DEBUG = False
    TESTING = False
    
    # Production security settings
    SECRET_KEY = os.environ.get('SECRET_KEY', 'astro-logic-production-key-change-in-production')
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'astro-logic-jwt-key-change-in-production')
    
    # Production CORS settings
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '').split(',')
    
    # Production logging
    LOG_LEVEL = 'WARNING'
    
    # Production database
    DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///astro_logic_production.db')
    
    # Disable sample data in production
    USE_SAMPLE_DATA = False

class TestingConfig(BaseConfig):
    """Testing environment configuration."""
    
    DEBUG = True
    TESTING = True
    
    # Testing-specific settings
    WTF_CSRF_ENABLED = False
    
    # Use in-memory database for testing
    DATABASE_URL = 'sqlite:///:memory:'
    
    # Shorter token expiration for testing
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(minutes=15)
    
    # Enable sample data for testing
    USE_SAMPLE_DATA = True
    
    # Faster timeouts for testing
    QUERY_TIMEOUT_SECONDS = 5

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
