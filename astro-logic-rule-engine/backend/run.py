#!/usr/bin/env python3
"""
AstroLogic Rule Engine - Application Entry Point

This is the main entry point for the AstroLogic Rule Engine application.
It initializes the Flask application and starts the development server.
"""

import os
from app import create_app

def main():
    """Main application entry point."""
    # Create Flask application instance
    app = create_app()
    
    # Get configuration from environment variables
    host = os.environ.get('FLASK_HOST', '127.0.0.1')
    port = int(os.environ.get('FLASK_PORT', 5000))
    debug = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    print("=" * 60)
    print("🌟 AstroLogic Rule Engine Starting...")
    print("=" * 60)
    print(f"🚀 Server: http://{host}:{port}")
    print(f"🔧 Debug Mode: {debug}")
    print(f"📡 API Endpoint: http://{host}:{port}/api/rule-engine/")
    print("=" * 60)
    print("📖 Available Endpoints:")
    print("   POST /api/rule-engine/          - Main rule evaluation")
    print("   GET  /api/rule-engine/health    - Health check")
    print("   GET  /api/rule-engine/examples  - Query examples")
    print("=" * 60)
    
    # Start the Flask development server
    try:
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")

if __name__ == '__main__':
    main()
