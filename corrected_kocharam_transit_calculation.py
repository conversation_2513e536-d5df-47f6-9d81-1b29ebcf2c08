"""
CORRECTED KOCHARAM Degree-Based Transit Start and End Date Calculations
This fixes the issues found in the original implementation
"""

from datetime import datetime, timedelta
from dateutil import parser

def calculate_transit_start_and_end_dates_corrected(planet_name, current_longitude, target_house_number, target_house_angle, reference_date, rotation_period):
    """
    CORRECTED: Calculate both transit start AND end dates using degree-based calculations

    Args:
        planet_name (str): Planet name (e.g., "J<PERSON><PERSON><PERSON>")
        current_longitude (float): Current planet position in degrees
        target_house_number (int): Target house number (1-12)
        target_house_angle (float): Starting angle of target house
        reference_date (datetime): Reference date for calculations
        rotation_period (int): Planet's rotation period in days

    Returns:
        dict: Complete transit information with start and end dates
    """

    print(f"🔬 CORRECTED Transit Calculation for {planet_name}")
    print(f"   Current position: {current_longitude:.2f}°")
    print(f"   Target house {target_house_number}: {target_house_angle:.2f}° to {(target_house_angle + 30) % 360:.2f}°")

    # Step 1: Calculate angular distance to house entry point
    angular_distance_to_entry = (target_house_angle - current_longitude) % 360
    print(f"   Angular distance to house entry: {angular_distance_to_entry:.2f}°")

    # Step 2: Calculate time to reach house entry
    transit_time_to_entry = (angular_distance_to_entry / 360.0) * rotation_period
    print(f"   Time to house entry: {transit_time_to_entry:.2f} days")

    # Step 3: Calculate transit start date (when planet enters house)
    transit_start_date = reference_date + timedelta(days=transit_time_to_entry)
    print(f"   Transit START date: {transit_start_date.strftime('%Y-%m-%d')}")

    # Step 4: Calculate house transit duration (30° span)
    house_span_degrees = 30.0
    transit_duration_days = (house_span_degrees / 360.0) * rotation_period
    print(f"   House transit duration: {transit_duration_days:.2f} days")

    # Step 5: Calculate transit end date (when planet exits house)
    transit_end_date = transit_start_date + timedelta(days=transit_duration_days)
    print(f"   Transit END date: {transit_end_date.strftime('%Y-%m-%d')}")

    # Step 6: Check if planet is already in the target house
    house_end_angle = (target_house_angle + 30) % 360
    already_in_house = is_planet_in_house_range(current_longitude, target_house_angle, house_end_angle)

    if already_in_house:
        # Planet is already in house, so transit is currently active
        print(f"   ✅ Planet is ALREADY in {target_house_number}th house!")
        transit_start_date = reference_date  # Transit already started

        # Calculate remaining time in house
        if target_house_angle <= house_end_angle:  # Normal case
            remaining_degrees = house_end_angle - current_longitude
        else:  # House crosses 0° boundary
            if current_longitude >= target_house_angle:
                remaining_degrees = (360 - current_longitude) + house_end_angle
            else:
                remaining_degrees = house_end_angle - current_longitude

        remaining_days = (remaining_degrees / 360.0) * rotation_period
        transit_end_date = reference_date + timedelta(days=remaining_days)
        print(f"   🔄 Remaining time in house: {remaining_days:.2f} days")
        print(f"   📅 Updated transit END date: {transit_end_date.strftime('%Y-%m-%d')}")

    return {
        "planet": planet_name,
        "target_house": target_house_number,
        "current_longitude": current_longitude,
        "target_house_angle": target_house_angle,
        "angular_distance_to_entry": angular_distance_to_entry,
        "transit_time_to_entry_days": transit_time_to_entry,
        "transit_duration_days": transit_duration_days,
        "transit_start_date": transit_start_date,
        "transit_end_date": transit_end_date,
        "already_in_house": already_in_house,
        "reference_date": reference_date
    }

def calculate_aspect_start_and_end_dates_corrected(planet_name, current_longitude, target_house_number, target_house_angle, reference_date, rotation_period):
    """
    CORRECTED: Calculate aspect start and end dates for all aspects

    Returns:
        list: List of aspect transit information for each aspect
    """

    print(f"🎯 CORRECTED Aspect Calculation for {planet_name} -> {target_house_number}th house")

    # Define aspects for each planet
    planet_aspects = {
        'JUPITER': [5, 7, 9],
        'SATURN': [3, 7, 10],
        'MARS': [4, 7, 8],
        'RAHU': [5, 7, 9],
        'KETU': [5, 7, 9],
        'SUN': [7], 'MOON': [7], 'MERCURY': [7], 'VENUS': [7]
    }

    aspect_numbers = planet_aspects.get(planet_name.upper(), [7])
    print(f"   {planet_name} aspects: {aspect_numbers}")

    aspect_results = []

    for aspect_number in aspect_numbers:
        print(f"\n   📐 Calculating {aspect_number}th aspect:")

        # Step 1: Calculate aspect angle
        aspect_angle = ((aspect_number - 1) * 30 + current_longitude) % 360
        print(f"      Aspect angle: ({aspect_number}-1)*30 + {current_longitude:.2f} = {aspect_angle:.2f}°")

        # Step 2: Check if aspect already falls in target house range
        house_end_angle = (target_house_angle + 30) % 360
        aspect_in_target_house = is_planet_in_house_range(aspect_angle, target_house_angle, house_end_angle)

        if aspect_in_target_house:
            print(f"      ✅ {aspect_number}th aspect is ALREADY affecting {target_house_number}th house!")
            aspect_start_date = reference_date
            aspect_end_date = reference_date  # Instantaneous aspect effect
        else:
            # Step 3: Calculate distance to target house
            distance_to_target = calculate_distance_to_house_range_corrected(aspect_angle, target_house_angle)
            print(f"      Distance to target house: {distance_to_target:.2f}°")

            # Step 4: Calculate time to reach aspect position
            time_to_aspect = (distance_to_target / 360.0) * rotation_period
            print(f"      Time to reach aspect: {time_to_aspect:.2f} days")

            # Step 5: Calculate aspect dates
            aspect_start_date = reference_date + timedelta(days=time_to_aspect)
            aspect_end_date = aspect_start_date  # Aspects are typically instantaneous
            print(f"      Aspect date: {aspect_start_date.strftime('%Y-%m-%d')}")

        aspect_results.append({
            "aspect_number": aspect_number,
            "aspect_angle": aspect_angle,
            "distance_to_target": distance_to_target if not aspect_in_target_house else 0.0,
            "time_to_aspect_days": 0.0 if aspect_in_target_house else time_to_aspect,
            "aspect_start_date": aspect_start_date,
            "aspect_end_date": aspect_end_date,
            "already_active": aspect_in_target_house
        })

    # Find fastest aspect
    active_aspects = [a for a in aspect_results if a['already_active']]
    if active_aspects:
        fastest_aspect = active_aspects[0]  # Already active
        print(f"\n   🚀 Fastest aspect: {fastest_aspect['aspect_number']}th (ALREADY ACTIVE)")
    else:
        fastest_aspect = min(aspect_results, key=lambda x: x['time_to_aspect_days'])
        print(f"\n   🚀 Fastest aspect: {fastest_aspect['aspect_number']}th aspect ({fastest_aspect['time_to_aspect_days']:.1f} days)")

    return aspect_results, fastest_aspect

def is_planet_in_house_range(planet_longitude, house_start_angle, house_end_angle):
    """
    CORRECTED: Check if planet is within house range, handling 360° wraparound
    """
    if house_start_angle <= house_end_angle:
        # Normal case: house doesn't cross 0°
        return house_start_angle <= planet_longitude <= house_end_angle
    else:
        # House crosses 0° boundary (e.g., 330° to 30°)
        return planet_longitude >= house_start_angle or planet_longitude <= house_end_angle

def calculate_distance_to_house_range_corrected(aspect_angle, target_house_start_angle):
    """
    CORRECTED: Calculate shortest distance from aspect angle to target house range
    """
    target_house_end_angle = (target_house_start_angle + 30) % 360

    # Check if already in range
    if is_planet_in_house_range(aspect_angle, target_house_start_angle, target_house_end_angle):
        return 0.0

    # Calculate distance to house start
    distance_to_start = (target_house_start_angle - aspect_angle) % 360
    return distance_to_start

def generate_multiple_transit_cycles_corrected(initial_transit_result, rotation_period, end_date, max_cycles=10):
    """
    CORRECTED: Generate multiple transit cycles with proper start and end dates
    """

    print(f"\n🔄 Generating multiple transit cycles up to {end_date.strftime('%Y-%m-%d')}")

    cycles = []
    current_start = initial_transit_result['transit_start_date']
    current_end = initial_transit_result['transit_end_date']
    cycle_number = 1

    while current_start <= end_date and cycle_number <= max_cycles:
        cycles.append({
            "cycle_number": cycle_number,
            "transit_start_date": current_start,
            "transit_end_date": current_end,
            "duration_days": (current_end - current_start).days
        })

        print(f"   Cycle {cycle_number}: {current_start.strftime('%Y-%m-%d')} to {current_end.strftime('%Y-%m-%d')}")

        # Calculate next cycle (planet completes full orbit and returns)
        next_cycle_start = current_start + timedelta(days=rotation_period)
        next_cycle_end = current_end + timedelta(days=rotation_period)

        current_start = next_cycle_start
        current_end = next_cycle_end
        cycle_number += 1

    print(f"   Generated {len(cycles)} transit cycles")
    return cycles

# Test the corrected implementation
def test_corrected_kocharam():
    """Test the corrected KOCHARAM implementation"""

    print("🧪 TESTING CORRECTED KOCHARAM IMPLEMENTATION")
    print("=" * 80)

    # Test case: JUPITER transit to 7th house
    test_data = {
        "planet_name": "JUPITER",
        "current_longitude": 104.5,  # Jupiter in Cancer
        "target_house_number": 7,
        "target_house_angle": 330.0,  # 7th house in Pisces
        "reference_date": datetime(2025, 1, 1),
        "rotation_period": 4333  # Jupiter period
    }

    # Test 1: Transit calculation
    print("\n1️⃣ TRANSIT CALCULATION:")
    transit_result = calculate_transit_start_and_end_dates_corrected(**test_data)

    # Test 2: Aspect calculation
    print("\n2️⃣ ASPECT CALCULATION:")
    aspect_results, fastest_aspect = calculate_aspect_start_and_end_dates_corrected(**test_data)

    # Test 3: Multiple cycles
    print("\n3️⃣ MULTIPLE TRANSIT CYCLES:")
    end_date = datetime(2035, 12, 31)  # 10 years
    cycles = generate_multiple_transit_cycles_corrected(transit_result, test_data["rotation_period"], end_date)

    print("\n" + "=" * 80)
    print("🎯 CORRECTED IMPLEMENTATION SUMMARY")
    print("=" * 80)
    print(f"✅ Transit start date: {transit_result['transit_start_date'].strftime('%Y-%m-%d')}")
    print(f"✅ Transit end date: {transit_result['transit_end_date'].strftime('%Y-%m-%d')}")
    print(f"✅ Transit duration: {transit_result['transit_duration_days']:.1f} days")
    print(f"✅ Fastest aspect: {fastest_aspect['aspect_number']}th aspect")
    print(f"✅ Multiple cycles: {len(cycles)} cycles generated")

    return transit_result, aspect_results, cycles

if __name__ == "__main__":
    test_corrected_kocharam()
