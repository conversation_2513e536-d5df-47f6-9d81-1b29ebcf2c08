# FL User Profile - MongoDB Integration

This directory contains the user profile management system with MongoDB integration.

## Structure

- `app/` - Main application code
- `app/services/horoscope/` - Horoscope-related services
  - `chart/` - Chart generation services
  - `match/` - Marriage matching services  
  - `prediction/` - Prediction services
  - `transit/` - Transit calculation services

## Setup

1. Install dependencies: `pip install -r requirements.txt`
2. Configure MongoDB connection
3. Run: `python run.py`

## Features

- User profile management
- Member profile management
- Astrological chart data storage
- MongoDB collections integration
