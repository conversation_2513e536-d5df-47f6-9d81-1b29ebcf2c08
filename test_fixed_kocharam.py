#!/usr/bin/env python3
"""
Test script to verify the FIXED KOCHARAM implementation
"""

import requests
import json
import time

def test_fixed_kocharam():
    """Test the FIXED KOCHARAM implementation"""
    
    # API endpoint
    base_url = "http://localhost:5003"
    endpoint = f"{base_url}/api/rule-engine/"
    
    # Your original query
    test_data = {
        "user_profile_id": 1,
        "member_profile_id": 1,
        "query": "(7th_House_Ruling_Planet Bhukti_Dates)AND KOCHARAM_FILTER(JUPITER in 7th_House)",
        "chart_type": "D1"
    }
    
    print("🔧 TESTING FIXED KOCHARAM IMPLEMENTATION")
    print("=" * 60)
    print(f"Query: {test_data['query']}")
    print()
    
    print("🎯 EXPECTED FIXES:")
    print("-" * 25)
    print("✅ Separate start and end dates (not same date)")
    print("✅ Real timing calculations (not static 12:00:00)")
    print("✅ Consistent validation logic")
    print("✅ Proper chart generation and storage")
    print("✅ Degree-based calculation with proper ranges")
    print()
    
    try:
        print("📡 Sending API Request...")
        start_time = time.time()
        
        response = requests.post(endpoint, json=test_data, timeout=60)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"⏱️  Execution Time: {execution_time:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        print()
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ SUCCESS - Analyzing FIXED KOCHARAM Results:")
            print("=" * 50)
            
            if 'result' in result and 'dasha_dates' in result['result']:
                dasha_dates = result['result']['dasha_dates']
                print(f"Total Periods: {len(dasha_dates)}")
                
                # Find periods with KOCHARAM filter
                kocharam_periods = [p for p in dasha_dates if 'kocharam_filter' in p]
                print(f"Periods with KOCHARAM: {len(kocharam_periods)}")
                
                if kocharam_periods:
                    print("\n🔍 ANALYZING FIRST FIXED KOCHARAM PERIOD:")
                    print("-" * 50)
                    
                    first_period = kocharam_periods[0]
                    kocharam_data = first_period['kocharam_filter']
                    
                    # Check calculation method
                    calc_method = kocharam_data.get('calculation_method', 'Unknown')
                    print(f"📊 Calculation Method: {calc_method}")
                    
                    # Check if using FIXED method
                    is_fixed = 'FIXED' in calc_method.upper()
                    print(f"🔧 Using FIXED Method: {'✅ YES' if is_fixed else '❌ NO'}")
                    
                    # Test Fix 1: Separate start and end dates
                    start_date = kocharam_data.get('predicted_start_date')
                    end_date = kocharam_data.get('predicted_end_date')
                    
                    print(f"\n🔧 FIX 1: SEPARATE DATES")
                    print(f"   Start Date: {start_date}")
                    print(f"   End Date: {end_date}")
                    
                    dates_different = start_date != end_date
                    print(f"   Different Dates: {'✅ FIXED' if dates_different else '❌ STILL BROKEN'}")
                    
                    # Test Fix 2: Real timing calculations
                    start_timing = kocharam_data.get('predicted_start_timing')
                    end_timing = kocharam_data.get('predicted_end_timing')
                    
                    print(f"\n🔧 FIX 2: REAL TIMING")
                    print(f"   Start Timing: {start_timing}")
                    print(f"   End Timing: {end_timing}")
                    
                    # Check if timing is not static 12:00:00
                    start_not_static = start_timing and '12:00:00' not in str(start_timing)
                    end_not_static = end_timing and '12:00:00' not in str(end_timing)
                    timing_fixed = start_not_static or end_not_static
                    print(f"   Real Timing: {'✅ FIXED' if timing_fixed else '❌ STILL STATIC'}")
                    
                    # Test Fix 3: Consistent validation logic
                    validation = kocharam_data.get('validation', False)
                    transit_found = kocharam_data.get('transit_found', False)
                    start_sign_val = kocharam_data.get('start_sign_validation', False)
                    end_sign_val = kocharam_data.get('end_sign_validation', False)
                    
                    print(f"\n🔧 FIX 3: CONSISTENT VALIDATION")
                    print(f"   Overall Validation: {validation}")
                    print(f"   Transit Found: {transit_found}")
                    print(f"   Start Sign Valid: {start_sign_val}")
                    print(f"   End Sign Valid: {end_sign_val}")
                    
                    # Check for consistency
                    validation_consistent = True
                    if validation and not transit_found:
                        validation_consistent = False
                        print("   ❌ Inconsistent: validation=True but transit_found=False")
                    if not validation and start_sign_val:
                        validation_consistent = False
                        print("   ❌ Inconsistent: validation=False but start_sign_val=True")
                    
                    print(f"   Validation Logic: {'✅ FIXED' if validation_consistent else '❌ STILL INCONSISTENT'}")
                    
                    # Test Fix 4: Chart generation
                    start_chart = kocharam_data.get('predicted_start_chart')
                    end_chart = kocharam_data.get('predicted_end_chart')
                    
                    print(f"\n🔧 FIX 4: CHART GENERATION")
                    print(f"   Start Chart: {'✅ PRESENT' if start_chart else '❌ MISSING'}")
                    print(f"   End Chart: {'✅ PRESENT' if end_chart else '❌ MISSING'}")
                    
                    chart_fixed = start_chart is not None or end_chart is not None
                    print(f"   Chart Data: {'✅ FIXED' if chart_fixed else '❌ STILL MISSING'}")
                    
                    # Test Fix 5: Longitude values
                    start_long = kocharam_data.get('predicted_start_longitude')
                    end_long = kocharam_data.get('predicted_end_longitude')
                    
                    print(f"\n🔧 FIX 5: LONGITUDE VALUES")
                    print(f"   Start Longitude: {start_long}°")
                    print(f"   End Longitude: {end_long}°")
                    
                    longitudes_different = start_long != end_long
                    print(f"   Different Longitudes: {'✅ FIXED' if longitudes_different else '❌ STILL SAME'}")
                    
                    # Overall assessment
                    fixes_working = [
                        dates_different,
                        timing_fixed,
                        validation_consistent,
                        chart_fixed,
                        longitudes_different
                    ]
                    
                    fixes_count = sum(fixes_working)
                    total_fixes = len(fixes_working)
                    
                    print(f"\n🎯 OVERALL ASSESSMENT:")
                    print(f"   Fixes Working: {fixes_count}/{total_fixes}")
                    print(f"   Success Rate: {(fixes_count/total_fixes)*100:.1f}%")
                    
                    if fixes_count == total_fixes:
                        print("   🎉 ALL FIXES WORKING! KOCHARAM IS FULLY FIXED!")
                    elif fixes_count >= 3:
                        print("   ✅ MOST FIXES WORKING! KOCHARAM IS MOSTLY FIXED!")
                    else:
                        print("   ❌ FIXES NOT WORKING! KOCHARAM STILL NEEDS WORK!")
                    
                    # Show all transit periods
                    transit_periods = kocharam_data.get('all_transit_periods', [])
                    print(f"\n📊 TRANSIT PERIODS: {len(transit_periods)}")
                    for i, period in enumerate(transit_periods, 1):
                        print(f"   Period {i}: {period.get('date')} at {period.get('longitude')}° in {period.get('sign')} ({period.get('degree_range')})")
                
                else:
                    print("❌ No KOCHARAM periods found")
            else:
                print("❌ No dasha_dates in result")
                
        else:
            print("❌ ERROR Response:")
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Flask server not running")
        print("💡 Start the server with: python run.py")
        
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: Request took too long")
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")

if __name__ == "__main__":
    test_fixed_kocharam()
