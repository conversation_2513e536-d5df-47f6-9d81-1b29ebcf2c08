<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Query Processing Walkthrough</title>
    
    <style>
        @media print {
            body { margin: 0.5in; }
            h1 { page-break-before: always; }
            table { page-break-inside: avoid; }
            pre { page-break-inside: avoid; }
        }
        
        body {
            font-family: 'Georgia', 'Times New Roman', serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 5px;
            margin-top: 25px;
        }
        
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            font-size: 12px;
        }
        
        th, td {
            border: 1px solid #bdc3c7;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #ecf0f1;
            font-weight: bold;
        }
        
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
        }
        
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 11px;
            border: 1px solid #e9ecef;
        }
        
        pre code {
            background: none;
            padding: 0;
        }
        
        ul, ol {
            margin: 10px 0;
            padding-left: 30px;
        }
        
        li {
            margin: 5px 0;
        }
        
        .print-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 20px;
        }
        
        .print-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .toc {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .toc h2 {
            margin-top: 0;
            border: none;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 8px 0;
        }
        
        .toc a {
            text-decoration: none;
            color: #3498db;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
    
</head>
<body>
    <div class="print-header">
        <h1>Fortune Lens Rule Engine</h1>
        <h2>Sample Query Processing Walkthrough</h2>
        <p>Generated on /Users/<USER>/PycharmProjects/fortune_lens</p>
    </div>
    
    <h1>Sample Query Processing Walkthrough</h1>

<h2>Query Analysis</h2>
<pre><code>{
  "user_profile_id": 100001,
  "member_profile_id": 1,
  "query": "(7th_House_Ruling_Planet Bhukti_Dates AND 7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates) AND Member_Age >= 23 AND <= 26 AND KOCHARAM_FILTER(JUPITER ASPECT 7th_House)",
  "chart_type": "D1"
}</code></pre>

<h2>Step-by-Step Processing Flow</h2>

<h3>1. Entry Point: <code>process_rule_engine_request()</code></h3>
<ul>
<li><strong>Function Called</strong>: <code>process_rule_engine_request(data)</code></li>
<li><strong>Action</strong>: Validates input data and detects query type</li>
<li><strong>Detection Result</strong>: Contains "Bhukti_Dates" → Routes to dasha-based processing</li>
<li><strong>Next Function</strong>: <code>parse_and_evaluate_dasha_query()</code></li>
</ol>

<h3>2. Query Parsing: <code>parse_and_evaluate_dasha_query()</code></h3>
<ul>
<li><strong>Function Called</strong>: <code>parse_and_evaluate_dasha_query(chart_data, query, "D1", 100001, 1)</code></li>
<li><strong>Actions</strong>:</li>
<li>Extract age constraints using regex: <code>r'Member_Age\s+>=\s+(\d+)\s+AND\s+<=\s+(\d+)'</code></li>
<li><strong>Result</strong>: <code>age_constraints = {"min_age": 23, "max_age": 26}</code></li>
<li>Remove age constraints from query string</li>
<li>Extract KOCHARAM filter using <code>parse_kocharam_filter(query)</code></li>
<li><strong>Result</strong>: <code>kocharam_condition = "JUPITER ASPECT 7th_House"</code></li>
<li><strong>Cleaned Query</strong>: <code>"(7th_House_Ruling_Planet Bhukti_Dates AND 7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates)"</code></li>
</ol>

<h3>3. Logical Operator Parsing</h3>
<ul>
<li><strong>Function Called</strong>: <code>split_by_operator(query, 'OR')</code></li>
<li><strong>Result</strong>: No OR operators found, single OR part</li>
<li><strong>Function Called</strong>: <code>split_by_operator(or_part, 'AND')</code></li>
<li><strong>Result</strong>: Two AND conditions:</li>
<li><code>"7th_House_Ruling_Planet Bhukti_Dates"</code></li>
<li><code>"7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates"</code></li>
</ol>

<h3>4. Individual Condition Processing</h3>

<h4>Condition 1: <code>7th_House_Ruling_Planet Bhukti_Dates</code></h4>
<ul>
<li><strong>Function Called</strong>: <code>parse_dasha_condition("7th_House_Ruling_Planet Bhukti_Dates")</code></li>
<li><strong>Result</strong>: <code>("HOUSE_RULING_PLANET_BHUKTI_DATES", {"house_number": 7, "dasha_type": "bhukti_dasha"})</code></li>
<li><strong>Function Called</strong>: <code>evaluate_dasha_condition("HOUSE_RULING_PLANET_BHUKTI_DATES", parameters, chart_data, 100)</code></li>
<li><strong>Process</strong>:</li>
<li>Get 7th house ruling planet from chart data using <code>get_house_sign_and_ruling_planet_from_chart(chart_data, 7)</code></li>
<li><strong>Example Result</strong>: 7th house is "Meenam" ruled by "JUPITER"</li>
<li>Get bhukti periods for JUPITER using <code>get_dasha_periods_for_planet(chart_data, "JUPITER", "bhukti_dasha")</code></li>
<li><strong>Example Result</strong>: List of periods where JUPITER appears as bhukti lord</li>
</ol>

<h4>Condition 2: <code>7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates</code></h4>
<ul>
<li><strong>Function Called</strong>: <code>parse_dasha_condition("7th_House_Ruling_Planet WITH_STARS_OF Bhukti_Dates")</code></li>
<li><strong>Result</strong>: <code>("HOUSE_RULING_PLANET_WITH_STARS_OF_BHUKTI_DATES", {"house_number": 7, "dasha_type": "bhukti_dasha"})</code></li>
<li><strong>Function Called</strong>: <code>evaluate_dasha_condition("HOUSE_RULING_PLANET_WITH_STARS_OF_BHUKTI_DATES", parameters, chart_data, 100)</code></li>
<li><strong>Process</strong>:</li>
<li>Get 7th house ruling planet: "JUPITER"</li>
<li>Get JUPITER's nakshatra from chart data using <code>get_planet_nakshatra_from_chart(chart_data, "JUPITER")</code></li>
<li><strong>Example Result</strong>: JUPITER in "THIRUVADIRAI" nakshatra</li>
<li>Get nakshatra lord using <code>get_nakshatra_lord("THIRUVADIRAI")</code></li>
<li><strong>Example Result</strong>: THIRUVADIRAI is ruled by "RAHU"</li>
<li>Find all planets in RAHU's nakshatras using <code>get_planets_with_stars_of_planet(chart_data, "RAHU")</code></li>
<li><strong>Example Result</strong>: ["MARS", "VENUS"] are in RAHU's nakshatras</li>
<li>Get bhukti periods for these planets</li>
</ol>

<h3>5. AND Logic Evaluation</h3>
<ul>
<li><strong>Logic</strong>: Both conditions must be true</li>
<li><strong>Result</strong>: Combine dasha periods from both conditions</li>
<li><strong>Deduplication</strong>: Remove duplicate periods and sort by start date</li>
</ol>

<h3>6. Age Filtering Application</h3>
<ul>
<li><strong>Function Called</strong>: <code>filter_dasha_periods_by_age(dasha_periods, chart_data, 23, 26)</code></li>
<li><strong>Process</strong>:</li>
<li>Get birth date from chart data using <code>get_member_birth_date_from_chart_data(chart_data)</code></li>
<li><strong>Example</strong>: Birth date = "1976-08-22 19:27:04"</li>
<li>For each dasha period, calculate age during that period</li>
<li><strong>Example</strong>: Period "1999-01-15 to 2000-02-10" → Age 22-23 → Overlaps with 23-26 range</li>
<li>Filter periods where person's age overlaps with 23-26 range</li>
</ol>

<h3>7. KOCHARAM Filter Processing</h3>
<ul>
<li><strong>Function Called</strong>: <code>process_kocharam_filter(filtered_dasha_dates, "JUPITER ASPECT 7th_House", chart_data, 100001, 1, True, age_constraints)</code></li>
</ol>

<h4>7.1 KOCHARAM Condition Parsing</h4>
<ul>
<li><strong>Function Called</strong>: <code>parse_kocharam_condition("JUPITER ASPECT 7th_House")</code></li>
<li><strong>Result</strong>: <code>{"type": "aspect", "planet": "JUPITER", "house": 7, "valid": True}</code></li>
</ol>

<h4>7.2 Enhanced KOCHARAM Algorithm Application</h4>
<ul>
<li><strong>Function Called</strong>: <code>apply_enhanced_kocharam_algorithm(dasha_periods, "JUPITER", 7, first_dasha_date, last_dasha_date, chart_data, birth_place_data, "aspect", age_start_date)</code></li>
</ol>

<strong>Step 2: Calculate Current Planet Position</strong>
<ul>
<li><strong>Reference Date</strong>: Age 23 start date = "1999-08-22 19:27:04"</li>
<li><strong>Function Called</strong>: <code>get_planet_position_on_date("JUPITER", "1999-08-22 19:27:04", birth_place_data)</code></li>
<li><strong>Example Result</strong>: JUPITER longitude = 45.5°</li>
</ol>

<strong>Step 3: Determine Target House Angle</strong>
<ul>
<li><strong>Function Called</strong>: <code>get_target_house_angle(chart_data, 7)</code></li>
<li><strong>Process</strong>: Get 7th house starting angle from user's birth chart</li>
<li><strong>Example Result</strong>: 7th house (Meenam) starts at 330°</li>
</ol>

<strong>Step 4: Calculate Aspect Angles</strong>
<ul>
<li><strong>JUPITER Aspects</strong>: [5, 7, 9] (5th, 7th, 9th aspects)</li>
<li><strong>Aspect Angle Calculation</strong>: <code>((aspect_number - 1) × 30 + current_longitude) % 360</code></li>
<li><strong>Results</strong>:</li>
<li>5th Aspect: <code>((5-1) × 30 + 45.5) % 360 = 165.5°</code></li>
<li>7th Aspect: <code>((7-1) × 30 + 45.5) % 360 = 225.5°</code></li>
<li>9th Aspect: <code>((9-1) × 30 + 45.5) % 360 = 285.5°</code></li>
</ol>

<strong>Step 5: Calculate Distance to Target House</strong>
<ul>
<li><strong>Target House Range</strong>: 330° - 360° (Meenam)</li>
<li><strong>Distance Calculations</strong>:</li>
<li>5th Aspect (165.5°) to 330°: 164.5°</li>
<li>7th Aspect (225.5°) to 330°: 104.5°</li>
<li>9th Aspect (285.5°) to 330°: 44.5° ← <strong>Fastest</strong></li>
</ol>

<strong>Step 6: Calculate Transit Time</strong>
<ul>
<li><strong>JUPITER Rotation Period</strong>: 4333 days</li>
<li><strong>Formula</strong>: <code>time_taken = 4333 × (44.5 / 360) = 535.8 days</code></li>
<li><strong>First Transit Date</strong>: "1999-08-22" + 535.8 days = "2001-02-09"</li>
</ol>

<strong>Step 7-9: Generate Transit Array and Find Overlaps</strong>
<ul>
<li><strong>Function Called</strong>: <code>generate_complete_transit_array(first_transit_date, 4333, last_dasha_date)</code></li>
<li><strong>Process</strong>: Generate all JUPITER aspect transits to 7th house during dasha periods</li>
<li><strong>Function Called</strong>: <code>find_overlapping_periods(dasha_periods, transit_dates, ...)</code></li>
<li><strong>Result</strong>: Periods where dasha dates overlap with JUPITER aspect transit dates</li>
</ol>

<h3>8. Final Response Creation</h3>
<ul>
<li><strong>Function Called</strong>: <code>create_clean_dasha_response(...)</code></li>
<li><strong>Result</strong>: Comprehensive response with:</li>
<li>Filtered dasha periods with KOCHARAM transit details</li>
<li>Age filtering analysis</li>
<li>KOCHARAM summary with transit predictions</li>
<li>Success indicators and confidence levels</li>
</ol>

<h2>Key Algorithms Used</h2>

<h3>1. Age Calculation Algorithm</h3>
<pre><code>def calculate_age_during_period(birth_date, period_start_date):
    age_at_start = relativedelta(period_start_date, birth_date).years
    return age_at_start</code></pre>

<h3>2. Aspect Angle Calculation</h3>
<pre><code>def calculate_aspect_angle(aspect_number, current_longitude):
    return ((aspect_number - 1) * 30 + current_longitude) % 360</code></pre>

<h3>3. Transit Time Calculation</h3>
<pre><code>def calculate_transit_time(rotation_period, angular_distance):
    return rotation_period * (angular_distance / 360.0)</code></pre>

<h3>4. Set Theory Operations for Logical Operators</h3>
<ul>
<li><strong>OR Logic</strong>: Union of all transit dates from any condition</li>
<li><strong>AND Logic</strong>: Intersection of transit dates from all conditions</li>
<li><strong>NOT Logic</strong>: Exclusion of transit dates from specified condition</li>
</ol>

<h2>Performance Optimizations Applied</h2>

<ol>
<li><strong>Chart Caching</strong>: Reuse generated charts for same date/location</li>
<li><strong>Degree-Based Calculations</strong>: Mathematical approach instead of iterative search</li>
<li><strong>Lazy Loading</strong>: Load data only when needed</li>
<li><strong>Batch Processing</strong>: Group multiple calculations together</li>
<li><strong>Memory Management</strong>: Automatic cleanup of temporary objects</li>
</ol>

<p>This walkthrough demonstrates the sophisticated processing capabilities of the Fortune Lens rule engine, showing how complex astrological queries are parsed, evaluated, and optimized for accurate results.</p>
    
    <div class="print-footer">
        <p>Fortune Lens Rule Engine - Technical Documentation</p>
        <p>For best printing results: Use Chrome/Safari → Print → More Settings → Paper Size: A4 → Margins: Default</p>
    </div>
</body>
</html>