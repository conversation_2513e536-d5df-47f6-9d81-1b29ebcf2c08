# Astro Insights Pro - Deployment Guide

**Professional Deployment Instructions for Production Environment**

## Overview

This guide provides comprehensive instructions for deploying Astro Insights Pro in production environments with proper security, scalability, and monitoring configurations.

## Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04 LTS or CentOS 8+
- **Python**: 3.8 or higher
- **MongoDB**: 4.4 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: Minimum 20GB SSD
- **Network**: HTTPS-capable domain

### Dependencies
- **Web Server**: Nginx (recommended)
- **WSGI Server**: Gunicorn
- **Process Manager**: Supervisor or systemd
- **SSL**: Let's Encrypt or commercial certificate

## Production Deployment

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3.8 python3.8-venv python3.8-dev -y
sudo apt install nginx supervisor mongodb -y

# Create application user
sudo useradd -m -s /bin/bash astroinsights
sudo usermod -aG sudo astroinsights
```

### 2. Application Deployment

```bash
# Switch to application user
sudo su - astroinsights

# Clone application
git clone <repository-url> /home/<USER>/astro-insights-pro
cd /home/<USER>/astro-insights-pro

# Create virtual environment
python3.8 -m venv venv
source venv/bin/activate

# Install dependencies
cd astro_insights_pro
pip install -r requirements.txt
pip install gunicorn
```

### 3. Environment Configuration

```bash
# Create production environment file
cp ../config/.env.example .env

# Edit production settings
nano .env
```

**Production .env Configuration**:
```bash
# Application
APP_NAME=Astro Insights Pro
DEBUG=False
SECRET_KEY=your-super-secure-secret-key-here
PORT=5003

# Database
MONGODB_URI=mongodb://localhost:27017/astro_insights_pro_prod

# Security
JWT_SECRET_KEY=your-jwt-secret-key-here
BCRYPT_LOG_ROUNDS=12

# Performance
CACHE_TYPE=redis
CACHE_REDIS_URL=redis://localhost:6379/0

# Monitoring
LOG_LEVEL=INFO
ENABLE_METRICS=True
```

### 4. Database Setup

```bash
# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Create database and user
mongo
> use astro_insights_pro_prod
> db.createUser({
    user: "astroinsights",
    pwd: "secure-password-here",
    roles: ["readWrite"]
  })
> exit
```

### 5. Gunicorn Configuration

Create `/home/<USER>/astro-insights-pro/gunicorn.conf.py`:

```python
# Gunicorn configuration for Astro Insights Pro

# Server socket
bind = "127.0.0.1:5003"
backlog = 2048

# Worker processes
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers
max_requests = 1000
max_requests_jitter = 50
preload_app = True

# Logging
accesslog = "/var/log/astroinsights/access.log"
errorlog = "/var/log/astroinsights/error.log"
loglevel = "info"

# Process naming
proc_name = "astro-insights-pro"

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190
```

### 6. Supervisor Configuration

Create `/etc/supervisor/conf.d/astroinsights.conf`:

```ini
[program:astroinsights]
command=/home/<USER>/astro-insights-pro/venv/bin/gunicorn -c gunicorn.conf.py "app:create_app('production')"
directory=/home/<USER>/astro-insights-pro/astro_insights_pro
user=astroinsights
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/astroinsights/supervisor.log
environment=PATH="/home/<USER>/astro-insights-pro/venv/bin"
```

### 7. Nginx Configuration

Create `/etc/nginx/sites-available/astroinsights`:

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Application proxy
    location / {
        proxy_pass http://127.0.0.1:5003;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Static files (if any)
    location /static/ {
        alias /home/<USER>/astro-insights-pro/astro_insights_pro/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Logs
    access_log /var/log/nginx/astroinsights_access.log;
    error_log /var/log/nginx/astroinsights_error.log;
}
```

### 8. SSL Certificate

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 9. Start Services

```bash
# Create log directories
sudo mkdir -p /var/log/astroinsights
sudo chown astroinsights:astroinsights /var/log/astroinsights

# Start services
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start astroinsights

# Enable Nginx site
sudo ln -s /etc/nginx/sites-available/astroinsights /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## Monitoring and Maintenance

### Health Checks
```bash
# Application health
curl https://your-domain.com/

# Service status
sudo supervisorctl status astroinsights
sudo systemctl status nginx
sudo systemctl status mongod
```

### Log Monitoring
```bash
# Application logs
tail -f /var/log/astroinsights/error.log

# Nginx logs
tail -f /var/log/nginx/astroinsights_access.log

# System logs
journalctl -u supervisor -f
```

### Backup Strategy
```bash
# Database backup
mongodump --db astro_insights_pro_prod --out /backup/mongodb/$(date +%Y%m%d)

# Application backup
tar -czf /backup/app/astro-insights-pro-$(date +%Y%m%d).tar.gz /home/<USER>/astro-insights-pro
```

## Security Checklist

- [ ] SSL certificate installed and configured
- [ ] Firewall configured (ports 80, 443, 22 only)
- [ ] MongoDB authentication enabled
- [ ] Strong passwords for all accounts
- [ ] Regular security updates scheduled
- [ ] Log monitoring configured
- [ ] Backup strategy implemented
- [ ] Rate limiting configured
- [ ] Security headers configured

## Performance Optimization

### Database Indexing
```javascript
// MongoDB indexes for performance
db.user_profile.createIndex({"email": 1})
db.member_profile.createIndex({"user_profile_id": 1})
db.user_member_astro_profile_data.createIndex({"member_profile_id": 1})
```

### Caching
```bash
# Install Redis for caching
sudo apt install redis-server -y
sudo systemctl enable redis-server
```

## Troubleshooting

### Common Issues
1. **Application won't start**: Check logs in `/var/log/astroinsights/`
2. **Database connection failed**: Verify MongoDB service and credentials
3. **SSL certificate issues**: Check Certbot logs and renewal
4. **Performance issues**: Monitor resource usage and optimize queries

### Support
- **Documentation**: `/docs/` directory
- **Logs**: `/var/log/astroinsights/`
- **Configuration**: Check `.env` and config files

---

**Astro Insights Pro** - Professional deployment for enterprise environments
